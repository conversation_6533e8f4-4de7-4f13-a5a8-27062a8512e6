# SpigaMonde Integration Points Mapping

This document maps the key integration points in the SpigaMonde system and their relationships to other components.

## Integration Points

### 1. Database Connection
- **File Location**: `spigamonde/database/connection.py`
- **Description**: Provides database connection and session management
- **Key Components**:
  - DatabaseManager class with methods for initialization, table creation, and session management
  - Global db_manager instance and helper functions like `get_session()` and `session_scope()`

### 2. Settings Manager
- **File Location**: `spigamonde/config/settings.py`
- **Description**: Manages application configuration using pydantic-settings
- **Key Components**:
  - Settings for database, spider, logging, storage, and monitoring
  - Environment variable support
  - Global settings instance and `get_settings()` function

### 3. Structured Logger
- **File Location**: `spigamonde/monitoring/logger.py`
- **Description**: Provides enhanced structured logging capabilities
- **Key Components**:
  - Loguru integration for logging implementation
  - Correlation tracking with context variables
  - Performance logging and structured JSON formatting
  - Global structured_logger instance and `setup_logging()` function

### 4. Command Line Interface
- **File Location**: `spigamonde/cli.py`
- **Description**: Provides command-line interface for SpigaMonde
- **Key Components**:
  - Click framework implementation
  - Commands for database initialization, crawling, content listing, monitoring, and statistics
  - Integration with all other components
  - Main entry point for the application

## Relationships

### Dependencies
- DatabaseConnection → SettingsManager (depends on)
- StructuredLogger → SettingsManager (depends on)

### Usage Relationships
- CommandLineInterface → DatabaseConnection (uses)
- CommandLineInterface → SettingsManager (uses)
- CommandLineInterface → StructuredLogger (uses)
- web/backend → DatabaseConnection (uses)
- web/backend → SettingsManager (uses)

### Ownership
- All integration points belong to the spigamonde module
- CommandLineInterface → spigamonde (belongs to)
- DatabaseConnection → spigamonde (belongs to)
- SettingsManager → spigamonde (belongs to)
- StructuredLogger → spigamonde (belongs to)

## System Integration

The integration points connect the SpigaMonde core components with the web backend:

1. **Database Access**: Backend imports `spigamonde.database.connection`
2. **Settings Access**: Backend imports `spigamonde.config.settings`
3. **Logging Integration**: Backend uses `spigamonde.monitoring.logger`
4. **CLI Integration**: Subprocess calls to `spigamonde.cli`

These integration points ensure consistent access to core functionality across both the CLI and web interfaces, maintaining a unified architecture.