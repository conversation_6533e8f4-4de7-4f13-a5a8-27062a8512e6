/**
 * System Module
 * System management and configuration
 */

// DOM Elements
let resetSystemBtn, clearDatabase, clearDownloads, clearLogs;
let resetResult, resetSummary;

/**
 * Initialize system module
 */
async function initSystem() {
    // Get DOM elements
    resetSystemBtn = document.getElementById('resetSystemBtn');
    clearDatabase = document.getElementById('clearDatabase');
    clearDownloads = document.getElementById('clearDownloads');
    clearLogs = document.getElementById('clearLogs');
    resetResult = document.getElementById('resetResult');
    resetSummary = document.getElementById('resetSummary');
    
    // Add event listeners
    resetSystemBtn.addEventListener('click', resetSystem);
    
    console.log('System module initialized');
}

/**
 * Reset system
 */
async function resetSystem() {
    // Confirmation dialog
    const confirmMessage = `Are you sure you want to reset the system?\n\nThis will:\n${clearDatabase.checked ? '• Clear all database content\n' : ''}${clearDownloads.checked ? '• Clear download directories\n' : ''}${clearLogs.checked ? '• Clear log files\n' : ''}\nThis action cannot be undone.`;
    
    if (!confirm(confirmMessage)) {
        logMessage('System reset cancelled by user', 'info');
        return;
    }
    
    logMessage('Starting system reset...', 'info');
    
    // Update button state
    setButtonLoading(resetSystemBtn, true);
    hideResult(resetResult);
    
    try {
        const params = new URLSearchParams({
            clear_database: clearDatabase.checked,
            clear_downloads: clearDownloads.checked,
            clear_logs: clearLogs.checked
        });
        
        const response = await fetch(`${API_BASE_URL}/api/reset-system?${params}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Display success result
        showResetResult(resetResult, data, false);
        logMessage('System reset completed', 'success');
        
        // Log individual operations
        for (const [operation, result] of Object.entries(data.operations || {})) {
            if (result.status === 'success') {
                logMessage(`${operation}: ${result.message}`, 'success');
            } else {
                logMessage(`${operation}: ${result.message}`, 'error');
            }
        }
        
        // Refresh other data after reset
        setTimeout(() => {
            updateModeIndicator();
            checkBackendStatus();
            
            // Refresh analytics if available
            if (window.refreshDetailedStats) {
                window.refreshDetailedStats();
            }
        }, 1000);
        
    } catch (error) {
        // Display error result
        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/reset-system'
        };
        
        showResetResult(resetResult, errorData, true);
        logMessage(`System reset failed: ${error.message}`, 'error');
        
    } finally {
        setButtonLoading(resetSystemBtn, false);
    }
}

/**
 * Show reset result
 */
function showResetResult(resultElement, data, isError = false) {
    // Update styling
    if (isError) {
        resultElement.classList.add('error');
        
        // Show simple error for reset failures
        resetSummary.innerHTML = `
            <div class="reset-item">
                <span class="reset-label">Status:</span>
                <span class="reset-value error">Error</span>
            </div>
            <div class="reset-item">
                <span class="reset-label">Error:</span>
                <span class="reset-value error">${data.error || 'Unknown error'}</span>
            </div>
        `;
        
    } else {
        resultElement.classList.remove('error');
        
        // Create summary display
        let summaryHtml = `
            <div class="reset-item">
                <span class="reset-label">Overall Status:</span>
                <span class="reset-value ${data.status === 'success' ? 'success' : ''}">${data.status.toUpperCase()}</span>
            </div>
        `;
        
        // Add operation results
        for (const [operation, result] of Object.entries(data.operations || {})) {
            const statusClass = result.status === 'success' ? 'success' : 'error';
            summaryHtml += `
                <div class="reset-item">
                    <span class="reset-label">${operation.charAt(0).toUpperCase() + operation.slice(1)}:</span>
                    <span class="reset-value ${statusClass}">${result.message}</span>
                </div>
            `;
        }
        
        resetSummary.innerHTML = summaryHtml;
    }
    
    // Show the result
    resultElement.style.display = 'block';
}

// Export functions
window.initSystem = initSystem;
window.resetSystem = resetSystem;
