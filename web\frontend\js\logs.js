/**
 * Logs Module
 * Activity logging and monitoring
 */

// DOM Elements
let clearLogBtn, activityLog;

/**
 * Initialize logs module
 */
async function initLogs() {
    // Get DOM elements
    clearLogBtn = document.getElementById('clearLogBtn');
    activityLog = document.getElementById('activityLog');
    
    // Add event listeners
    clearLogBtn.addEventListener('click', clearActivityLog);
    
    console.log('Logs module initialized');
}

/**
 * Clear activity log
 */
function clearActivityLog() {
    if (activityLog) {
        activityLog.innerHTML = '';
        logMessage('Activity log cleared', 'info');
    }
}

// Export functions
window.initLogs = initLogs;
window.clearActivityLog = clearActivityLog;
