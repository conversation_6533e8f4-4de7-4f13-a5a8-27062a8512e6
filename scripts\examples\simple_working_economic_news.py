#!/usr/bin/env python3
"""
Simple Working Economic News Aggregator
Minimal, reliable approach to get economic news working.

Usage:
    python simple_working_economic_news.py
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content
from rich.console import Console
from rich.panel import Panel


def main():
    """Execute simple economic news aggregation."""
    console = Console()
    
    console.print(Panel.fit(
        "[bold green]📈 Simple Economic News Aggregator[/bold green]\n"
        "Minimal approach to get economic news working",
        border_style="green"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Use only the most reliable sources that we know work
    reliable_sources = [
        "https://feeds.bbci.co.uk/news/business/rss.xml",  # BBC Business - very reliable
    ]
    
    console.print(f"[cyan]Testing with {len(reliable_sources)} reliable source(s)...[/cyan]")
    
    # Very simple crawl configuration
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Simple Economic News 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 2.0,  # Slower to be more respectful
        'CONCURRENT_REQUESTS': 1,  # Single request at a time
        'DEPTH_LIMIT': 1,
        'CLOSESPIDER_PAGECOUNT': 10,  # Very limited for testing
        'LOG_LEVEL': 'INFO',
        'ALLOWED_FILE_TYPES': ['xml', 'rss'],
    }
    
    # Execute crawl
    try:
        console.print("[cyan]Starting crawl...[/cyan]")
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=reliable_sources)
        process.start()
        
        console.print("[green]✓ Crawl completed![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Check what we got
    console.print("\n[bold cyan]Checking crawled content...[/bold cyan]")
    check_crawled_content(console)
    
    return 0


def check_crawled_content(console):
    """Check what content was actually crawled."""
    
    with session_scope() as session:
        # Get the most recent content
        recent_content = session.query(Content).order_by(Content.created_at.desc()).limit(20).all()
        
        console.print(f"Found {len(recent_content)} recent items:")
        
        economic_headlines = []
        
        for content in recent_content:
            console.print(f"  📄 {content.filename} - {content.url}")
            
            # Check if we have local content
            if content.local_path and os.path.exists(content.local_path):
                try:
                    with open(content.local_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content_text = f.read()
                    
                    # Check if it's RSS content
                    if is_rss_content(content_text):
                        console.print(f"    [green]✓ RSS content found[/green]")
                        headlines = extract_headlines_from_rss(content_text, content.url)
                        
                        # Filter for economic content
                        for headline in headlines:
                            if is_economic_content(headline['title'], headline.get('description', '')):
                                economic_headlines.append(headline)
                        
                        console.print(f"    [cyan]Found {len(headlines)} total headlines, {len([h for h in headlines if is_economic_content(h['title'], h.get('description', ''))])} economic[/cyan]")
                    else:
                        console.print(f"    [yellow]Not RSS content[/yellow]")
                        
                except Exception as e:
                    console.print(f"    [red]Error reading: {e}[/red]")
        
        # Generate simple report
        if economic_headlines:
            console.print(f"\n[bold green]Found {len(economic_headlines)} economic headlines![/bold green]")
            
            # Show first few headlines
            for i, headline in enumerate(economic_headlines[:5]):
                console.print(f"{i+1}. {headline['title']}")
            
            # Generate simple HTML
            html_file = generate_simple_html(economic_headlines)
            console.print(f"\n[green]✓ HTML report generated: {html_file}[/green]")
            console.print(f"[cyan]Open in browser: file://{html_file.absolute()}[/cyan]")
        else:
            console.print("[yellow]No economic headlines found[/yellow]")


def is_rss_content(content):
    """Check if content is RSS."""
    return ('<rss' in content.lower() or 
            '<feed' in content.lower() or 
            '<?xml' in content.lower())


def extract_headlines_from_rss(rss_content, url):
    """Extract headlines from RSS content."""
    headlines = []
    
    # Find RSS items
    items = re.findall(r'<item[^>]*>(.*?)</item>', rss_content, re.DOTALL | re.IGNORECASE)
    
    for item in items:
        try:
            # Extract title
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', item, re.IGNORECASE)
            if not title_match:
                continue
            
            title = clean_text(title_match.group(1))
            
            # Extract description
            desc_match = re.search(r'<description[^>]*>([^<]+)</description>', item, re.IGNORECASE)
            description = clean_text(desc_match.group(1)) if desc_match else ""
            
            # Extract link
            link_match = re.search(r'<link[^>]*>([^<]+)</link>', item, re.IGNORECASE)
            link = link_match.group(1) if link_match else url
            
            if title and len(title) > 10:
                headlines.append({
                    'title': title,
                    'description': description,
                    'link': link,
                    'source': get_source_name(url)
                })
                
        except Exception:
            continue  # Skip malformed items
    
    return headlines


def is_economic_content(title, description):
    """Check if content is economic/business related."""
    economic_keywords = [
        'economy', 'economic', 'market', 'trade', 'business', 'financial', 'finance',
        'bank', 'banking', 'investment', 'stock', 'share', 'currency', 'dollar',
        'growth', 'gdp', 'inflation', 'recession', 'recovery', 'profit', 'revenue',
        'company', 'corporate', 'industry', 'sector', 'commodity', 'oil', 'gold'
    ]
    
    # Also include Asia/Latin America keywords
    regional_keywords = [
        'asia', 'asian', 'china', 'japan', 'korea', 'india', 'singapore',
        'latin america', 'brazil', 'mexico', 'argentina', 'chile'
    ]
    
    combined_text = f"{title} {description}".lower()
    
    # Check for economic keywords
    has_economic = any(keyword in combined_text for keyword in economic_keywords)
    
    # Check for regional keywords
    has_regional = any(keyword in combined_text for keyword in regional_keywords)
    
    return has_economic or has_regional


def get_source_name(url):
    """Get friendly source name."""
    domain = urlparse(url).netloc.lower()
    
    if 'bbc' in domain:
        return 'BBC Business'
    elif 'nikkei' in domain:
        return 'Nikkei Asia'
    elif 'scmp' in domain:
        return 'South China Morning Post'
    else:
        return domain.replace('www.', '').title()


def clean_text(text):
    """Clean HTML and entities from text."""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Decode common HTML entities
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    text = text.replace('&#39;', "'")
    text = text.replace('&nbsp;', ' ')
    
    return text.strip()


def generate_simple_html(headlines):
    """Generate simple HTML report."""
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Economic News Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: #28a745;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 20px;
        }}
        .headline {{
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }}
        .headline-title {{
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .headline-description {{
            color: #666;
            font-size: 0.9em;
        }}
        .source {{
            color: #28a745;
            font-size: 0.8em;
            margin-top: 5px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 Economic News Report</h1>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Total Headlines: {len(headlines)}</p>
    </div>
"""
    
    for headline in headlines:
        title = headline.get('title', 'No title')
        description = headline.get('description', '')
        source = headline.get('source', 'Unknown')
        link = headline.get('link', '#')
        
        html_content += f"""
    <div class="headline">
        <div class="headline-title">
            <a href="{link}" target="_blank" style="text-decoration: none; color: inherit;">
                {title}
            </a>
        </div>
        {f'<div class="headline-description">{description}</div>' if description else ''}
        <div class="source">Source: {source}</div>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    # Write HTML file
    output_dir = Path(__file__).parent / 'output'
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    html_file = output_dir / f'simple_economic_news_{timestamp}.html'
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_file


if __name__ == "__main__":
    sys.exit(main())
