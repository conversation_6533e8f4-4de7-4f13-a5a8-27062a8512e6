"""Basic tests for SpigaMonde functionality."""

import pytest
from pathlib import Path

from spigamonde.config.settings import get_settings
from spigamonde.utils.file_utils import FileTypeDetector, calculate_url_hash
from spigamonde.models.content import ContentType


def test_settings_loading():
    """Test that settings load correctly."""
    settings = get_settings()
    assert settings.app_name == "SpigaMonde"
    assert settings.version == "0.1.0"
    assert isinstance(settings.spider.allowed_file_types, list)
    assert len(settings.spider.allowed_file_types) > 0


def test_file_type_detector():
    """Test file type detection functionality."""
    detector = FileTypeDetector()
    
    # Test PDF detection
    content_type, file_info = detector.detect_content_type("https://example.com/document.pdf")
    assert content_type == ContentType.DOCUMENT
    assert file_info['extension'] == 'pdf'
    assert file_info['filename'] == 'document.pdf'
    
    # Test image detection
    content_type, file_info = detector.detect_content_type("https://example.com/image.jpg")
    assert content_type == ContentType.IMAGE
    assert file_info['extension'] == 'jpg'
    
    # Test unknown file
    content_type, file_info = detector.detect_content_type("https://example.com/unknown.xyz")
    assert content_type == ContentType.OTHER


def test_url_hash():
    """Test URL hashing functionality."""
    url1 = "https://example.com/test.pdf"
    url2 = "https://example.com/test.pdf"
    url3 = "https://example.com/different.pdf"
    
    hash1 = calculate_url_hash(url1)
    hash2 = calculate_url_hash(url2)
    hash3 = calculate_url_hash(url3)
    
    assert hash1 == hash2  # Same URLs should have same hash
    assert hash1 != hash3  # Different URLs should have different hashes
    assert len(hash1) == 64  # SHA-256 produces 64-character hex string


def test_allowed_file_types():
    """Test file type filtering."""
    detector = FileTypeDetector()
    
    # Test allowed types
    assert detector.is_allowed_file_type('pdf')
    assert detector.is_allowed_file_type('jpg')
    assert detector.is_allowed_file_type('mp4')
    
    # Test disallowed types
    assert not detector.is_allowed_file_type('exe')
    assert not detector.is_allowed_file_type('unknown')
    assert not detector.is_allowed_file_type('')


def test_storage_path_generation():
    """Test storage path generation."""
    from spigamonde.utils.file_utils import get_storage_path
    
    url = "https://example.com/test.pdf"
    filename = "test.pdf"
    content_type = ContentType.DOCUMENT
    
    path = get_storage_path(url, filename, content_type)
    assert isinstance(path, Path)
    assert path.name == "test.pdf"


if __name__ == "__main__":
    pytest.main([__file__])
