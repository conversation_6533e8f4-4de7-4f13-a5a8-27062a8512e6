# SpigaMonde Script Library

**Custom crawl scripts for specialized use cases**

## 📁 Directory Structure

```
scripts/
├── examples/          # Example scripts demonstrating common patterns
├── templates/         # Script templates for quick customization
├── user_scripts/      # Your custom scripts go here
└── README.md         # This file
```

## 🚀 Quick Start

### 1. Browse Examples
Check out the `examples/` folder for ready-to-use scripts:
- `crawl_academic_papers.py` - Research paper discovery and analysis
- `monitor_news_sites.py` - Real-time news monitoring
- `extract_documentation.py` - Technical documentation harvesting
- `analyze_social_content.py` - Social media content analysis

### 2. Use Templates
Start with a template from `templates/` folder:
- `basic_crawl_template.py` - Simple crawling script
- `analysis_focused_template.py` - Content analysis emphasis
- `monitoring_template.py` - Real-time monitoring integration

### 3. Create Custom Scripts
Place your custom scripts in `user_scripts/` folder.

## 📋 Script Categories

### 🔬 **Research & Academic**
- Academic paper crawling
- Citation network analysis
- Research trend monitoring
- Conference proceeding extraction

### 📰 **News & Media**
- Breaking news monitoring
- Media sentiment analysis
- RSS feed aggregation
- Social media tracking

### 📚 **Documentation & Knowledge**
- API documentation harvesting
- Tutorial and guide collection
- Knowledge base building
- Technical reference extraction

### 🛒 **E-commerce & Business**
- Product catalog crawling
- Price monitoring
- Review and rating analysis
- Competitor intelligence

### 🌐 **Web Intelligence**
- Domain analysis and mapping
- Link relationship discovery
- Content trend analysis
- SEO and marketing intelligence

## 🛠️ Creating Custom Scripts

### Using LLM Assistance
1. **Describe your needs** to an LLM with access to SpigaMonde
2. **Provide the LLM** with `docs/llm-crawl-script-guide.md`
3. **Specify requirements**:
   - Target websites or domains
   - Content types of interest
   - Analysis requirements
   - Output preferences
   - Performance constraints

### Example LLM Prompt
```
I need a SpigaMonde crawl script that:
- Crawls academic computer science papers from arxiv.org
- Focuses on machine learning and AI papers from the last 6 months
- Extracts author information, abstracts, and citations
- Classifies papers by subfield (NLP, computer vision, etc.)
- Exports results to a structured CSV file
- Runs with respectful delays and monitors progress

Please generate a complete Python script following the SpigaMonde patterns.
```

### Manual Creation
1. **Copy a template** from `templates/` folder
2. **Customize parameters** for your use case
3. **Add domain-specific logic** as needed
4. **Test with small scope** before full deployment
5. **Save to `user_scripts/`** folder

## 📊 Script Features

### Core Capabilities
- **Flexible crawling**: Custom depth, scope, and filtering
- **Intelligent analysis**: Content classification and quality assessment
- **Real-time monitoring**: Live progress dashboards
- **Database integration**: Persistent storage and querying
- **Export options**: CSV, JSON, custom formats
- **Error handling**: Robust error recovery and logging

### Advanced Features
- **Custom content analysis**: Domain-specific processing
- **Multi-threaded execution**: Parallel processing capabilities
- **Scheduled execution**: Automated recurring crawls
- **Alert integration**: Notifications and monitoring
- **Performance optimization**: Resource management and tuning

## 🎯 Best Practices

### Script Development
- **Start small**: Test with limited scope first
- **Use descriptive names**: Clear purpose and domain indication
- **Include documentation**: Comments and usage instructions
- **Handle errors gracefully**: Robust error recovery
- **Respect rate limits**: Ethical crawling practices

### Performance Optimization
- **Set appropriate delays**: Respectful server interaction
- **Limit concurrent requests**: Avoid overwhelming targets
- **Filter content types**: Focus on relevant content
- **Monitor resource usage**: Memory and disk management
- **Use incremental crawling**: Resume from previous sessions

### Data Management
- **Organize outputs**: Structured file and folder naming
- **Validate data quality**: Check analysis results
- **Backup important data**: Prevent data loss
- **Document data sources**: Track crawl origins
- **Respect privacy**: Handle sensitive content appropriately

## 🔧 Troubleshooting

### Common Issues
- **Import errors**: Ensure SpigaMonde is properly installed
- **Database issues**: Run `spiga init` before script execution
- **Permission errors**: Check file system permissions
- **Network timeouts**: Adjust delay and timeout settings
- **Memory issues**: Reduce concurrent requests or page limits

### Getting Help
- **Check examples**: Similar use cases in `examples/` folder
- **Review documentation**: `docs/` folder for detailed guides
- **Use LLM assistance**: Generate scripts with AI help
- **Test incrementally**: Start with small scope and expand

## 📈 Monitoring and Analytics

### Real-time Monitoring
Most scripts can integrate with SpigaMonde's monitoring dashboard:
```python
# Start monitoring in separate thread
import threading
dashboard_thread = threading.Thread(target=run_live_dashboard, args=(600,))
dashboard_thread.start()
```

### Performance Metrics
Track crawl performance and content analysis results:
- Pages crawled per minute
- Content analysis accuracy
- Download success rates
- Error rates and types
- Resource utilization

### Data Analysis
Query and analyze crawled content:
```python
# Example: Find high-quality academic papers
with session_scope() as session:
    papers = session.query(Content).join(ContentAnalysis).filter(
        ContentAnalysis.category == 'ACADEMIC_PAPER',
        ContentAnalysis.quality_score == 'EXCELLENT'
    ).all()
```

---

**Ready to create powerful, custom crawling solutions with SpigaMonde!** 🚀

For detailed guidance on script creation, see `docs/llm-crawl-script-guide.md`.
