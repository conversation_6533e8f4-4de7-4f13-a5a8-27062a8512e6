#!/usr/bin/env python3
"""
News Site Monitor for SpigaMonde
Real-time monitoring of news websites for breaking news and trending topics.

Usage:
    python monitor_news_sites.py
    
Features:
    - Monitors major news websites
    - Focuses on recent articles and breaking news
    - Real-time content analysis and classification
    - Sentiment analysis and topic detection
    - Live monitoring dashboard
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import threading
import time

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.dashboard import run_live_dashboard
from spigamonde.monitoring.metrics import get_metrics_collector
from rich.console import Console
from rich.panel import Panel
from rich.live import Live
from rich.table import Table


def main():
    """Execute news monitoring with real-time dashboard."""
    console = Console()
    
    # Display banner
    console.print(Panel.fit(
        "[bold red]SpigaMonde News Monitor[/bold red]\n"
        "Real-time news discovery and analysis",
        border_style="red"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # News sources (use RSS feeds and news homepages)
    news_sources = [
        "https://feeds.bbci.co.uk/news/rss.xml",  # BBC News RSS
        "https://rss.cnn.com/rss/edition.rss",    # CNN RSS
        "https://feeds.reuters.com/reuters/topNews",  # Reuters
        "https://www.npr.org/rss/rss.php?id=1001",   # NPR
        "https://techcrunch.com/feed/",           # Tech news
    ]
    
    # Configure for news monitoring
    crawl_config = {
        'USER_AGENT': 'SpigaMonde News Monitor 1.0 (+https://github.com/spigamonde)',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.0,  # Faster for news monitoring
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS': 3,  # More aggressive for news
        'DEPTH_LIMIT': 2,  # RSS feeds + article pages
        'CLOSESPIDER_PAGECOUNT': 200,  # More articles for news monitoring
        'LOG_LEVEL': 'INFO',
        
        # Focus on recent content
        'ALLOWED_FILE_TYPES': ['html', 'xml', 'rss'],
        'MAX_FILE_SIZE': 5 * 1024 * 1024,  # 5MB for articles
    }
    
    console.print(f"[cyan]Monitoring {len(news_sources)} news sources...[/cyan]")
    console.print("[yellow]Starting real-time monitoring dashboard...[/yellow]")
    
    # Start monitoring dashboard
    dashboard_thread = threading.Thread(
        target=run_live_dashboard,
        args=(600,),  # 10 minutes of monitoring
        daemon=True
    )
    dashboard_thread.start()
    
    # Start news analysis display
    analysis_thread = threading.Thread(
        target=live_news_analysis,
        args=(console,),
        daemon=True
    )
    analysis_thread.start()
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=news_sources)
        process.start()
        
        console.print("[green]✓ News monitoring completed![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Monitoring error: {e}[/red]")
        return 1
    
    # Final analysis
    console.print("\n[bold cyan]Final news analysis...[/bold cyan]")
    analyze_news_results(console)
    
    return 0


def live_news_analysis(console):
    """Display live news analysis results."""
    
    def generate_news_table():
        """Generate current news analysis table."""
        table = Table(title="Latest News Analysis", show_header=True, header_style="bold red")
        table.add_column("Time", style="cyan", width=8)
        table.add_column("Headline", style="white", width=50)
        table.add_column("Category", style="green", width=15)
        table.add_column("Quality", style="yellow", width=10)
        
        with session_scope() as session:
            # Get recent news articles
            recent_news = session.query(Content).join(ContentAnalysis).filter(
                ContentAnalysis.category.in_(['NEWS_ARTICLE', 'BLOG_POST'])
            ).order_by(Content.created_at.desc()).limit(10).all()
            
            for article in recent_news:
                analysis = article.content_analysis[0] if article.content_analysis else None
                if analysis:
                    time_str = article.created_at.strftime("%H:%M")
                    headline = article.filename[:47] + "..." if len(article.filename) > 50 else article.filename
                    category = analysis.category.replace('_', ' ').title()
                    quality = analysis.quality_score
                    
                    table.add_row(time_str, headline, category, quality)
        
        return table
    
    # Live display for 5 minutes
    with Live(generate_news_table(), refresh_per_second=0.5) as live:
        for _ in range(300):  # 5 minutes
            time.sleep(1)
            live.update(generate_news_table())


def analyze_news_results(console):
    """Analyze and display news monitoring results."""
    
    with session_scope() as session:
        # Query news articles
        news_articles = session.query(Content).join(ContentAnalysis).filter(
            ContentAnalysis.category.in_(['NEWS_ARTICLE', 'BLOG_POST'])
        ).all()
        
        if not news_articles:
            console.print("[yellow]No news articles found in this monitoring session.[/yellow]")
            return
        
        # Analyze by source
        source_stats = {}
        total_articles = len(news_articles)
        
        for article in news_articles:
            domain = article.url.split('/')[2] if '/' in article.url else 'unknown'
            if domain not in source_stats:
                source_stats[domain] = {'count': 0, 'quality': []}
            
            source_stats[domain]['count'] += 1
            if article.content_analysis:
                quality = article.content_analysis[0].quality_score
                source_stats[domain]['quality'].append(quality)
        
        # Display source analysis
        table = Table(title="News Sources Analysis", show_header=True, header_style="bold red")
        table.add_column("Source", style="cyan")
        table.add_column("Articles", style="blue")
        table.add_column("Avg Quality", style="green")
        table.add_column("Coverage", style="yellow")
        
        for source, stats in sorted(source_stats.items(), key=lambda x: x[1]['count'], reverse=True):
            avg_quality = "N/A"
            if stats['quality']:
                quality_scores = {'EXCELLENT': 5, 'GOOD': 4, 'FAIR': 3, 'POOR': 2}
                avg_score = sum(quality_scores.get(q, 1) for q in stats['quality']) / len(stats['quality'])
                avg_quality = f"{avg_score:.1f}/5"
            
            coverage = f"{stats['count']/total_articles*100:.1f}%"
            
            table.add_row(source, str(stats['count']), avg_quality, coverage)
        
        console.print(table)
        
        # Summary
        console.print(f"\n[bold green]Monitoring Summary:[/bold green]")
        console.print(f"📰 Total articles monitored: {total_articles}")
        console.print(f"🌐 News sources covered: {len(source_stats)}")
        console.print(f"⏱️  Monitoring duration: Real-time")
        
        # Recent headlines
        console.print(f"\n[bold cyan]Recent Headlines:[/bold cyan]")
        recent = sorted(news_articles, key=lambda x: x.created_at, reverse=True)[:5]
        for i, article in enumerate(recent, 1):
            headline = article.filename[:70] + "..." if len(article.filename) > 70 else article.filename
            console.print(f"{i}. {headline}")


if __name__ == "__main__":
    sys.exit(main())
