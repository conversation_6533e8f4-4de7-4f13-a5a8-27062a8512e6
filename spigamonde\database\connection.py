"""Database connection and session management."""

import os
from contextlib import contextmanager
from typing import Generator

from loguru import logger
from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session, sessionmaker

from ..config.settings import get_settings
from ..models.base import Base
# Import models to ensure they're registered with Base
from ..models import content  # noqa: F401


class DatabaseManager:
    """Manages database connections and sessions."""
    
    def __init__(self):
        self.engine: Engine = None
        self.SessionLocal: sessionmaker = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize the database connection."""
        if self._initialized:
            return
        
        settings = get_settings()
        
        # Create engine with appropriate settings
        engine_kwargs = {
            "echo": settings.database.echo,
        }
        
        # Add connection pool settings for non-SQLite databases
        if not settings.database.url.startswith("sqlite"):
            engine_kwargs.update({
                "pool_size": settings.database.pool_size,
                "max_overflow": settings.database.max_overflow,
            })
        
        self.engine = create_engine(settings.database.url, **engine_kwargs)
        
        # Enable foreign key constraints for SQLite
        if settings.database.url.startswith("sqlite"):
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.close()
        
        # Create session factory
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        self._initialized = True
        logger.info(f"Database initialized with URL: {settings.database.url}")
    
    def create_tables(self) -> None:
        """Create all database tables."""
        if not self._initialized:
            self.initialize()
        
        Base.metadata.create_all(bind=self.engine)
        logger.info("Database tables created successfully")
    
    def drop_tables(self) -> None:
        """Drop all database tables."""
        if not self._initialized:
            self.initialize()
        
        Base.metadata.drop_all(bind=self.engine)
        logger.warning("All database tables dropped")
    
    def get_session(self) -> Session:
        """Get a new database session."""
        if not self._initialized:
            self.initialize()
        
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """Provide a transactional scope around a series of operations."""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def close(self) -> None:
        """Close the database connection."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connection closed")


# Global database manager instance
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    return db_manager


def get_session() -> Session:
    """Get a new database session."""
    return db_manager.get_session()


@contextmanager
def session_scope() -> Generator[Session, None, None]:
    """Provide a transactional scope around a series of operations."""
    with db_manager.session_scope() as session:
        yield session


def init_database() -> None:
    """Initialize the database and create tables."""
    db_manager.initialize()
    db_manager.create_tables()


def reset_database() -> None:
    """Reset the database by dropping and recreating all tables."""
    db_manager.drop_tables()
    db_manager.create_tables()
    logger.warning("Database has been reset")
