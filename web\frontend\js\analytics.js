/**
 * Analytics Module
 * Comprehensive statistics and monitoring
 */

// DOM Elements
let refreshStatsBtn, autoRefreshStats;
let statsWidget, statsError, statsErrorMessage;
let totalContent, analyzedContent, totalStorage, analysisProgress;
let recentActivity, contentBreakdown, topSources, recentContent;

// Auto-refresh timer
let autoRefreshTimer = null;

/**
 * Initialize analytics module
 */
async function initAnalytics() {
    // Get DOM elements
    refreshStatsBtn = document.getElementById('refreshStatsBtn');
    autoRefreshStats = document.getElementById('autoRefreshStats');
    statsWidget = document.getElementById('statsWidget');
    statsError = document.getElementById('statsError');
    statsErrorMessage = document.getElementById('statsErrorMessage');
    
    // Stats display elements
    totalContent = document.getElementById('totalContent');
    analyzedContent = document.getElementById('analyzedContent');
    totalStorage = document.getElementById('totalStorage');
    analysisProgress = document.getElementById('analysisProgress');
    recentActivity = document.getElementById('recentActivity');
    contentBreakdown = document.getElementById('contentBreakdown');
    topSources = document.getElementById('topSources');
    recentContent = document.getElementById('recentContent');
    
    // Add event listeners
    refreshStatsBtn.addEventListener('click', refreshDetailedStats);
    autoRefreshStats.addEventListener('change', toggleAutoRefresh);
    
    // Load initial stats
    refreshDetailedStats();
    
    console.log('Analytics module initialized');
}

/**
 * Refresh detailed stats
 */
async function refreshDetailedStats() {
    logMessage('Refreshing detailed statistics...', 'info');
    
    // Update button state
    setButtonLoading(refreshStatsBtn, true);
    hideStatsError();
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/stats/detailed`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Display stats
        displayDetailedStats(data);
        logMessage('Statistics refreshed successfully', 'success');
        
    } catch (error) {
        // Display error
        showStatsError(error.message);
        logMessage(`Stats refresh failed: ${error.message}`, 'error');
        
    } finally {
        setButtonLoading(refreshStatsBtn, false);
    }
}

/**
 * Display detailed stats
 */
function displayDetailedStats(data) {
    const overview = data.overview || {};
    const recentActivityData = data.recent_activity || {};
    const contentBreakdownData = data.content_breakdown || {};
    const topDomainsData = data.top_domains || [];
    const recentSamplesData = data.recent_samples || [];
    const storageMetrics = data.storage_metrics || {};
    
    // Update overview cards
    totalContent.textContent = overview.total_content || 0;
    analyzedContent.textContent = overview.analyzed_content || 0;
    totalStorage.textContent = `${storageMetrics.total_storage_mb || 0} MB`;
    analysisProgress.textContent = `${overview.analysis_coverage_pct || 0}%`;
    
    // Update recent activity
    updateRecentActivity(recentActivityData);
    
    // Update content breakdown
    updateContentBreakdown(contentBreakdownData, overview.total_content || 0);
    
    // Update top sources
    updateTopSources(topDomainsData);
    
    // Update recent content
    updateRecentContent(recentSamplesData);
    
    // Show stats widget
    statsWidget.style.display = 'block';
}

/**
 * Update recent activity display
 */
function updateRecentActivity(activityData) {
    const activities = [
        { label: 'Last Hour', key: 'last_hour' },
        { label: 'Last 24 Hours', key: 'last_24h' },
        { label: 'Last Week', key: 'last_week' },
        { label: 'Last Month', key: 'last_month' }
    ];
    
    recentActivity.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <span class="activity-label">${activity.label}</span>
            <span class="activity-count">${activityData[activity.key] || 0}</span>
        </div>
    `).join('');
}

/**
 * Update content breakdown display
 */
function updateContentBreakdown(breakdownData, totalCount) {
    const statusData = breakdownData.by_status || {};
    const typeData = breakdownData.by_type || {};
    
    // Combine status and type data
    const allBreakdowns = [
        ...Object.entries(statusData).map(([key, value]) => ({ 
            label: `${key} (status)`, 
            count: value, 
            percentage: totalCount > 0 ? (value / totalCount * 100) : 0 
        })),
        ...Object.entries(typeData).map(([key, value]) => ({ 
            label: `${key} (type)`, 
            count: value, 
            percentage: totalCount > 0 ? (value / totalCount * 100) : 0 
        }))
    ];
    
    // Sort by count descending
    allBreakdowns.sort((a, b) => b.count - a.count);
    
    contentBreakdown.innerHTML = allBreakdowns.slice(0, 8).map(item => `
        <div class="breakdown-item">
            <span class="breakdown-label">${item.label}</span>
            <div class="breakdown-bar">
                <div class="breakdown-fill" style="width: ${item.percentage}%"></div>
            </div>
            <span class="breakdown-count">${item.count}</span>
        </div>
    `).join('');
}

/**
 * Update top sources display
 */
function updateTopSources(sourcesData) {
    if (sourcesData.length === 0) {
        topSources.innerHTML = '<div class="source-item">No sources available</div>';
        return;
    }
    
    topSources.innerHTML = sourcesData.slice(0, 10).map(source => `
        <div class="source-item">
            <span class="source-domain">${source.domain || 'Unknown'}</span>
            <span class="source-count">${source.count}</span>
        </div>
    `).join('');
}

/**
 * Update recent content display
 */
function updateRecentContent(contentData) {
    if (contentData.length === 0) {
        recentContent.innerHTML = '<div class="content-item">No recent content</div>';
        return;
    }
    
    recentContent.innerHTML = contentData.slice(0, 10).map(item => `
        <div class="content-item">
            <div class="content-url">${item.url || 'Unknown URL'}</div>
            <div class="content-meta">
                <span class="content-type">${item.content_type || 'unknown'}</span>
                <span class="content-size">${item.file_size_mb || 0} MB</span>
                <span class="content-date">${item.created_at ? new Date(item.created_at).toLocaleDateString() : 'Unknown'}</span>
            </div>
        </div>
    `).join('');
}

/**
 * Show stats error
 */
function showStatsError(errorMessage) {
    statsErrorMessage.textContent = errorMessage;
    statsError.style.display = 'block';
    statsWidget.style.display = 'none';
}

/**
 * Hide stats error
 */
function hideStatsError() {
    statsError.style.display = 'none';
}

/**
 * Toggle auto-refresh
 */
function toggleAutoRefresh() {
    if (autoRefreshStats.checked) {
        // Start auto-refresh
        autoRefreshTimer = setInterval(refreshDetailedStats, 30000); // 30 seconds
        logMessage('Auto-refresh enabled (30s interval)', 'info');
    } else {
        // Stop auto-refresh
        if (autoRefreshTimer) {
            clearInterval(autoRefreshTimer);
            autoRefreshTimer = null;
        }
        logMessage('Auto-refresh disabled', 'info');
    }
}

// Export functions
window.initAnalytics = initAnalytics;
window.refreshDetailedStats = refreshDetailedStats;
