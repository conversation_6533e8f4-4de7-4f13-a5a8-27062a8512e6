"""Tests for the ContentSpider."""

import pytest
from scrapy.http import HtmlResponse, Request
from unittest.mock import Mock, patch

from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.models.content import ContentType


class TestContentSpider:
    """Test cases for ContentSpider."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.spider = ContentSpider()
        self.spider.start_urls = ['https://example.com']
    
    def create_response(self, url: str, html_content: str) -> HtmlResponse:
        """Create a mock HtmlResponse for testing."""
        request = Request(url=url)
        return HtmlResponse(
            url=url,
            request=request,
            body=html_content.encode('utf-8'),
            encoding='utf-8'
        )
    
    def test_spider_initialization(self):
        """Test spider initializes correctly."""
        assert self.spider.name == "content_spider"
        assert hasattr(self.spider, 'settings')
        assert hasattr(self.spider, 'file_detector')
    
    def test_should_crawl_url_valid_urls(self):
        """Test URL filtering for valid URLs."""
        valid_urls = [
            'https://example.com/page1',
            'http://test.com/documents',
            'https://site.org/files/document.pdf'
        ]
        
        for url in valid_urls:
            assert self.spider._should_crawl_url(url), f"Should crawl {url}"
    
    def test_should_crawl_url_invalid_urls(self):
        """Test URL filtering for invalid URLs."""
        invalid_urls = [
            'javascript:void(0)',
            'mailto:<EMAIL>',
            'tel:+1234567890',
            'ftp://files.example.com',
            'https://example.com/style.css',
            'https://example.com/script.js',
            'https://example.com/favicon.ico',
            'https://example.com/?logout=1'
        ]
        
        for url in invalid_urls:
            assert not self.spider._should_crawl_url(url), f"Should not crawl {url}"
    
    @patch('spigamonde.spiders.content_spider.session_scope')
    def test_parse_extracts_links(self, mock_session):
        """Test that parse method extracts links correctly."""
        html_content = """
        <html>
            <body>
                <a href="/page1">Page 1</a>
                <a href="https://example.com/page2">Page 2</a>
                <a href="document.pdf">PDF Document</a>
                <a href="image.jpg">Image</a>
                <a href="javascript:void(0)">Invalid Link</a>
            </body>
        </html>
        """
        
        response = self.create_response('https://example.com', html_content)
        response.meta['depth'] = 0
        
        # Mock database operations
        mock_session.return_value.__enter__.return_value = Mock()
        
        # Get all requests generated by parse
        requests = list(self.spider.parse(response))
        
        # Should generate requests for valid links and content
        assert len(requests) > 0
        
        # Check that we get both crawl requests and download requests
        crawl_requests = [r for r in requests if r.callback == self.spider.parse]
        download_requests = [r for r in requests if r.callback == self.spider._download_content]
        
        # Should have some crawl requests for pages
        assert len(crawl_requests) >= 0  # Might be 0 if no valid page links
        
        # Should have download requests for files
        assert len(download_requests) >= 0  # Might be 0 if no valid file links
    
    @patch('spigamonde.spiders.content_spider.session_scope')
    def test_extract_content_finds_files(self, mock_session):
        """Test content extraction finds downloadable files."""
        html_content = """
        <html>
            <body>
                <a href="document.pdf">PDF Document</a>
                <a href="image.jpg">JPEG Image</a>
                <a href="archive.zip">ZIP Archive</a>
                <a href="page.html">HTML Page</a>
                <a href="unknown.xyz">Unknown File</a>
            </body>
        </html>
        """
        
        response = self.create_response('https://example.com', html_content)
        
        # Mock database operations
        mock_session.return_value.__enter__.return_value = Mock()
        
        # Mock _save_content to return content IDs
        with patch.object(self.spider, '_save_content', return_value=1):
            requests = list(self.spider._extract_content(response))
        
        # Should find downloadable files
        assert len(requests) >= 3  # PDF, JPG, ZIP should be found
        
        # All requests should be download requests
        for request in requests:
            assert request.callback == self.spider._download_content
            assert 'content_id' in request.meta
            assert 'content_type' in request.meta
            assert 'file_info' in request.meta
    
    def test_file_type_detection_in_content_extraction(self):
        """Test that file types are detected correctly during content extraction."""
        test_cases = [
            ('https://example.com/doc.pdf', ContentType.DOCUMENT),
            ('https://example.com/pic.jpg', ContentType.IMAGE),
            ('https://example.com/vid.mp4', ContentType.VIDEO),
            ('https://example.com/song.mp3', ContentType.AUDIO),
            ('https://example.com/data.zip', ContentType.ARCHIVE),
        ]
        
        for url, expected_type in test_cases:
            content_type, file_info = self.spider.file_detector.detect_content_type(url)
            assert content_type == expected_type, f"Expected {expected_type} for {url}, got {content_type}"
    
    @patch('spigamonde.spiders.content_spider.session_scope')
    def test_download_content_success(self, mock_session):
        """Test successful content download."""
        # Create a mock response with file content (make it large enough to pass size check)
        file_content = b"This is test PDF content " * 50  # Make it larger than 1024 bytes
        response = Mock()
        response.url = "https://example.com/test.pdf"
        response.body = file_content
        response.headers = {'Content-Type': 'application/pdf'}
        response.meta = {
            'content_id': 1,
            'content_type': ContentType.DOCUMENT,
            'file_info': {'filename': 'test.pdf', 'extension': 'pdf'}
        }
        
        # Mock database and file operations
        mock_session.return_value.__enter__.return_value = Mock()
        
        with patch('builtins.open', create=True) as mock_open:
            with patch.object(self.spider, '_is_duplicate_content', return_value=False):
                with patch.object(self.spider, '_update_content_download'):
                    with patch('spigamonde.spiders.content_spider.get_storage_path', return_value='test_path.pdf'):
                        # Should not raise any exceptions
                        self.spider._download_content(response)
                        
                        # Verify file was written
                        mock_open.assert_called_once()
    
    @patch('spigamonde.spiders.content_spider.session_scope')
    def test_download_content_duplicate_detection(self, mock_session):
        """Test that duplicate content is skipped."""
        response = Mock()
        response.url = "https://example.com/test.pdf"
        response.body = b"Test content"
        response.meta = {
            'content_id': 1,
            'content_type': ContentType.DOCUMENT,
            'file_info': {'filename': 'test.pdf', 'extension': 'pdf'}
        }
        
        # Mock database operations
        mock_session.return_value.__enter__.return_value = Mock()
        
        with patch.object(self.spider, '_is_duplicate_content', return_value=True):
            with patch.object(self.spider, '_update_content_status') as mock_update:
                self.spider._download_content(response)
                
                # Should update status to skipped
                mock_update.assert_called_once()
                args = mock_update.call_args[0]
                assert args[1].value == 'skipped'  # ContentStatus.SKIPPED
    
    def test_start_requests_generation(self):
        """Test that start_requests generates correct initial requests."""
        self.spider.start_urls = ['https://example.com', 'https://test.org']
        
        with patch.object(self.spider, '_create_crawl_session'):
            with patch.object(self.spider, '_save_url'):
                requests = list(self.spider.start_requests())
                
                assert len(requests) == 2
                assert all(isinstance(r, Request) for r in requests)
                assert requests[0].url == 'https://example.com'
                assert requests[1].url == 'https://test.org'
                assert all(r.callback == self.spider.parse for r in requests)


if __name__ == "__main__":
    pytest.main([__file__])
