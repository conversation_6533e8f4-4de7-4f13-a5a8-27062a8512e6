#!/usr/bin/env python3
"""
Simple Test Script for Web Interface
Description: Basic test script that doesn't require complex dependencies
Execution Time: ~10 seconds
Resource Usage: Minimal (just logging and database check)
"""

import sys
import time
import os
from pathlib import Path

def main():
    """Execute a simple test suitable for web interface."""
    print("Starting simple test script...")
    print(f"Working directory: {os.getcwd()}")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")

    try:
        # Add SpigaMonde to path
        project_root = Path(__file__).parent.parent.parent
        sys.path.insert(0, str(project_root))
        print(f"Project root: {project_root}")

        # Test basic imports
        print("Testing imports...")

        try:
            from spigamonde.database.connection import init_database
            print("SUCCESS Database connection import successful")
        except ImportError as e:
            print(f"ERROR Database import failed: {e}")
            return 1

        try:
            from spigamonde.config.settings import get_settings
            print("SUCCESS Settings import successful")
        except ImportError as e:
            print(f"ERROR Settings import failed: {e}")
            return 1

        # Initialize database
        print("Initializing database...")
        init_database()
        print("SUCCESS Database initialized successfully")

        # Get settings
        print("Loading settings...")
        settings = get_settings()
        print(f"SUCCESS Settings loaded - User agent: {settings.spider.user_agent}")

        # Simulate some work
        print("Simulating work...")
        for i in range(5):
            print(f"   Step {i+1}/5...")
            time.sleep(1)

        print("Simple test completed successfully!")
        print("Test results:")
        print("   - Database: Connected")
        print("   - Settings: Loaded")
        print("   - Execution: Successful")

        return 0

    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
