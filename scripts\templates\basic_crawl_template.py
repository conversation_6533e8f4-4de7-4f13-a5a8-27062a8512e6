#!/usr/bin/env python3
"""
Basic SpigaMonde Crawl Script Template
Customize this template for your specific crawling needs.

Usage:
    1. Copy this template to scripts/user_scripts/
    2. Rename to describe your use case (e.g., my_domain_crawler.py)
    3. Customize the configuration sections below
    4. Run: python scripts/user_scripts/my_domain_crawler.py
"""

import sys
import os
from pathlib import Path
from datetime import datetime
import threading

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.dashboard import run_live_dashboard
from rich.console import Console
from rich.panel import Panel


# ============================================================================
# CONFIGURATION SECTION - CUSTOMIZE THESE VALUES
# ============================================================================

# Target URLs to crawl
TARGET_URLS = [
    "https://example.com",
    # Add your target URLs here
]

# Crawl parameters
CRAWL_CONFIG = {
    'USER_AGENT': 'SpigaMonde Custom Crawler 1.0',
    'ROBOTSTXT_OBEY': True,  # Always respect robots.txt
    'DOWNLOAD_DELAY': 1.0,   # Delay between requests (seconds)
    'RANDOMIZE_DOWNLOAD_DELAY': True,
    'CONCURRENT_REQUESTS': 2,  # Number of parallel requests
    'DEPTH_LIMIT': 2,        # How deep to crawl
    'CLOSESPIDER_PAGECOUNT': 50,  # Maximum pages to crawl
    'LOG_LEVEL': 'INFO',
    
    # Content filtering
    'ALLOWED_FILE_TYPES': ['html', 'pdf', 'doc'],  # File types to download
    'MAX_FILE_SIZE': 10 * 1024 * 1024,  # 10MB max file size
}

# Analysis focus (customize based on your content type)
EXPECTED_CONTENT_CATEGORIES = [
    'TECHNICAL_DOCUMENTATION',
    'BLOG_POST',
    'ACADEMIC_PAPER',
    # Add expected categories for your domain
]

# Output configuration
OUTPUT_CONFIG = {
    'enable_monitoring': True,   # Show real-time dashboard
    'monitoring_duration': 300,  # Dashboard duration (seconds)
    'export_results': True,     # Export results after crawl
    'export_format': 'csv',     # csv, json, or both
}

# ============================================================================
# MAIN EXECUTION LOGIC - USUALLY NO NEED TO MODIFY
# ============================================================================

def main():
    """Execute the custom crawl with monitoring and analysis."""
    console = Console()
    
    # Display banner
    console.print(Panel.fit(
        "[bold blue]SpigaMonde Custom Crawler[/bold blue]\n"
        f"Crawling {len(TARGET_URLS)} target URL(s)",
        border_style="blue"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Display configuration
    console.print(f"[cyan]Configuration:[/cyan]")
    console.print(f"  Max depth: {CRAWL_CONFIG['DEPTH_LIMIT']}")
    console.print(f"  Max pages: {CRAWL_CONFIG['CLOSESPIDER_PAGECOUNT']}")
    console.print(f"  Download delay: {CRAWL_CONFIG['DOWNLOAD_DELAY']}s")
    console.print(f"  File types: {', '.join(CRAWL_CONFIG['ALLOWED_FILE_TYPES'])}")
    
    # Start monitoring if enabled
    dashboard_thread = None
    if OUTPUT_CONFIG['enable_monitoring']:
        console.print("[yellow]Starting monitoring dashboard...[/yellow]")
        dashboard_thread = threading.Thread(
            target=run_live_dashboard,
            args=(OUTPUT_CONFIG['monitoring_duration'],),
            daemon=True
        )
        dashboard_thread.start()
    
    # Execute crawl
    try:
        console.print("[cyan]Starting crawl...[/cyan]")
        process = CrawlerProcess(CRAWL_CONFIG)
        process.crawl(ContentSpider, start_urls=TARGET_URLS)
        process.start()
        
        console.print("[green]✓ Crawl completed successfully![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Analyze results
    console.print("\n[bold cyan]Analyzing results...[/bold cyan]")
    analyze_results(console)
    
    # Export if enabled
    if OUTPUT_CONFIG['export_results']:
        export_results(console)
    
    return 0


def analyze_results(console):
    """Analyze and display crawl results."""
    
    with session_scope() as session:
        # Query all content from this crawl
        all_content = session.query(Content).all()
        analyzed_content = session.query(Content).join(ContentAnalysis).all()
        
        if not all_content:
            console.print("[yellow]No content found in this crawl.[/yellow]")
            return
        
        # Basic statistics
        total_items = len(all_content)
        analyzed_items = len(analyzed_content)
        analysis_rate = (analyzed_items / total_items * 100) if total_items > 0 else 0
        
        console.print(f"📊 [bold]Crawl Statistics:[/bold]")
        console.print(f"  Total items discovered: {total_items}")
        console.print(f"  Items analyzed: {analyzed_items}")
        console.print(f"  Analysis coverage: {analysis_rate:.1f}%")
        
        if analyzed_content:
            # Category breakdown
            categories = {}
            quality_scores = {}
            
            for content in analyzed_content:
                analysis = content.content_analysis[0]
                category = analysis.category
                quality = analysis.quality_score
                
                categories[category] = categories.get(category, 0) + 1
                quality_scores[quality] = quality_scores.get(quality, 0) + 1
            
            console.print(f"\n📋 [bold]Content Categories:[/bold]")
            for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                percentage = count / analyzed_items * 100
                console.print(f"  {category.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
            
            console.print(f"\n⭐ [bold]Quality Distribution:[/bold]")
            for quality, count in sorted(quality_scores.items(), key=lambda x: x[1], reverse=True):
                percentage = count / analyzed_items * 100
                console.print(f"  {quality}: {count} ({percentage:.1f}%)")


def export_results(console):
    """Export crawl results to file."""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    export_filename = f"crawl_results_{timestamp}.{OUTPUT_CONFIG['export_format']}"
    
    console.print(f"\n[dim]Results can be exported using:[/dim]")
    console.print(f"[dim]spiga export --format {OUTPUT_CONFIG['export_format']} --output {export_filename}[/dim]")
    
    # You can add custom export logic here if needed


# ============================================================================
# CUSTOMIZATION HELPERS - ADD YOUR DOMAIN-SPECIFIC LOGIC HERE
# ============================================================================

def custom_content_filter(content):
    """
    Add custom logic to filter content based on your requirements.
    
    Args:
        content: Content object from database
        
    Returns:
        bool: True if content should be included in analysis
    """
    # Example: Only include content with certain keywords
    # if hasattr(content, 'filename'):
    #     keywords = ['research', 'analysis', 'study']
    #     return any(keyword in content.filename.lower() for keyword in keywords)
    
    return True


def custom_analysis_post_processing(analysis_result):
    """
    Add custom post-processing of analysis results.
    
    Args:
        analysis_result: ContentAnalysisResult object
        
    Returns:
        dict: Additional metadata or processed results
    """
    # Example: Extract domain-specific information
    # custom_data = {}
    # if 'research' in analysis_result.metadata.keywords:
    #     custom_data['research_type'] = 'empirical'
    
    # return custom_data
    
    return {}


if __name__ == "__main__":
    sys.exit(main())
