"""Main content spider for discovering and downloading files."""

import json
from datetime import datetime
from typing import Any, Dict, Generator, List, Optional
from urllib.parse import urljoin, urlparse

import scrapy
from loguru import logger
from scrapy import Request
from scrapy.http import Response
from sqlalchemy.orm import Session

from ..config.settings import get_settings
from ..database.connection import session_scope
from ..models.content import Content, ContentStatus, ContentType, CrawlSession, URL
from ..utils.file_utils import (
    calculate_content_hash, calculate_url_hash, get_file_detector, get_storage_path
)
from ..monitoring.logger import get_logger, log_spider_activity
from ..monitoring.metrics import get_metrics_collector, PerformanceTimer, record_crawl_metric, record_download_metric
from ..analysis.analyzer import get_content_analyzer


class ContentSpider(scrapy.Spider):
    """Spider for discovering and cataloging web content and files."""
    
    name = "content_spider"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.settings = get_settings()
        self.file_detector = get_file_detector()
        self.session_id: Optional[int] = None
        self.crawled_urls = set()
        self.discovered_content = 0
        self.downloaded_content = 0
        self.enhanced_logger = get_logger()
        self.metrics = get_metrics_collector()
        self.content_analyzer = get_content_analyzer()
        
        # Get spider settings from our configuration
        spider_settings = get_settings().spider

        # Configure spider settings
        self.custom_settings = {
            'CONCURRENT_REQUESTS': spider_settings.concurrent_requests,
            'DOWNLOAD_DELAY': spider_settings.download_delay,
            'RANDOMIZE_DOWNLOAD_DELAY': spider_settings.randomize_download_delay,
            'USER_AGENT': spider_settings.user_agent,
            'ROBOTSTXT_OBEY': spider_settings.robotstxt_obey,
            'DEPTH_LIMIT': spider_settings.max_depth,
        }

        # Set page limit if specified
        if spider_settings.max_pages:
            self.custom_settings['CLOSESPIDER_PAGECOUNT'] = spider_settings.max_pages

        # Store settings for later use
        self.spider_settings = spider_settings
    
    def start_requests(self) -> Generator[Request, None, None]:
        """Generate initial requests from start URLs."""
        start_urls = getattr(self, 'start_urls', [])
        
        if not start_urls:
            logger.error("No start URLs provided")
            return
        
        # Create crawl session
        self._create_crawl_session(start_urls)
        
        # Create initial URL records and requests
        for url in start_urls:
            self._save_url(url, depth=0, is_seed=True)
            yield Request(
                url=url,
                callback=self.parse,
                meta={'depth': 0, 'url_obj': url}
            )
    
    def parse(self, response: Response) -> Generator[Request, None, None]:
        """Parse response and extract links and content."""
        current_depth = response.meta.get('depth', 0)
        current_url = response.url

        with PerformanceTimer("parse_page"):
            self.enhanced_logger.info(
                f"Parsing page: {current_url}",
                url=current_url,
                depth=current_depth,
                status_code=response.status,
                spider_name=self.name
            )

            # Record crawl metrics
            record_crawl_metric("page_parsed", 1, url=current_url)

            # Update URL status
            self._update_url_status(current_url, response.status)

            # Extract and process content from current page
            yield from self._extract_content(response)

            # Extract links for further crawling if within depth limit
            if current_depth < self.spider_settings.max_depth:
                yield from self._extract_links(response, current_depth + 1)

            log_spider_activity("page_parsed", url=current_url, depth=current_depth)
    
    def _extract_content(self, response: Response) -> Generator[Request, None, None]:
        """Extract downloadable content from the current page."""
        # Find all links that might point to files
        file_links = response.css('a[href]::attr(href)').getall()
        
        for link in file_links:
            absolute_url = urljoin(response.url, link)
            
            # Get file info and check if it's a file we want
            content_type, file_info = self.file_detector.detect_content_type(absolute_url)
            
            # Skip if not an interesting file type
            if content_type == ContentType.OTHER and not file_info['extension']:
                continue
            
            # Check if file type is allowed
            if file_info['extension'] and not self.file_detector.is_allowed_file_type(file_info['extension']):
                continue
            
            # Create content record
            content_id = self._save_content(
                url=absolute_url,
                source_url=response.url,
                content_type=content_type,
                file_info=file_info
            )
            
            if content_id:
                # Request to download the file
                yield Request(
                    url=absolute_url,
                    callback=self._download_content,
                    meta={
                        'content_id': content_id,
                        'content_type': content_type,
                        'file_info': file_info
                    }
                )
    
    def _extract_links(self, response: Response, next_depth: int) -> Generator[Request, None, None]:
        """Extract links for further crawling."""
        links = response.css('a[href]::attr(href)').getall()
        
        for link in links:
            absolute_url = urljoin(response.url, link)
            
            # Skip if already crawled
            if absolute_url in self.crawled_urls:
                continue
            
            # Basic URL filtering
            if not self._should_crawl_url(absolute_url):
                continue
            
            # Save URL and create request
            self._save_url(absolute_url, depth=next_depth)
            self.crawled_urls.add(absolute_url)
            
            yield Request(
                url=absolute_url,
                callback=self.parse,
                meta={'depth': next_depth, 'url_obj': absolute_url}
            )
    
    def _download_content(self, response: Response) -> None:
        """Download and save content file."""
        content_id = response.meta['content_id']
        content_type = response.meta['content_type']
        file_info = response.meta['file_info']
        
        try:
            # Check content size
            content_length = len(response.body)
            should_download, reason = self.file_detector.should_download_content(
                response.url, content_length
            )
            
            if not should_download:
                self.enhanced_logger.info(
                    f"Skipping content: {reason}",
                    url=response.url,
                    reason=reason,
                    file_size=content_length
                )
                self._update_content_status(content_id, ContentStatus.SKIPPED, reason)
                return
            
            # Calculate content hash
            content_hash = calculate_content_hash(response.body)
            
            # Check for duplicates
            if self._is_duplicate_content(content_hash):
                self.enhanced_logger.info(
                    f"Skipping duplicate content",
                    url=response.url,
                    content_hash=content_hash[:8]
                )
                self._update_content_status(content_id, ContentStatus.SKIPPED, "Duplicate content")
                return
            
            # Determine storage path
            filename = file_info['filename'] or f"file_{content_id}"
            storage_path = get_storage_path(response.url, filename, content_type)
            
            # Save file to disk
            with open(storage_path, 'wb') as f:
                f.write(response.body)
            
            # Update content record
            self._update_content_download(
                content_id=content_id,
                local_path=str(storage_path),
                content_hash=content_hash,
                file_size=content_length,
                mime_type=response.headers.get('Content-Type', '').decode('utf-8', errors='ignore')
            )
            
            self.downloaded_content += 1

            # Enhanced logging with metadata
            self.enhanced_logger.info(
                f"Content downloaded successfully",
                url=response.url,
                storage_path=str(storage_path),
                file_size=content_length,
                content_type=content_type.value,
                content_hash=content_hash[:8]
            )

            # Record download metrics
            record_download_metric("file_downloaded", 1, content_type=content_type.value, file_size=content_length)
            record_download_metric("bytes_downloaded", content_length)

            log_spider_activity("content_downloaded", url=response.url, file_size=content_length)

            # Perform content analysis
            self._analyze_content(
                content_id=content_id,
                url=response.url,
                content=response.body.decode('utf-8', errors='ignore'),
                file_path=str(storage_path),
                mime_type=response.headers.get('Content-Type', '').decode('utf-8', errors='ignore')
            )

        except Exception as e:
            self.enhanced_logger.error(
                f"Failed to download content",
                url=response.url,
                error=str(e),
                content_id=content_id
            )
            self._update_content_status(content_id, ContentStatus.FAILED, str(e))
            record_download_metric("download_failed", 1)

    def _analyze_content(self, content_id: int, url: str, content: str,
                        file_path: str, mime_type: str) -> None:
        """Analyze downloaded content and store results."""
        try:
            # Perform content analysis
            analysis_result = self.content_analyzer.analyze_content(
                url=url,
                content=content,
                file_path=file_path,
                mime_type=mime_type
            )

            # Store analysis results in database
            self._store_analysis_results(content_id, analysis_result)

            self.enhanced_logger.info(
                f"Content analysis completed",
                url=url,
                content_id=content_id,
                category=analysis_result.classification.category.value,
                quality=analysis_result.classification.quality_score.value,
                confidence=analysis_result.classification.confidence,
                analysis_time=analysis_result.analysis_time
            )

        except Exception as e:
            self.enhanced_logger.error(
                f"Content analysis failed",
                url=url,
                content_id=content_id,
                error=str(e)
            )

    def _store_analysis_results(self, content_id: int, analysis_result) -> None:
        """Store content analysis results in database."""
        try:
            from ..models.content import ContentAnalysis
            import json

            with session_scope() as session:
                # Create analysis record
                analysis = ContentAnalysis(
                    content_id=content_id,
                    category=analysis_result.classification.category.value,  # Store enum value, not enum object
                    confidence=analysis_result.classification.confidence,
                    quality_score=analysis_result.classification.quality_score.value,  # Store enum value, not enum object
                    title=analysis_result.metadata.title,
                    description=analysis_result.metadata.description,
                    author=analysis_result.metadata.author,
                    language=analysis_result.metadata.language,
                    word_count=analysis_result.metadata.word_count,
                    character_count=analysis_result.metadata.character_count,
                    line_count=analysis_result.metadata.line_count,
                    paragraph_count=analysis_result.metadata.paragraph_count,
                    readability_score=analysis_result.metadata.readability_score,
                    complexity_score=analysis_result.metadata.complexity_score,
                    content_hash=analysis_result.metadata.content_hash,
                    similarity_hash=analysis_result.metadata.similarity_hash,
                    emails=json.dumps(analysis_result.metadata.emails) if analysis_result.metadata.emails else None,
                    urls=json.dumps(analysis_result.metadata.urls) if analysis_result.metadata.urls else None,
                    phone_numbers=json.dumps(analysis_result.metadata.phone_numbers) if analysis_result.metadata.phone_numbers else None,
                    dates=json.dumps(analysis_result.metadata.dates) if analysis_result.metadata.dates else None,
                    keywords=json.dumps(analysis_result.metadata.keywords) if analysis_result.metadata.keywords else None,
                    topics=json.dumps(analysis_result.metadata.topics) if analysis_result.metadata.topics else None,
                    analysis_time=analysis_result.analysis_time,
                    analysis_success=analysis_result.success,
                    error_message=analysis_result.error_message
                )

                session.add(analysis)
                session.commit()

        except Exception as e:
            self.enhanced_logger.error(f"Failed to store analysis results: {e}", content_id=content_id)

    def _should_crawl_url(self, url: str) -> bool:
        """Determine if URL should be crawled."""
        parsed = urlparse(url)
        
        # Skip non-HTTP(S) URLs
        if parsed.scheme not in ('http', 'https'):
            return False
        
        # Skip common non-content URLs
        skip_patterns = [
            'javascript:', 'mailto:', 'tel:', 'ftp:',
            '#', '?logout', '?login', '/login', '/logout',
            '.css', '.js', '.ico'
        ]
        
        url_lower = url.lower()
        for pattern in skip_patterns:
            if pattern in url_lower:
                return False
        
        return True
    
    def _create_crawl_session(self, start_urls: List[str]) -> None:
        """Create a new crawl session record."""
        try:
            with session_scope() as session:
                crawl_session = CrawlSession(
                    session_name=f"crawl_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    start_time=datetime.now(),
                    seed_urls=json.dumps(start_urls),
                    max_depth=self.spider_settings.max_depth,
                    max_pages=self.spider_settings.max_pages,
                )
                session.add(crawl_session)
                session.flush()
                self.session_id = crawl_session.id
                logger.info(f"Created crawl session {self.session_id}")
        except Exception as e:
            logger.error(f"Failed to create crawl session: {e}")
    
    def _save_url(self, url: str, depth: int = 0, is_seed: bool = False) -> None:
        """Save URL to database."""
        try:
            with session_scope() as session:
                parsed = urlparse(url)
                
                # Check if URL already exists
                existing = session.query(URL).filter(URL.url == url).first()
                if existing:
                    return
                
                url_obj = URL(
                    url=url,
                    domain=parsed.netloc,
                    path=parsed.path,
                    depth=depth,
                    is_seed=is_seed
                )
                session.add(url_obj)
        except Exception as e:
            logger.error(f"Failed to save URL {url}: {e}")
    
    def _update_url_status(self, url: str, status_code: int) -> None:
        """Update URL crawl status."""
        try:
            with session_scope() as session:
                url_obj = session.query(URL).filter(URL.url == url).first()
                if url_obj:
                    url_obj.status_code = status_code
                    url_obj.last_crawled = datetime.now()
                    url_obj.crawl_count += 1
                    url_obj.is_crawled = True
        except Exception as e:
            logger.error(f"Failed to update URL status for {url}: {e}")
    
    def _save_content(self, url: str, source_url: str, content_type: ContentType, file_info: Dict[str, Any]) -> Optional[int]:
        """Save content record to database."""
        try:
            with session_scope() as session:
                # Get source URL ID
                source_url_obj = session.query(URL).filter(URL.url == source_url).first()
                if not source_url_obj:
                    logger.warning(f"Source URL not found: {source_url}")
                    return None
                
                url_hash = calculate_url_hash(url)
                
                # Check if content already exists
                existing = session.query(Content).filter(Content.url_hash == url_hash).first()
                if existing:
                    return existing.id
                
                content = Content(
                    url=url,
                    filename=file_info.get('filename'),
                    file_extension=file_info.get('extension'),
                    mime_type=file_info.get('mime_type'),
                    content_type=content_type,
                    url_hash=url_hash,
                    source_url_id=source_url_obj.id,
                    status=ContentStatus.PENDING
                )
                session.add(content)
                session.flush()
                
                self.discovered_content += 1
                return content.id
                
        except Exception as e:
            logger.error(f"Failed to save content {url}: {e}")
            return None
    
    def _update_content_status(self, content_id: int, status: ContentStatus, error_message: str = None) -> None:
        """Update content processing status."""
        try:
            with session_scope() as session:
                content = session.query(Content).filter(Content.id == content_id).first()
                if content:
                    content.status = status
                    if error_message:
                        content.error_message = error_message
                    if status == ContentStatus.FAILED:
                        content.retry_count += 1
        except Exception as e:
            logger.error(f"Failed to update content status for {content_id}: {e}")
    
    def _update_content_download(self, content_id: int, local_path: str, content_hash: str, 
                                file_size: int, mime_type: str) -> None:
        """Update content with download information."""
        try:
            with session_scope() as session:
                content = session.query(Content).filter(Content.id == content_id).first()
                if content:
                    content.local_path = local_path
                    content.content_hash = content_hash
                    content.file_size = file_size
                    content.mime_type = mime_type
                    content.is_downloaded = True
                    content.status = ContentStatus.COMPLETED
        except Exception as e:
            logger.error(f"Failed to update content download for {content_id}: {e}")
    
    def _is_duplicate_content(self, content_hash: str) -> bool:
        """Check if content with this hash already exists."""
        try:
            with session_scope() as session:
                existing = session.query(Content).filter(Content.content_hash == content_hash).first()
                return existing is not None
        except Exception as e:
            logger.error(f"Failed to check for duplicate content: {e}")
            return False
    
    def closed(self, reason: str) -> None:
        """Called when spider is closed."""
        logger.info(f"Spider closed: {reason}")
        logger.info(f"Discovered content: {self.discovered_content}")
        logger.info(f"Downloaded content: {self.downloaded_content}")
        
        # Update crawl session
        if self.session_id:
            try:
                with session_scope() as session:
                    crawl_session = session.query(CrawlSession).filter(CrawlSession.id == self.session_id).first()
                    if crawl_session:
                        crawl_session.end_time = datetime.now()
                        crawl_session.is_active = False
                        crawl_session.is_completed = True
                        crawl_session.content_found = self.discovered_content
                        crawl_session.content_downloaded = self.downloaded_content
            except Exception as e:
                logger.error(f"Failed to update crawl session: {e}")
