#!/usr/bin/env python3
"""
Test current SpigaMonde configuration
Shows the actual settings being used.
"""

import os
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

console = Console()

def test_config():
    """Test current configuration."""
    
    # Force reload of settings by clearing any cached modules
    import sys
    modules_to_clear = [name for name in sys.modules.keys() if name.startswith('spigamonde')]
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]
    
    # Now import fresh settings
    from spigamonde.config.settings import get_settings
    
    settings = get_settings()
    
    # Create configuration table
    table = Table(title="Current SpigaMonde Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="yellow")
    
    # Spider settings
    table.add_row("User-Agent", settings.spider.user_agent)
    table.add_row("Downloads Directory", str(settings.storage.base_path))
    table.add_row("Max Depth", str(settings.spider.max_depth))
    table.add_row("Max Pages", str(settings.spider.max_pages) if settings.spider.max_pages else "Unlimited")
    table.add_row("Download Delay", f"{settings.spider.download_delay}s")
    table.add_row("Concurrent Requests", str(settings.spider.concurrent_requests))
    table.add_row("Robots.txt Obey", str(settings.spider.robotstxt_obey))
    table.add_row("Max File Size", f"{settings.spider.max_file_size / (1024*1024):.0f}MB")
    
    # Determine config type
    if "TestCrawler" in settings.spider.user_agent:
        config_type = "🧪 TESTING"
        border_style = "yellow"
    elif "SpigaMonde" in settings.spider.user_agent:
        config_type = "🚀 PRODUCTION"
        border_style = "green"
    else:
        config_type = "❓ UNKNOWN"
        border_style = "red"
    
    console.print(Panel(
        table,
        title=f"{config_type} Configuration Active",
        border_style=border_style
    ))
    
    # Show environment variables
    console.print("\n[bold]Environment Variables:[/bold]")
    env_vars = [
        "SPIDER_USER_AGENT",
        "SPIDER_MAX_PAGES", 
        "SPIDER_MAX_DEPTH",
        "STORAGE_BASE_PATH",
        "DEBUG"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "[dim]Not set[/dim]")
        console.print(f"  {var}: {value}")

if __name__ == "__main__":
    test_config()
