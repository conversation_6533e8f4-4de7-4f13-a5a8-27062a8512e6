{"type":"entity","name":"Spider System","entityType":"SystemComponent","observations":["Core web crawling component responsible for content extraction","Implemented in content_spider.py","Handles link extraction from web pages","Complies with robots.txt guidelines","Part of the spigamonde.spiders module"]}
{"type":"entity","name":"Content Analysis Engine","entityType":"SystemComponent","observations":["Responsible for analyzing and classifying web content","Performs content quality assessment","Extracts metadata from crawled content","Located in spigamonde/analysis/ directory","Includes classifier.py and analyzer.py modules"]}
{"type":"entity","name":"Database Layer","entityType":"SystemComponent","observations":["SQLite database implementation for persistent storage","Stores crawled content, analysis results, and metadata","Located in spigamonde/database/ directory","Handles data persistence for the entire system","Includes connection management and transaction handling"]}
{"type":"entity","name":"Monitoring System","entityType":"SystemComponent","observations":["Real-time dashboard for system performance visualization","Collects and aggregates system metrics across all components","Tracks performance indicators and system health","Implemented in spigamonde/monitoring/ directory","Provides alerting capabilities for system issues"]}
{"type":"entity","name":"CLI Interface","entityType":"SystemComponent","observations":["Handles command line interactions with the system","Parses user commands and parameters","Provides help and usage information","Implemented in spigamonde/cli.py","Supports various commands for system operation"]}
{"type":"entity","name":"Configuration System","entityType":"SystemComponent","observations":["Manages system settings and configuration","Handles environment variables and configuration files","Provides default values and overrides","Implemented in spigamonde/config/ directory","Includes settings.py for configuration management"]}
{"type":"entity","name":"Web Pages","entityType":"ContentType","observations":["HTML content","Text extraction","Web page content structure","HTML markup and semantic elements","Hyperlink connectivity"]}
{"type":"entity","name":"Documents","entityType":"ContentType","observations":["PDFs","Academic papers","Technical docs","Structured document formats","Text-based documentation"]}
{"type":"entity","name":"Images","entityType":"ContentType","observations":["PNG files","JPG files","SVG files","Raster and vector graphics","Visual content formats"]}
{"type":"entity","name":"Archives","entityType":"Content Type","observations":["Compressed files that bundle multiple files or directories into a single file","Used for data archiving and distribution","Common formats include ZIP, TAR, GZ, RAR","Reduces storage space and transfer time","May require decompression tools to access contents"]}
{"type":"entity","name":"Data Files","entityType":"Content Type","observations":["Structured data files containing organized information","Includes configuration files that control application behavior","Common formats include JSON, XML, YAML, CSV, INI","Used for data storage, exchange, and configuration management","May be human-readable or binary formats"]}
{"type":"entity","name":"Academic Papers","entityType":"ContentCategory","observations":["research content","citations","peer-reviewed","scholarly articles","scientific publications"]}
{"type":"entity","name":"Technical Documentation","entityType":"ContentCategory","observations":["tutorials","guides","API docs","instructional content","reference materials"]}
{"type":"entity","name":"Blog Posts","entityType":"ContentCategory","observations":["informal content","opinions","personal perspectives","casual writing style","web-based articles"]}
{"type":"entity","name":"Legal Documents","entityType":"ContentCategory","observations":["Category for terms, policies, and contracts","Legal documentation including terms of service, privacy policies, and contractual agreements","Content type that requires legal review and compliance checks"]}
{"type":"entity","name":"News Articles","entityType":"ContentCategory","observations":["Category for journalism and current events","Timely content covering news, events, and journalistic reporting","Content that is time-sensitive and fact-based"]}
{"type":"entity","name":"Low Quality Content","entityType":"ContentCategory","observations":["Category for spam and irrelevant content","Content that lacks value, is potentially spam, or is irrelevant to users","Content that should be filtered or flagged for removal"]}
{"type":"entity","name":"Spider","entityType":"Component","observations":["Web crawling component responsible for fetching content from websites","Part of the SpigaMonde web spider system","Triggers content analysis pipeline after crawling"]}
{"type":"entity","name":"Database","entityType":"Component","observations":["Storage system for crawled content","Receives and stores data from the Spider component","Persistent storage for web content"]}
{"type":"entity","name":"Content Analysis","entityType":"Component","observations":["Analysis pipeline that processes crawled content","Triggered by the Spider component","Performs content classification and analysis"]}
{"type":"entity","name":"Real-time Metrics","entityType":"MonitoringComponent","observations":["Collects system performance data in real-time","Gathers CPU, memory, and network usage statistics","Provides low-latency metric collection for monitoring","Part of the Monitoring System component","Feeds data to Dashboard Display for visualization"]}
{"type":"entity","name":"Dashboard Display","entityType":"MonitoringComponent","observations":["Visualizes system metrics and performance data","Provides real-time dashboard for system monitoring","Receives data from Real-time Metrics component","Presents information to User Monitoring interface","Implemented in spigamonde/monitoring/dashboard.py","Third step of the Monitoring Workflow","Presents aggregated metrics through visual dashboards","Provides real-time visibility into system performance"]}
{"type":"entity","name":"User Monitoring","entityType":"MonitoringComponent","observations":["Interface for users to monitor system performance","Receives visualized data from Dashboard Display","Allows users to track system health and metrics","Provides alerting capabilities for system issues","Part of the overall Monitoring System component"]}
{"type":"entity","name":"Custom Download Directories","entityType":"Feature","observations":["Allows users to specify where downloaded content should be stored","Part of file organization capabilities"]}
{"type":"entity","name":"File Organization","entityType":"Feature","observations":["Organizes downloaded content in a structured manner","Connects download directories to content storage"]}
{"type":"entity","name":"Content Storage","entityType":"Feature","observations":["Manages the actual storage of downloaded content","Final destination in the download organization chain"]}
{"type":"entity","name":"Depth Limiting","entityType":"Feature","observations":["Controls how deep the crawler will go in a website's structure","Limits recursive crawling to prevent infinite loops"]}
{"type":"entity","name":"Crawl Scope","entityType":"Feature","observations":["Defines the boundaries of what content will be crawled","Influenced by depth limiting settings"]}
{"type":"entity","name":"Resource Management","entityType":"Feature","observations":["Manages system resources during crawling operations","Affected by crawl scope definitions"]}
{"type":"entity","name":"Delay Settings","entityType":"Feature","observations":["Configures time delays between crawl requests","Helps control the rate of requests to servers"]}
{"type":"entity","name":"Respectful Crawling","entityType":"Feature","observations":["Ensures crawling doesn't overwhelm target servers","Implemented through delay settings and other politeness measures"]}
{"type":"entity","name":"Server Load Management","entityType":"Feature","observations":["Manages the impact of crawling on target servers","Depends on respectful crawling practices"]}
{"type":"entity","name":"Crawling Workflow","entityType":"Workflow","observations":["Primary workflow for web content crawling and processing","Systematic process for extracting and analyzing web content","Core data pipeline for the SpigaMonde system","Executes asynchronously to handle multiple URLs concurrently","Implements error handling and retry mechanisms","Supports configurable depth limits and politeness settings"]}
{"type":"entity","name":"URL Input","entityType":"WorkflowStep","observations":["Initial step of the crawling workflow","Accepts URLs to be crawled","Defines the starting points for web crawling","Supports both single URL and batch URL processing","Validates URL format and accessibility","Integrates with seed URL databases"]}
{"type":"entity","name":"Robots.txt Check","entityType":"WorkflowStep","observations":["Checks robots.txt compliance before crawling","Ensures respectful crawling practices","Determines allowed and disallowed paths","Parses robots.txt files according to RFC 9309","Implements caching to avoid repeated requests","Handles both allow and disallow directives"]}
{"type":"entity","name":"Content Download","entityType":"WorkflowStep","observations":["Downloads content from web pages","Handles various content types (HTML, PDF, images)","Manages network requests and responses","Handles HTTP status codes and redirects","Supports various content encodings (gzip, deflate)","Implements timeout and retry mechanisms"]}
{"type":"entity","name":"Link Extraction","entityType":"WorkflowStep","observations":["Extracts hyperlinks from downloaded content","Identifies new URLs to crawl","Supports recursive crawling within scope limits","Identifies href, src, and other relevant attributes","Filters links based on domain scope settings","Normalizes relative URLs to absolute URLs"]}
{"type":"entity","name":"Database Storage","entityType":"WorkflowStep","observations":["Stores crawled and analyzed content in database","Persists data for future retrieval and analysis","Manages data integrity and transaction handling","Implements transactional integrity","Supports bulk inserts for performance","Handles content deduplication"]}
{"type":"entity","name":"Content Analysis Step","entityType":"WorkflowStep","observations":["Analyzes downloaded content for classification","Extracts metadata and content quality metrics","Prepares content for storage and further processing","Classifies content into categories (academic, blog, news, etc.)","Extracts title, description, and key metadata","Performs quality scoring and deduplication"]}
{"type":"entity","name":"Analysis Workflow","entityType":"Workflow","observations":["Primary workflow for content analysis in the MCP system","Consists of 6 sequential steps for processing content","Used for systematic content analysis and metadata extraction","Workflow steps: 1. Content Reception → 2. Text Extraction → 3. Classification → 4. Quality Assessment → 5. Metadata Extraction → 6. Result Storage","This workflow is part of the MCP memory server's key workflows for content analysis"]}
{"type":"entity","name":"Content Reception","entityType":"WorkflowStep","observations":["First step of the Analysis Workflow","Initial stage where content is received for processing"]}
{"type":"entity","name":"Text Extraction","entityType":"WorkflowStep","observations":["Second step of the Analysis Workflow","Process of extracting text from received content"]}
{"type":"entity","name":"Classification","entityType":"WorkflowStep","observations":["Third step of the Analysis Workflow","Categorization of content based on predefined criteria"]}
{"type":"entity","name":"Quality Assessment","entityType":"WorkflowStep","observations":["Fourth step of the Analysis Workflow","Evaluation of content quality and relevance"]}
{"type":"entity","name":"Metadata Extraction","entityType":"WorkflowStep","observations":["Fifth step of the Analysis Workflow","Extraction of metadata from classified content"]}
{"type":"entity","name":"Result Storage","entityType":"WorkflowStep","observations":["Sixth and final step of the Analysis Workflow","Storage of processed results and metadata"]}
{"type":"entity","name":"Monitoring Workflow","entityType":"Workflow","observations":["Third key workflow in the MCP memory server","Responsible for tracking and displaying system performance metrics","Consists of four main steps: Metrics Collection, Real-time Aggregation, Dashboard Display, and Performance Tracking"]}
{"type":"entity","name":"Metrics Collection","entityType":"WorkflowStep","observations":["First step of the Monitoring Workflow","Involves gathering performance metrics from various system components","Data sources include system logs, API responses, and user interactions"]}
{"type":"entity","name":"Real-time Aggregation","entityType":"WorkflowStep","observations":["Second step of the Monitoring Workflow","Processes and aggregates collected metrics in real-time","Prepares data for visualization and analysis"]}
{"type":"entity","name":"Performance Tracking","entityType":"WorkflowStep","observations":["Fourth step of the Monitoring Workflow","Monitors system performance over time","Identifies trends, anomalies, and optimization opportunities"]}
{"type":"entity","name":"Modular Architecture","entityType":"DesignPattern","observations":["Architectural pattern that emphasizes separation of concerns","Promotes extensibility by isolating components","Allows independent development and testing of modules","Reduces system complexity through well-defined interfaces","Supports maintainability and code reusability"]}
{"type":"entity","name":"Observer Pattern","entityType":"DesignPattern","observations":["Behavioral design pattern for monitoring and metrics collection","Defines a one-to-many dependency between objects","Enables automatic notification when state changes occur","Useful for implementing distributed event handling systems","Supports loose coupling between components"]}
{"type":"entity","name":"Pipeline Pattern","entityType":"DesignPattern","observations":["Architectural pattern for content processing workflow","Organizes processing steps in a sequential chain","Enables modular data transformation and processing","Supports parallel processing of independent pipeline stages","Facilitates debugging and testing of individual components"]}
{"type":"entity","name":"Configuration Pattern","entityType":"DesignPattern","observations":["Design pattern for flexible system behavior","Enables runtime configuration without code changes","Supports environment-specific settings management","Allows customization of system behavior through external files","Promotes separation of configuration from implementation"]}
{"type":"entity","name":"Concurrent Requests","entityType":"PerformanceConcept","observations":["Performance concept focused on parallel processing","Enables multiple requests to be processed simultaneously","Improves system throughput and resource utilization","Implemented through asynchronous processing patterns","Essential for handling high-volume web crawling operations"]}
{"type":"entity","name":"Rate Limiting","entityType":"PerformanceConcept","observations":["Performance concept focused on respectful crawling","Controls the frequency of requests to target servers","Prevents overwhelming target systems with too many requests","Implemented through configurable delay settings","Essential for maintaining good relationships with website owners"]}
{"type":"entity","name":"Memory Efficiency","entityType":"PerformanceConcept","observations":["Performance concept focused on resource optimization","Minimizes memory consumption during processing","Optimizes data structures and processing algorithms","Reduces garbage collection overhead","Essential for long-running crawling operations"]}
{"type":"entity","name":"Analysis Speed","entityType":"PerformanceConcept","observations":["Performance concept focused on processing time optimization","Targets 2-5ms per item processing time","Optimizes content analysis and classification algorithms","Balances speed with accuracy in content processing","Measured and monitored through the Monitoring System"]}
{"type":"entity","name":"Error Handling","entityType":"ArchitecturalConcept","observations":["Concept: Error Handling (graceful degradation)","Description: Mechanisms to handle system failures gracefully without complete system shutdown","Implementation Details: Implement circuit breakers, fallback mechanisms, and retry policies","Relevant Observations: Ensures system availability even during partial failures"]}
{"type":"entity","name":"Database Persistence","entityType":"ArchitecturalConcept","observations":["Concept: Database Persistence (reliable storage)","Description: Strategies for reliable data storage and retrieval with durability guarantees","Implementation Details: Use ACID-compliant databases, implement backup and recovery procedures","Relevant Observations: Critical for data integrity and system reliability"]}
{"type":"entity","name":"Real-time Monitoring","entityType":"ArchitecturalConcept","observations":["Concept: Real-time Monitoring (operational visibility)","Description: Continuous observation of system performance and health metrics","Implementation Details: Implement logging, metrics collection, and alerting systems","Relevant Observations: Enables proactive issue detection and performance optimization","Live statistics updates every 30 seconds"]}
{"type":"entity","name":"Configuration Flexibility","entityType":"ArchitecturalConcept","observations":["Concept: Configuration Flexibility (deployment adaptability)","Description: Ability to adapt system behavior through external configuration without code changes","Implementation Details: Use environment variables, configuration files, and feature flags","Relevant Observations: Supports multiple deployment environments and easy system tuning"]}
{"type":"entity","name":"SpigaMonde Web Interface","entityType":"WebInterfaceComponent","observations":["Modern web-based control panel for SpigaMonde operations"]}
{"type":"entity","name":"FastAPI Backend","entityType":"WebInterfaceComponent","observations":["RESTful API server providing SpigaMonde integration"]}
{"type":"entity","name":"Frontend Dashboard","entityType":"WebInterfaceComponent","observations":["HTML/CSS/JS interface with real-time monitoring"]}
{"type":"entity","name":"Dual-Mode Scripts Tab","entityType":"WebInterfaceComponent","observations":["Toggle between URL-based commands and standalone scripts"]}
{"type":"entity","name":"Virtual Environment Manager","entityType":"Web Interface Component","observations":["UV-based dependency and execution management"]}
{"type":"entity","name":"API Endpoints","entityType":"Technical Architecture","observations":["12 REST endpoints for stats, scripts, health, reset operations"]}
{"type":"entity","name":"Script Execution Engine","entityType":"Technical Architecture","observations":["Subprocess-based execution with UV environment isolation"]}
{"type":"entity","name":"Cache-Busting System","entityType":"Technical Architecture","observations":["Version-controlled asset loading for development"]}
{"type":"entity","name":"CORS Integration","entityType":"Technical Architecture","observations":["Cross-origin support for frontend-backend communication"]}
{"type":"entity","name":"UV Package Manager","entityType":"Tool","observations":["Modern Python dependency manager used by SpigaMonde"]}
{"type":"entity","name":"Virtual Environment Isolation","entityType":"System Component","observations":["Ensures web UI runs in same environment as SpigaMonde"]}
{"type":"entity","name":"Script Mode Toggle","entityType":"Feature","observations":["Command Mode (URL-based) vs Script Mode (standalone execution)"]}
{"type":"entity","name":"Error Handling System","entityType":"System Component","observations":["Comprehensive logging and user feedback mechanisms"]}
{"type":"entity","name":"Virtual Environment Mismatch","entityType":"Issue","observations":["Incompatibility between development and execution environments"]}
{"type":"entity","name":"Script Execution Failures","entityType":"Issue","observations":["Failures that occur when running scripts in the web interface"]}
{"type":"entity","name":"UV Environment Isolation","entityType":"Solution","observations":["Using UV to create consistent execution environments"]}
{"type":"entity","name":"Import and Segmentation Errors","entityType":"Issue","observations":["Errors related to module imports and code segmentation"]}
{"type":"entity","name":"Proper Subprocess Commands","entityType":"Solution","observations":["Correctly formatted subprocess commands for execution"]}
{"type":"entity","name":"Access Violations","entityType":"Issue","observations":["Security-related access errors during script execution"]}
{"type":"entity","name":"Unicode Handling","entityType":"Solution","observations":["Proper handling of Unicode characters in text processing"]}
{"type":"entity","name":"Windows Console Encoding Issues","entityType":"Issue","observations":["Character encoding problems specific to Windows console environments"]}
{"type":"entity","name":"Technical Specifications","entityType":"SystemConfiguration","observations":["Port Configuration: Backend (8000), Frontend (3000)","Supported Script Types: CLI Commands, Web Scripts, Examples, Templates","API Response Format: JSON with timestamps and status indicators","Error Handling: Comprehensive logging with user-friendly messages","Performance: Sub-second response times for most operations"]}
{"type":"entity","name":"Critical Learnings","entityType":"DevelopmentGuidelines","observations":["Environment Isolation: Must use 'uv run' for all script execution","Startup Commands: Specific UV-based commands required for proper operation","Unicode Compatibility: Windows console requires ASCII-safe output","Development Workflow: Cache-busting and auto-reload for rapid iteration"]}
{"type":"entity","name":"Script Management","entityType":"OperationalCapability","observations":["Execute both URL-based crawls and standalone scripts"]}
{"type":"entity","name":"System Administration","entityType":"OperationalCapability","observations":["Database reset, download cleanup, health monitoring"]}
{"type":"entity","name":"Development Support","entityType":"OperationalCapability","observations":["Enhanced logging, error reporting, troubleshooting guides"]}
{"type":"entity","name":"SpigaMonde System","entityType":"System","observations":["The complete SpigaMonde web crawling and content analysis system","Integrates web crawling, content analysis, database storage, and monitoring components","Provides both CLI and Web Interface access"]}
{"type":"entity","name":"Web Interface Backend","entityType":"WebInterfaceComponent","observations":["Backend component of the SpigaMonde Web Interface","Handles API requests and communicates with the core system components","Implemented using FastAPI"]}
{"type":"entity","name":"Web Interface Script Engine","entityType":"WebInterfaceComponent","observations":["Script execution engine for the SpigaMonde Web Interface","Responsible for running user scripts in an isolated environment","Uses UV for virtual environment management"]}
{"type":"entity","name":"Web Interface Settings","entityType":"WebInterfaceComponent","observations":["Settings management component of the SpigaMonde Web Interface","Provides UI for configuring system parameters","Manages user preferences and system configurations"]}
{"type":"entity","name":"Environment Consistency","entityType":"Critical Success Factor","observations":["Web UI must share SpigaMonde's UV environment"]}
{"type":"entity","name":"Proper Startup Sequence","entityType":"Critical Success Factor","observations":["Specific commands prevent common failures"]}
{"type":"entity","name":"Script Categorization","entityType":"Critical Success Factor","observations":["Clear separation between command and script modes"]}
{"type":"entity","name":"Error Prevention","entityType":"Critical Success Factor","observations":["Comprehensive troubleshooting documentation"]}
{"type":"entity","name":"Development Workflow","entityType":"Critical Success Factor","observations":["Cache-busting and real-time updates for productivity"]}
{"type":"entity","name":"index.html","entityType":"FrontendFile","observations":["Main HTML structure for SpigaMonde Web Interface located at web/frontend/index.html","Contains tab-based navigation with Dashboard, Analytics, Scripts, System, and Logs tabs","Includes header with mode toggle button, status bar, and main content areas","Implements responsive design with container layout and grid systems","Serves as the entry point for the web interface with script and CSS imports"]}
{"type":"entity","name":"app-simple.js","entityType":"FrontendFile","observations":["JavaScript application logic file located at web/frontend/app-simple.js","Handles API calls to backend endpoints including /api/health, /api/spiga/stats, /api/execute-script","Manages event listeners for buttons, tabs, and script execution controls","Implements real-time status updates and progress indicators","Contains functions for tab switching, mode toggling, and script management"]}
{"type":"entity","name":"style.css","entityType":"FrontendFile","observations":["CSS stylesheet for UI styling located at web/frontend/style.css","Provides responsive design with flexbox and grid layouts","Includes gradient backgrounds, hover effects, and smooth transitions","Defines styles for tabs, buttons, cards, and status indicators","Implements dark/light mode support through CSS classes and variables"]}
{"type":"entity","name":"Dashboard Tab","entityType":"FrontendComponent","observations":["Primary tab in SpigaMonde Web Interface for system overview and statistics","Located in web/frontend/index.html with data-tab='dashboard'","Displays quick stats, dashboard overview, and quick actions sections","Includes 'Get Statistics' button to fetch data from /api/spiga/stats endpoint","Provides real-time updates on content counts, analysis progress, and system health"]}
{"type":"entity","name":"Scripts Tab","entityType":"FrontendComponent","observations":["Tab for script execution with dual-mode interface (Command vs Script modes)","Located in web/frontend/index.html with data-tab='scripts'","Contains mode toggle buttons, script dropdowns, parameter inputs, and execute buttons","Integrates with /api/available-scripts and /api/execute-script backend endpoints","Provides execution status, progress indicators, and results display"]}
{"type":"entity","name":"Reset Tab","entityType":"FrontendComponent","observations":["System reset functionality within the System tab for cleanup operations","Located in web/frontend/index.html under data-tab='system' section","Includes reset button with options to clear database, downloads, and logs","Calls /api/reset-system endpoint with configuration options","Provides feedback on reset operations and summary of actions performed"]}
{"type":"entity","name":"Logs Tab","entityType":"FrontendComponent","observations":["Tab for real-time activity logging and monitoring display","Located in web/frontend/index.html with data-tab='logs'","Displays chronological log entries with timestamps and message types","Includes clear log button to reset the activity display","Shows system events, user actions, and API call tracking"]}
{"type":"entity","name":"Mode Toggle Buttons","entityType":"FrontendComponent","observations":["UI buttons for switching between Command Mode and Script Mode in the Scripts tab","Located in web/frontend/index.html with IDs commandModeBtn and scriptModeBtn","Command Mode button (🔧) for URL-based crawl commands with icon and label","Script Mode button (🐍) for standalone Python scripts with icon and label","Triggers mode switching via JavaScript event listeners and API calls to /api/mode"]}
{"type":"entity","name":"Script Dropdowns","entityType":"FrontendComponent","observations":["Dropdown select elements for script selection in both Command and Script modes","Command Mode dropdown (scriptSelect) with predefined script options like Basic Web Crawl","Script Mode dropdown (standaloneScriptSelect) dynamically populated from /api/available-scripts","Located in web/frontend/index.html within script-controls sections","OnChange events trigger parameter display and execution button enablement"]}
{"type":"entity","name":"Execute Buttons","entityType":"FrontendComponent","observations":["Buttons to trigger script execution via API calls to /api/execute-script","Command Mode execute button (executeScriptBtn) with rocket icon and 'Execute Script' text","Script Mode execute button (executeStandaloneBtn) with play icon and 'Execute Script' text","Includes loading spinners and disabled states during execution","Located in web/frontend/index.html within script-actions sections"]}
{"type":"entity","name":"Status Indicators","entityType":"FrontendComponent","observations":["Real-time feedback UI elements showing execution progress and results","Includes scriptStatus for text status updates (e.g., 'Ready to execute')","scriptProgress bar with percentage fill and detailed progress messages","scriptResults area for displaying execution output and summaries","Located in web/frontend/index.html within scripts-section for execution status"]}
{"type":"entity","name":"/api/health","entityType":"API Endpoint","observations":["Health check endpoint for monitoring system connectivity and status","Located in web/backend/main.py:app.get at line 970-1001","Performs quick database ping to verify database connection","Returns JSON response with status: healthy/unhealthy, timestamp, and service status","Uses SQLAlchemy session to execute SELECT 1 query for database health check","Handles exceptions and returns 503 status code if health check fails","Logs debug level messages for health check requests"]}
{"type":"entity","name":"/api/spiga/stats","entityType":"API Endpoint","observations":["Database statistics and content counts endpoint for SpigaMonde system","Located in web/backend/main.py:get_spigamonde_stats at line 138-202","Provides real-time statistics from the database including content counts, analysis counts, and recent activity","Calculates analysis coverage percentage and latest crawl session information","Returns comprehensive stats with total_content, analyzed_content, recent_content_24h, completed_content","Includes latest activity data with last crawl timestamp and URL","Handles database exceptions and returns error responses with 500 status code on failure"]}
{"type":"entity","name":"/api/available-scripts","entityType":"API Endpoint","observations":["Script discovery and categorization endpoint for available SpigaMonde scripts","Located in web/backend/main.py:get_available_scripts at line 419-507","Scans multiple directories to discover available scripts: cli_commands, web_scripts, example_scripts, template_scripts","Returns structured JSON with script categories and metadata including name, description, path, and type","Handles script discovery from web/scripts, scripts/examples, and scripts/templates directories","Provides total count of available scripts across all categories","Includes error handling for directory access issues and returns 500 status code on failure"]}
{"type":"entity","name":"/api/execute-script","entityType":"API Endpoint","observations":["Script execution endpoint with subprocess management for SpigaMonde scripts","Located in web/backend/main.py:execute_script at line 510-675","Handles POST requests with script configuration including script_type, target_url, and execution parameters","Supports multiple script types: basic-crawl, web_scripts, example_scripts, template_scripts","Uses UV-based execution with environment isolation for running Python scripts","Executes scripts in background threads with 5-minute timeout to avoid blocking","Returns immediate response with execution_id, command details, and estimated duration","Includes comprehensive error handling for script not found, execution failures, and timeouts"]}
{"type":"entity","name":"/api/reset-system","entityType":"API Endpoint","observations":["Database and download cleanup endpoint for system reset operations","Located in web/backend/main.py:reset_system at line 678-826","Handles POST requests with reset options: clear_database, clear_downloads, clear_logs","Performs atomic database operations to delete all content and analysis records","Clears download directories including ./downloads, ./test_downloads, and storage base path","Optionally clears log files from ./logs directory with size calculations","Returns detailed operation results with success/error status for each reset operation","Handles partial successes and provides comprehensive error reporting"]}
{"type":"entity","name":"/api/mode","entityType":"API Endpoint","observations":["Configuration mode detection endpoint for SpigaMonde system","Located in web/backend/main.py:get_current_mode at line 306-356","Determines current operating mode based on user agent configuration","Returns mode information: testing (TestCrawler) or production (SpigaMonde) with icons and descriptions","Includes configuration details: user_agent, storage_path, max_pages, download_delay","Provides toggle functionality via /api/mode/toggle endpoint (line 359-416)","Helps identify system configuration state for monitoring and management"]}
{"type":"entity","name":"/api/logs/recent","entityType":"API Endpoint","observations":["Log file access and streaming endpoint for retrieving recent log entries","Located in web/backend/main.py:get_recent_logs at line 1004-1064","Accepts optional lines parameter to specify number of log lines to retrieve (default: 50)","Searches for log files in common locations: spigamonde.log, logs/spigamonde.log, logs/app.log","Returns last N lines from log file with file path, lines requested, and lines returned","Handles cases where no log file is found by returning backend memory logs","Includes error handling for file read issues and returns 500 status code on failure","Provides real-time log access for monitoring and debugging purposes"]}
{"type":"entity","name":"Backend Main","entityType":"FastAPI Application","observations":["Primary FastAPI application serving as the web interface backend for SpigaMonde","Located in web/backend/main.py with comprehensive API endpoint definitions","Initializes SpigaMonde components on startup including database connection and logging","Configures CORS middleware for development with allowed origins for localhost","Integrates with SpigaMonde modules: config.settings, database.connection, models.content, monitoring.logger","Uses UVicorn server for development with auto-reload capability","Provides system health checks, statistics, script execution, mode management, and log access","Handles exceptions globally and returns structured error responses with HTTP status codes"]}
{"type":"entity","name":"Subprocess Execution Engine","entityType":"Execution System","observations":["UV-based script execution system with environment isolation for SpigaMonde","Integrated within /api/execute-script endpoint in web/backend/main.py","Executes scripts using subprocess.run with UV to manage Python environments","Supports timeout management (300 seconds/5 minutes) to prevent hanging processes","Runs scripts in background threads to avoid blocking the main API server","Handles multiple script types: CLI commands, web scripts, example scripts, template scripts","Captures and logs stdout and stderr outputs for monitoring and debugging","Provides execution IDs for tracking and immediate response with estimated duration"]}
{"type":"entity","name":"SpigaMonde CLI","entityType":"Software Module","observations":["Command-line interface for SpigaMonde built with Click framework","Provides commands for database initialization, URL crawling, content listing, statistics, and monitoring","Integrates with database connection, settings system, logging, and spider engine","Supports debug mode and custom configuration files","Includes real-time monitoring dashboard and alert system integration","Executed by web backend for automated crawling operations"]}
{"type":"entity","name":"Database Connection","entityType":"Software Module","observations":["Manages database connections and sessions using SQLAlchemy ORM","Includes DatabaseManager class for initialization, table creation, and dropping","Supports SQLite with foreign key constraints enabled via PRAGMA","Provides session scopes with transactional management and error handling","Configurable connection pooling for non-SQLite databases","Global instance pattern with thread-safe session management"]}
{"type":"entity","name":"Content Models","entityType":"Software Module","observations":["Defines SQLAlchemy ORM models for web crawling data storage","Includes models for URL, Content, CrawlSession, and ContentAnalysis","Uses enumerations for content status, type, category, and quality scoring","Implements relationships between URLs and content items","Includes indexes for efficient querying and deduplication","Supports content hashing for duplicate detection and metadata storage"]}
{"type":"entity","name":"Settings System","entityType":"Software Module","observations":["Configuration management using Pydantic Settings and BaseSettings","Supports environment variables and .env file configuration","Includes nested settings for database, spider, logging, storage, and monitoring","Provides type-safe configuration with validation and default values","Global settings instance with reload capability for dynamic configuration changes","Database settings with URL, connection pooling, and SQLite-specific optimizations"]}
{"type":"entity","name":"Logging System","entityType":"Software Module","observations":["Enhanced structured logging with Loguru framework","Supports correlation IDs, session IDs, and spider name context for traceability","Includes console logging with rich formatting and JSON file logging for structured data","Performance logging decorator for timing and monitoring operations","Separate log files for errors and performance metrics","Context manager for setting logging context variables dynamically"]}
{"type":"entity","name":"Spider Engine","entityType":"Software Module","observations":["Core crawling functionality built on Scrapy framework","Discovers and downloads web content including documents, images, videos, and more","Integrates with database for URL tracking, content storage, and session management","Includes content type detection, file size filtering, and duplicate detection","Supports configurable depth limits, concurrent requests, and download delays","Performs content analysis and stores results with quality scoring and metadata extraction"]}
{"type":"entity","name":"Web Scripts","entityType":"Script Category","observations":["Location: web/scripts/","Purpose: Lightweight scripts for web interface","Contains simple, fast-executing scripts for web UI integration","Designed for quick testing and HTTP-based checks"]}
{"type":"entity","name":"test_crawl_simple.py","entityType":"Script File","observations":["Location: web/scripts/test_crawl_simple.py","Purpose: Basic SpigaMonde integration test","Execution Time: ~10 seconds","Resource Usage: Minimal (just logging and database check)","Tests database connection, settings loading, and basic functionality","Suitable for web interface due to low resource requirements"]}
{"type":"entity","name":"simple_news_check.py","entityType":"Script File","observations":["Location: web/scripts/simple_news_check.py","Purpose: HTTP-based news source checker","Execution Time: ~15 seconds","Resource Usage: Low (HTTP requests only, no Scrapy)","Checks news sources via simple HTTP requests","Validates content presence and accessibility","Includes BBC News, Reuters, and AP News sources"]}
{"type":"entity","name":"Example Scripts","entityType":"Script Category","observations":["Location: scripts/examples/","Purpose: Complex SpigaMonde examples demonstrating advanced features","Includes full-featured crawlers with monitoring, analysis, and reporting","Designed for production-like scenarios and educational purposes"]}
{"type":"entity","name":"news_headline_aggregator.py","entityType":"Script File","observations":["Location: scripts/examples/news_headline_aggregator.py","Purpose: News aggregation with Scrapy for Asia and Latin America markets","Features: Multi-source crawling, headline extraction, economic metadata parsing","Includes HTML dashboard generation with live updates and export options","Uses BeautifulSoup for enhanced HTML parsing and RSS feed processing"]}
{"type":"entity","name":"robust_economic_news.py","entityType":"Script File","observations":["Location: scripts/examples/robust_economic_news.py","Purpose: Economic news crawler with network resilience and fallback sources","Features: Tested RSS feeds, error handling, retries, content validation","Includes economic and regional keyword filtering for relevance scoring","Generates HTML dashboard with regional and source statistics"]}
{"type":"entity","name":"Template Scripts","entityType":"Script Category","observations":["Location: scripts/templates/","Purpose: Customizable script templates for SpigaMonde","Provides starting points for custom crawlers and scripts","Includes configuration sections and customization helpers"]}
{"type":"entity","name":"basic_crawl_template.py","entityType":"Script File","observations":["Location: scripts/templates/basic_crawl_template.py","Purpose: Basic template for creating custom crawlers","Features: Configurable target URLs, crawl parameters, analysis focus, output options","Includes customization helpers for content filtering and post-processing","Designed to be copied and modified for specific use cases"]}
{"type":"entity","name":"CLI Commands","entityType":"Script Category","observations":["Location: spigamonde/cli.py","Purpose: Direct SpigaMonde command execution via command-line interface","Provides comprehensive command-line control over the spider system","Includes commands for crawling, monitoring, content management, and system administration"]}
{"type":"entity","name":"spiga init","entityType":"CLI Command","observations":["Purpose: Initialize or reset the database","Usage: spiga init [--reset]","Initializes database schema, optionally resets all tables","Essential for first-time setup and database maintenance"]}
{"type":"entity","name":"spiga crawl","entityType":"CLI Command","observations":["Purpose: Start crawling specified URLs","Usage: spiga crawl <urls> [--depth] [--max-pages] [--delay] [--output-dir] [--file-types] [--concurrent]","Configurable depth, page limits, download delays, and concurrency","Supports multiple seed URLs and file type filtering"]}
{"type":"entity","name":"spigamonde.db","entityType":"DatabaseFile","observations":["Primary SQLite database for SpigaMonde system","Stores crawled content, analysis results, and metadata","Located in the project root directory","Uses SQLAlchemy ORM for data management","Contains tables for URLs, Content, CrawlSession, and ContentAnalysis"]}
{"type":"entity","name":"downloads/","entityType":"StorageDirectory","observations":["Production download directory for crawled content","Stores HTML pages, documents, images, and other web content","Organized by domain and crawl session","Used for persistent storage of downloaded files","Configurable path in system settings"]}
{"type":"entity","name":"test_downloads/","entityType":"StorageDirectory","observations":["Testing download directory for development and testing","Separate from production downloads to avoid contamination","Used for experimental crawls and validation testing","Cleared regularly during testing cycles","Helps maintain data integrity in production environment"]}
{"type":"entity","name":"Console Output","entityType":"LogLocation","observations":["Real-time backend logs displayed in terminal or console","Shows system events, errors, and debug information","Uses Loguru for structured logging with rich formatting","Includes timestamps, log levels, and contextual data","Essential for debugging and monitoring backend operations"]}
{"type":"entity","name":"Browser Console","entityType":"LogLocation","observations":["Frontend JavaScript logs in web browser developer tools","Displays client-side errors, warnings, and debug messages","Used for troubleshooting frontend functionality and API calls","Includes console.log, console.error, and console.warn outputs","Helpful for identifying frontend issues and user interactions"]}
{"type":"entity","name":"Activity Log","entityType":"LogLocation","observations":["Web interface action tracking for user activities","Records API calls, button clicks, and navigation events","Displayed in the Logs tab of the web interface","Provides chronological record of user interactions","Used for auditing and understanding user behavior"]}
{"type":"entity","name":"SpigaMonde Logs","entityType":"LogLocation","observations":["Core system logging for SpigaMonde operations","Structured logging with Loguru framework","Includes error logs, performance metrics, and system events","Stored in log files with rotation and retention policies","Used for debugging, monitoring, and auditing system behavior"]}
{"type":"entity","name":"UV Environment","entityType":"VirtualEnvironment","observations":["UV-based Python virtual environment for dependency management","Located in project root directory (I:\\SpigaMonde)","Manages all project dependencies through uv.lock and pyproject.toml","Provides isolated execution environment for SpigaMonde core and web interface","Ensures consistent dependency versions across development and production"]}
{"type":"entity","name":"Project Root Directory","entityType":"FileSystemLocation","observations":["Base directory for all SpigaMonde operations at I:\\SpigaMonde","Contains core application code, configuration files, and virtual environment","Holds pyproject.toml, uv.lock, .env files, and main application modules","Serves as the working directory for CLI operations and script execution","Root of the project structure with spigamonde/, scripts/, web/, and tests/ directories"]}
{"type":"entity","name":"Working Directory Management","entityType":"SystemComponent","observations":["Manages current working directory for backend operations","Ensures scripts execute from project root directory (I:\\SpigaMonde)","Handles directory changes on application startup and script execution","Integrates with UV environment for consistent execution context","Prevents path-related issues by maintaining proper working directory"]}
{"type":"entity","name":"Environment Variables","entityType":"ConfigurationSystem","observations":["Manages application configuration through environment variables","Uses .env files for local development with .env.example as template","Supports multiple environments: .env.production, .env.testing","Integrated with Pydantic Settings for type-safe configuration","Handles database, spider, logging, storage, and monitoring settings"]}
{"type":"entity","name":"Backend Startup","entityType":"SystemEvent","observations":["Initializes SpigaMonde database connection using init_database()","Sets up logging system and ensures proper working directory","Executed as part of FastAPI startup event in web/backend/main.py","Changes working directory to project root for consistent execution","Logs startup success and errors for monitoring"]}
{"type":"entity","name":"Script Categorization System","entityType":"SystemComponent","observations":["Scans script directories and categorizes scripts by type","Handles discovery of CLI commands, web scripts, example scripts, and template scripts","Integrated in /api/available-scripts endpoint","Provides structured JSON output with script metadata"]}
{"type":"entity","name":"System Health","entityType":"SystemComponent","observations":["Monitors database connectivity and overall system status","Tested by /api/health endpoint via database ping","Returns health status including database connection state","Part of system monitoring and reliability"]}
{"type":"entity","name":"Script Parameters","entityType":"DataStructure","observations":["Contains script type, target URL, and execution parameters","Sent from Web UI to API endpoint","Used to configure script execution"]}
{"type":"entity","name":"Script Results","entityType":"DataOutput","observations":["Output generated by script execution","Includes stdout, stderr, and return code","Captured by subprocess and returned to frontend"]}
{"type":"entity","name":"Execution Status","entityType":"SystemState","observations":["Indicates the current state of script execution","Includes status like running, completed, failed","Displayed in frontend status indicators"]}
{"type":"entity","name":"User Actions","entityType":"SystemComponent","observations":["Actions performed by users in the SpigaMonde system","Includes UI interactions, command executions, and configuration changes"]}
{"type":"entity","name":"API Calls","entityType":"SystemComponent","observations":["REST API requests made to the SpigaMonde backend","Includes both internal and external API interactions","Used for system communication and data exchange"]}
{"type":"entity","name":"Script Execution","entityType":"SystemComponent","observations":["Execution of Python scripts within the SpigaMonde system","Includes both web interface script execution and CLI command execution","Managed by the Subprocess Execution Engine"]}
{"type":"entity","name":"Database Operations","entityType":"SystemComponent","observations":["Database transactions and operations performed by the SpigaMonde system","Includes CRUD operations, queries, and data management tasks","Handled by the Database Layer component"]}
{"type":"entity","name":"File Operations","entityType":"SystemComponent","observations":["File system operations performed by the SpigaMonde system","Includes file creation, deletion, reading, and writing","Used for content storage, configuration management, and log handling"]}
{"type":"entity","name":"Session scope","entityType":"SystemComponent","observations":["Defines the boundaries and context of database sessions","Manages transaction isolation and data consistency","Part of the database management system"]}
{"type":"entity","name":"Transaction management","entityType":"SystemComponent","observations":["Handles database transaction processing and ACID compliance","Manages commit, rollback, and transaction states","Ensures data integrity during concurrent operations"]}
{"type":"entity","name":"Backend console output","entityType":"LogLocation","observations":["Real-time backend logs displayed in terminal or console","Shows system events, errors, and debug information from the backend","Used for debugging and monitoring backend operations"]}
{"type":"entity","name":"SpigaMonde logging system","entityType":"SystemComponent","observations":["Core system logging for SpigaMonde operations","Structured logging with Loguru framework","Includes error logs, performance metrics, and system events"]}
{"type":"entity","name":"Download directory management","entityType":"SystemComponent","observations":["Manages organization and storage of downloaded content","Handles file placement in appropriate directories","Controls access and permissions for download directories"]}
{"type":"entity","name":"User","entityType":"Actor","observations":["Selects script in frontend dropdown"]}
{"type":"entity","name":"Frontend","entityType":"System","observations":["Displays script selection dropdown","Calls /api/available-scripts to populate options"]}
{"type":"entity","name":"API_AvailableScripts","entityType":"Endpoint","observations":["Returns list of available scripts for frontend dropdown"]}
{"type":"entity","name":"ScriptConfiguration","entityType":"Data","observations":["Contains user-configured parameters for script execution"]}
{"type":"entity","name":"ExecuteButton","entityType":"UIElement","observations":["Triggers script execution when clicked by user"]}
{"type":"entity","name":"API_ExecuteScript","entityType":"Endpoint","observations":["Receives POST requests with script configuration","Returns execution results to frontend"]}
{"type":"entity","name":"Backend","entityType":"System","observations":["Validates script type and parameters","Constructs uv run python script.py command"]}
{"type":"entity","name":"VirtualEnvironment","entityType":"Environment","observations":["Isolated Python environment for script execution"]}
{"type":"entity","name":"Subprocess","entityType":"Process","observations":["Executes script in proper virtual environment"]}
{"type":"entity","name":"SpigaMondeDatabase","entityType":"Database","observations":["Stores and provides access to core functionality data"]}
{"type":"entity","name":"ScriptResults","entityType":"Data","observations":["Captured output from script execution","Returned to frontend for display"]}
{"type":"entity","name":"ExecutionStatus","entityType":"Data","observations":["Status information about script execution progress"]}
{"type":"entity","name":"Frontend Auto-refresh Timer","entityType":"Component","observations":["Triggers statistics refresh every 30 seconds","Implemented with JavaScript setInterval function","Controlled by auto-refresh checkbox in Analytics tab","Located in web/frontend/app-simple.js"]}
{"type":"entity","name":"JavaScript Stats API Call","entityType":"Process","observations":["JavaScript function that calls /api/spiga/stats endpoint","Part of refreshDetailedStats function in app-simple.js","Uses fetch API to make HTTP GET request","Handles success and error responses"]}
{"type":"entity","name":"Backend Database Session","entityType":"Component","observations":["Database session opened using session_scope context manager","Implemented in web/backend/main.py","Uses SpigaMonde database connection system","Provides transactional access to database"]}
{"type":"entity","name":"Content and Analysis Queries","entityType":"Process","observations":["Queries executed on Content and ContentAnalysis tables","Retrieves total content count, analyzed content count","Gets recent content from last 24 hours","Calculates analysis coverage percentage"]}
{"type":"entity","name":"JSON Statistics Response","entityType":"DataStructure","observations":["JSON response containing counts and timestamps","Includes total_content, analyzed_content, recent_content_24h","Contains analysis_coverage percentage","Has latest_activity information with last crawl timestamp and URL"]}
{"type":"entity","name":"Frontend Dashboard Update","entityType":"Process","observations":["Updates dashboard displays with new statistics","Uses updateAnalyticsDisplay function in app-simple.js","Populates analytics-stat widgets with new values","Updates last refresh time indicator"]}
{"type":"entity","name":"30-second Refresh Cycle","entityType":"Process","observations":["Process repeats every 30 seconds","Controlled by setInterval with 30000ms delay","Can be enabled/disabled via auto-refresh checkbox","Managed by statsRefreshInterval variable"]}
{"type":"entity","name":"SpigaMonde Modules","entityType":"SoftwareModule","observations":["Core application modules become importable","Located in spigamonde/ directory","Include spider, database, analysis components"]}
{"type":"entity","name":"Virtual Environment","entityType":"Environment","observations":["All operations share same environment","Managed by UV package manager","Provides isolated execution context"]}
{"type":"entity","name":"Import Failure Prevention","entityType":"SystemBenefit","observations":["Prevents import failures through consistent environment","Eliminates segmentation faults","Ensures reliable module access"]}
{"type":"entity","name":"Entry Point","entityType":"FrontendComponent","observations":["File location: web/frontend/index.html","Main UI structure","Serves as the entry point for the frontend application","Contains the basic HTML structure with header, main content area, and footer","Includes references to CSS and JavaScript files with version parameters for cache busting"]}
{"type":"entity","name":"Application Logic","entityType":"FrontendComponent","observations":["File location: web/frontend/app-simple.js","Handles API calls, event handling, and application state management","Contains functions for fetching data from backend services","Implements event listeners for user interactions","Manages the display of content in the UI"]}
{"type":"entity","name":"Styling","entityType":"FrontendComponent","observations":["File location: web/frontend/style.css","Provides responsive design for different screen sizes","Defines component styling for UI elements","Uses CSS Grid and Flexbox for layout","Includes media queries for responsive behavior"]}
{"type":"entity","name":"API Server","entityType":"Component","observations":["FastAPI application located at web/backend/main.py","Main entry point for the SpigaMonde Web Interface backend","Handles HTTP requests and responses","Implements RESTful API endpoints"]}
{"type":"entity","name":"web/backend/main.py","entityType":"File","observations":["Contains the FastAPI application implementation","Implements startup event handler for initialization","Defines API endpoints for the web interface","Located in the web/backend directory"]}
{"type":"entity","name":"Startup Logic","entityType":"Component","observations":["Responsible for initializing SpigaMonde components on startup","Manages working directory changes","Initializes database connections","Handles error during startup process"]}
{"type":"entity","name":"Endpoint Handlers","entityType":"Component","observations":["Individual functions for each API endpoint","Handle specific routes and HTTP methods","Implement business logic for each endpoint","Return structured JSON responses"]}
{"type":"entity","name":"Subprocess Management","entityType":"Component","observations":["Handles execution of external scripts and commands","Uses UV-based script execution engine","Manages script execution timeouts","Captures and processes script output"]}
{"type":"entity","name":"scripts/web_test_script.py","entityType":"File","observations":["Web interface test script","Demonstrates SpigaMonde operations that can be triggered from the web interface","Performs various operations and returns structured results","Located in the scripts directory"]}
{"type":"entity","name":"DatabaseConnection","entityType":"IntegrationPoint","observations":["File location: spigamonde/database/connection.py","Provides database connection and session management","Used by backend for database access","Contains DatabaseManager class with methods for initialization, table creation, and session management","Exports global db_manager instance and helper functions like get_session() and session_scope()"]}
{"type":"entity","name":"SettingsManager","entityType":"IntegrationPoint","observations":["File location: spigamonde/config/settings.py","Manages application configuration using pydantic-settings","Provides settings for database, spider, logging, storage, and monitoring","Uses environment variables for configuration","Exports global settings instance and get_settings() function"]}
{"type":"entity","name":"StructuredLogger","entityType":"IntegrationPoint","observations":["File location: spigamonde/monitoring/logger.py","Provides enhanced structured logging capabilities","Uses loguru for logging implementation","Supports correlation tracking with context variables","Includes performance logging and structured JSON formatting","Exports structured_logger instance and setup_logging() function"]}
{"type":"entity","name":"CommandLineInterface","entityType":"IntegrationPoint","observations":["File location: spigamonde/cli.py","Provides command-line interface for SpigaMonde","Built with click framework","Includes commands for database initialization, crawling, content listing, monitoring, and statistics","Integrates with all other components (database, settings, logging, monitoring)","Main entry point for the application"]}
{"type":"entity","name":"Web Scripts Directory","entityType":"ScriptLocation","observations":["Location: web/scripts/","Contains lightweight, web-optimized scripts","Used for quick execution in web interface","Simple and fast-executing scripts"]}
{"type":"entity","name":"Core Examples Directory","entityType":"ScriptLocation","observations":["Location: scripts/examples/","Contains full SpigaMonde functionality examples","Complex scripts demonstrating advanced features","Production-like scenarios and educational purposes"]}
{"type":"entity","name":"Templates Directory","entityType":"ScriptLocation","observations":["Location: scripts/templates/","Contains customizable script patterns","Starting points for custom crawlers","Includes configuration sections and customization helpers"]}
{"type":"entity","name":"CLI Commands Location","entityType":"ScriptLocation","observations":["Location: spigamonde/cli.py","Direct SpigaMonde command execution","Provides comprehensive command-line control","Includes commands for crawling, monitoring, content management"]}
{"type":"relation","from":"Spider","to":"Database","relationType":"stores_crawled_content"}
{"type":"relation","from":"Spider","to":"Content Analysis","relationType":"triggers_analysis"}
{"type":"relation","from":"Content Analysis","to":"Database","relationType":"stores_analysis_results"}
{"type":"relation","from":"Monitoring System","to":"Database","relationType":"reads metrics and statistics"}
{"type":"relation","from":"Real-time Metrics","to":"Dashboard Display","relationType":"feeds_data_to"}
{"type":"relation","from":"Dashboard Display","to":"User Monitoring","relationType":"presents_data_to"}
{"type":"relation","from":"Custom Download Directories","to":"File Organization","relationType":"connects to"}
{"type":"relation","from":"File Organization","to":"Content Storage","relationType":"connects to"}
{"type":"relation","from":"Depth Limiting","to":"Crawl Scope","relationType":"defines"}
{"type":"relation","from":"Crawl Scope","to":"Resource Management","relationType":"affects"}
{"type":"relation","from":"Delay Settings","to":"Respectful Crawling","relationType":"implements"}
{"type":"relation","from":"Respectful Crawling","to":"Server Load Management","relationType":"enables"}
{"type":"relation","from":"Crawling Workflow","to":"URL Input","relationType":"starts_with"}
{"type":"relation","from":"URL Input","to":"Robots.txt Check","relationType":"followed_by"}
{"type":"relation","from":"Robots.txt Check","to":"Content Download","relationType":"followed_by"}
{"type":"relation","from":"Content Download","to":"Link Extraction","relationType":"followed_by"}
{"type":"relation","from":"Link Extraction","to":"Content Analysis Step","relationType":"followed_by"}
{"type":"relation","from":"Content Analysis Step","to":"Database Storage","relationType":"followed_by"}
{"type":"relation","from":"Crawling Workflow","to":"Database Storage","relationType":"ends_with"}
{"type":"relation","from":"Analysis Workflow","to":"Content Reception","relationType":"hasStep"}
{"type":"relation","from":"Analysis Workflow","to":"Text Extraction","relationType":"hasStep"}
{"type":"relation","from":"Analysis Workflow","to":"Classification","relationType":"hasStep"}
{"type":"relation","from":"Analysis Workflow","to":"Quality Assessment","relationType":"hasStep"}
{"type":"relation","from":"Analysis Workflow","to":"Metadata Extraction","relationType":"hasStep"}
{"type":"relation","from":"Analysis Workflow","to":"Result Storage","relationType":"hasStep"}
{"type":"relation","from":"Content Reception","to":"Text Extraction","relationType":"precedes"}
{"type":"relation","from":"Text Extraction","to":"Classification","relationType":"precedes"}
{"type":"relation","from":"Classification","to":"Quality Assessment","relationType":"precedes"}
{"type":"relation","from":"Quality Assessment","to":"Metadata Extraction","relationType":"precedes"}
{"type":"relation","from":"Metadata Extraction","to":"Result Storage","relationType":"precedes"}
{"type":"relation","from":"Monitoring Workflow","to":"Metrics Collection","relationType":"hasStep"}
{"type":"relation","from":"Monitoring Workflow","to":"Real-time Aggregation","relationType":"hasStep"}
{"type":"relation","from":"Monitoring Workflow","to":"Dashboard Display","relationType":"hasStep"}
{"type":"relation","from":"Monitoring Workflow","to":"Performance Tracking","relationType":"hasStep"}
{"type":"relation","from":"Concurrent Requests","to":"Spider System","relationType":"optimizes"}
{"type":"relation","from":"Rate Limiting","to":"Spider System","relationType":"constrains"}
{"type":"relation","from":"Memory Efficiency","to":"Content Analysis Engine","relationType":"enhances"}
{"type":"relation","from":"Analysis Speed","to":"Content Analysis Engine","relationType":"measures"}
{"type":"relation","from":"Concurrent Requests","to":"Database Layer","relationType":"affects"}
{"type":"relation","from":"Memory Efficiency","to":"Database Layer","relationType":"optimizes"}
{"type":"relation","from":"Analysis Speed","to":"Monitoring System","relationType":"monitored_by"}
{"type":"relation","from":"SpigaMonde Web Interface","to":"SpigaMonde Core","relationType":"INTEGRATES_WITH"}
{"type":"relation","from":"FastAPI Backend","to":"SpigaMonde Database","relationType":"PROVIDES_API_FOR"}
{"type":"relation","from":"Frontend Dashboard","to":"FastAPI Backend","relationType":"COMMUNICATES_WITH"}
{"type":"relation","from":"Script Execution Engine","to":"SpigaMonde CLI Commands","relationType":"EXECUTES"}
{"type":"relation","from":"UV Package Manager","to":"Web Interface","relationType":"MANAGES_ENVIRONMENT_FOR"}
{"type":"relation","from":"Web Interface","to":"UV Virtual Environment","relationType":"REQUIRES"}
{"type":"relation","from":"Script Execution","to":"Virtual Environment Isolation","relationType":"DEPENDS_ON"}
{"type":"relation","from":"Backend API","to":"SpigaMonde Database","relationType":"ACCESSES"}
{"type":"relation","from":"Frontend","to":"Cache-Busting for Development","relationType":"DEPENDS_ON"}
{"type":"relation","from":"Dual-Mode Scripts","to":"Both URL-based and Standalone Execution","relationType":"ENABLES"}
{"type":"relation","from":"Virtual Environment Mismatch","to":"Script Execution Failures","relationType":"CAUSES"}
{"type":"relation","from":"UV Environment Isolation","to":"Import and Segmentation Errors","relationType":"SOLVES"}
{"type":"relation","from":"Proper Subprocess Commands","to":"Access Violations","relationType":"PREVENTS"}
{"type":"relation","from":"Unicode Handling","to":"Windows Console Encoding Issues","relationType":"RESOLVES"}
{"type":"relation","from":"Web Interface","to":"SpigaMonde Statistics","relationType":"PROVIDES_REAL_TIME"}
{"type":"relation","from":"Dashboard","to":"Content Counts","relationType":"DISPLAYS"}
{"type":"relation","from":"Dashboard","to":"Analysis Results","relationType":"DISPLAYS"}
{"type":"relation","from":"Dashboard","to":"System Health","relationType":"DISPLAYS"}
{"type":"relation","from":"Scripts Tab","to":"News Aggregators","relationType":"EXECUTES"}
{"type":"relation","from":"Scripts Tab","to":"Crawl Commands","relationType":"EXECUTES"}
{"type":"relation","from":"Scripts Tab","to":"Test Scripts","relationType":"EXECUTES"}
{"type":"relation","from":"Activity Log","to":"All User Actions and API Calls","relationType":"TRACKS"}
{"type":"relation","from":"Reset System","to":"Database and Download Cleanup","relationType":"MANAGES"}
{"type":"relation","from":"SpigaMonde System","to":"SpigaMonde Web Interface","relationType":"HAS_WEB_INTERFACE"}
{"type":"relation","from":"Database Layer","to":"Web Interface Backend","relationType":"ACCESSED_BY"}
{"type":"relation","from":"CLI Interface","to":"Web Interface Script Engine","relationType":"EXECUTED_BY"}
{"type":"relation","from":"Configuration System","to":"Web Interface Settings","relationType":"MANAGED_BY"}
{"type":"relation","from":"Content Crawling","to":"Web Dashboard","relationType":"MONITORED_BY"}
{"type":"relation","from":"Analysis Results","to":"Web Statistics","relationType":"DISPLAYED_IN"}
{"type":"relation","from":"Error Handling","to":"Web Activity Log","relationType":"LOGGED_TO"}
{"type":"relation","from":"System Health","to":"Web Health Endpoint","relationType":"REPORTED_BY"}
{"type":"relation","from":"SpigaMonde Development","to":"Web Interface Development","relationType":"INCLUDES"}
{"type":"relation","from":"Testing Workflow","to":"Web Interface Testing Tools","relationType":"ENHANCED_BY"}
{"type":"relation","from":"Documentation","to":"Web Interface Setup and Troubleshooting","relationType":"COVERS"}
{"type":"relation","from":"Virtual Environment","to":"Both Core and Web Interface","relationType":"CRITICAL_FOR"}
{"type":"relation","from":"spigamonde.db","to":"Database Layer","relationType":"is_primary_storage_for"}
{"type":"relation","from":"downloads/","to":"Spider System","relationType":"stores_downloaded_content_for"}
{"type":"relation","from":"test_downloads/","to":"Spider System","relationType":"stores_test_downloads_for"}
{"type":"relation","from":"Console Output","to":"Logging System","relationType":"displays_real_time_logs_from"}
{"type":"relation","from":"Browser Console","to":"Web Interface Frontend","relationType":"provides_client_side_logging_for"}
{"type":"relation","from":"Activity Log","to":"Logs Tab","relationType":"is_displayed_in"}
{"type":"relation","from":"SpigaMonde Logs","to":"Logging System","relationType":"is_core_logging_for"}
{"type":"relation","from":"downloads/","to":"Content Storage","relationType":"is_physical_storage_for"}
{"type":"relation","from":"test_downloads/","to":"Content Storage","relationType":"is_test_storage_for"}
{"type":"relation","from":"spigamonde.db","to":"Content Models","relationType":"stores_data_for"}
{"type":"relation","from":"UV Environment","to":"Configuration System","relationType":"provides_execution_environment_for"}
{"type":"relation","from":"Project Root Directory","to":"Spider System","relationType":"contains_source_code_for"}
{"type":"relation","from":"Project Root Directory","to":"Database Layer","relationType":"contains_database_files_for"}
{"type":"relation","from":"Project Root Directory","to":"Content Analysis Engine","relationType":"contains_analysis_modules_for"}
{"type":"relation","from":"Working Directory Management","to":"Web Interface Script Engine","relationType":"manages_execution_context_for"}
{"type":"relation","from":"Working Directory Management","to":"Script Execution Engine","relationType":"ensures_proper_working_directory_for"}
{"type":"relation","from":"Environment Variables","to":"Configuration System","relationType":"provides_configuration_data_to"}
{"type":"relation","from":"Environment Variables","to":"Settings System","relationType":"feeds_configuration_to"}
{"type":"relation","from":"Dashboard Tab","to":"/api/health","relationType":"CALLS"}
{"type":"relation","from":"Dashboard Tab","to":"/api/spiga/stats","relationType":"CALLS"}
{"type":"relation","from":"Scripts Tab","to":"/api/available-scripts","relationType":"CALLS"}
{"type":"relation","from":"Scripts Tab","to":"/api/execute-script","relationType":"CALLS"}
{"type":"relation","from":"Reset Tab","to":"/api/reset-system","relationType":"CALLS"}
{"type":"relation","from":"Logs Tab","to":"/api/logs/recent","relationType":"CALLS"}
{"type":"relation","from":"Mode Toggle Buttons","to":"Script Dropdowns","relationType":"TRIGGERS_SCRIPT_CATEGORIZATION_AND_UI_UPDATES"}
{"type":"relation","from":"Execute Buttons","to":"/api/execute-script","relationType":"SEND_POST_TO"}
{"type":"relation","from":"Status Indicators","to":"Web Interface Backend","relationType":"RECEIVE_FROM"}
{"type":"relation","from":"/api/spiga/stats","to":"Database Layer","relationType":"QUERIES"}
{"type":"relation","from":"/api/execute-script","to":"Script Execution Engine","relationType":"EXECUTES"}
{"type":"relation","from":"/api/available-scripts","to":"Script Categorization System","relationType":"SCANS"}
{"type":"relation","from":"/api/reset-system","to":"Database Layer","relationType":"CALLS"}
{"type":"relation","from":"/api/reset-system","to":"File Organization","relationType":"CALLS"}
{"type":"relation","from":"/api/health","to":"Database Layer","relationType":"TESTS"}
{"type":"relation","from":"/api/health","to":"System Health","relationType":"TESTS"}
{"type":"relation","from":"Backend Startup","to":"Database Layer","relationType":"INITIALIZES"}
{"type":"relation","from":"Backend Startup","to":"Logging System","relationType":"INITIALIZES"}
{"type":"relation","from":"Scripts Tab","to":"/api/execute-script","relationType":"SENDS_SCRIPT_PARAMETERS"}
{"type":"relation","from":"Subprocess Execution Engine","to":"UV Environment","relationType":"EXECUTES_WITH_UV"}
{"type":"relation","from":"Script Execution Engine","to":"Database Layer","relationType":"ACCESSES"}
{"type":"relation","from":"Scripts Tab","to":"Script Parameters","relationType":"SENDS"}
{"type":"relation","from":"Script Parameters","to":"/api/execute-script","relationType":"DELIVERED_TO"}
{"type":"relation","from":"Subprocess Execution Engine","to":"UV Environment","relationType":"EXECUTES_WITH"}
{"type":"relation","from":"Subprocess Execution Engine","to":"UV Environment","relationType":"EXECUTES_UV_RUN_PYTHON_SCRIPT"}
{"type":"relation","from":"Script Execution Engine","to":"Configuration System","relationType":"ACCESSES"}
{"type":"relation","from":"Script Execution Engine","to":"Logging System","relationType":"ACCESSES"}
{"type":"relation","from":"Subprocess Execution Engine","to":"Script Results","relationType":"CAPTURES"}
{"type":"relation","from":"Script Results","to":"Frontend Dashboard","relationType":"RETURNED_TO"}
{"type":"relation","from":"Frontend Dashboard","to":"Execution Status","relationType":"DISPLAYS"}
{"type":"relation","from":"Frontend Dashboard","to":"Script Results","relationType":"DISPLAYS"}
{"type":"relation","from":"User Actions","to":"Browser Console","relationType":"LOGGED_TO"}
{"type":"relation","from":"User Actions","to":"Activity Log","relationType":"LOGGED_TO"}
{"type":"relation","from":"API Calls","to":"Backend console output","relationType":"LOGGED_TO"}
{"type":"relation","from":"Script Execution","to":"SpigaMonde logging system","relationType":"LOGGED_TO"}
{"type":"relation","from":"Database Operations","to":"Session scope","relationType":"TRACKED_BY"}
{"type":"relation","from":"Database Operations","to":"Transaction management","relationType":"TRACKED_BY"}
{"type":"relation","from":"File Operations","to":"Download directory management","relationType":"MONITORED_BY"}
{"type":"relation","from":"Frontend Errors","to":"UI error messages and console","relationType":"DISPLAYED_IN"}
{"type":"relation","from":"API Errors","to":"HTTP status codes with error details","relationType":"RETURNED_AS"}
{"type":"relation","from":"Script Failures","to":"Subprocess stderr and logged","relationType":"CAPTURED_BY"}
{"type":"relation","from":"Database Errors","to":"Session rollback and error reporting","relationType":"HANDLED_BY"}
{"type":"relation","from":"Environment Issues","to":"Virtual environment validation","relationType":"DIAGNOSED_BY"}
{"type":"relation","from":"User","to":"Frontend","relationType":"interacts_with"}
{"type":"relation","from":"Frontend","to":"API_AvailableScripts","relationType":"calls"}
{"type":"relation","from":"User","to":"ScriptConfiguration","relationType":"configures"}
{"type":"relation","from":"User","to":"ExecuteButton","relationType":"clicks"}
{"type":"relation","from":"Frontend","to":"API_ExecuteScript","relationType":"sends_post_request"}
{"type":"relation","from":"API_ExecuteScript","to":"Backend","relationType":"forwards_to"}
{"type":"relation","from":"Backend","to":"VirtualEnvironment","relationType":"uses"}
{"type":"relation","from":"Backend","to":"Subprocess","relationType":"creates"}
{"type":"relation","from":"Subprocess","to":"SpigaMondeDatabase","relationType":"accesses"}
{"type":"relation","from":"Subprocess","to":"ScriptResults","relationType":"generates"}
{"type":"relation","from":"ScriptResults","to":"API_ExecuteScript","relationType":"returned_to"}
{"type":"relation","from":"API_ExecuteScript","to":"Frontend","relationType":"sends_response"}
{"type":"relation","from":"Frontend Auto-refresh Timer","to":"JavaScript Stats API Call","relationType":"TRIGGERS"}
{"type":"relation","from":"JavaScript Stats API Call","to":"Backend Database Session","relationType":"INITIATES"}
{"type":"relation","from":"Backend Database Session","to":"Content and Analysis Queries","relationType":"EXECUTES"}
{"type":"relation","from":"Content and Analysis Queries","to":"JSON Statistics Response","relationType":"GENERATES"}
{"type":"relation","from":"JSON Statistics Response","to":"Frontend Dashboard Update","relationType":"TRIGGERS"}
{"type":"relation","from":"Frontend Dashboard Update","to":"30-second Refresh Cycle","relationType":"COMPLETES"}
{"type":"relation","from":"30-second Refresh Cycle","to":"Frontend Auto-refresh Timer","relationType":"RESTARTS"}
{"type":"relation","from":"Backend Startup","to":"Working Directory Management","relationType":"triggers"}
{"type":"relation","from":"Working Directory Management","to":"SpigaMonde Modules","relationType":"enables_import_of"}
{"type":"relation","from":"SpigaMonde Modules","to":"Script Execution","relationType":"required_for"}
{"type":"relation","from":"Script Execution","to":"Virtual Environment","relationType":"uses"}
{"type":"relation","from":"Virtual Environment","to":"Import Failure Prevention","relationType":"provides"}
{"type":"relation","from":"Entry Point","to":"Application Logic","relationType":"loads"}
{"type":"relation","from":"Entry Point","to":"Styling","relationType":"loads"}
{"type":"relation","from":"API Server","to":"web/backend/main.py","relationType":"is implemented in"}
{"type":"relation","from":"API Server","to":"Startup Logic","relationType":"initializes"}
{"type":"relation","from":"API Server","to":"Endpoint Handlers","relationType":"contains"}
{"type":"relation","from":"API Server","to":"Subprocess Management","relationType":"uses"}
{"type":"relation","from":"Startup Logic","to":"web/backend/main.py","relationType":"is implemented in"}
{"type":"relation","from":"Endpoint Handlers","to":"web/backend/main.py","relationType":"are implemented in"}
{"type":"relation","from":"Subprocess Management","to":"scripts/web_test_script.py","relationType":"executes"}
{"type":"relation","from":"DatabaseConnection","to":"SettingsManager","relationType":"depends_on"}
{"type":"relation","from":"StructuredLogger","to":"SettingsManager","relationType":"depends_on"}
{"type":"relation","from":"CommandLineInterface","to":"DatabaseConnection","relationType":"uses"}
{"type":"relation","from":"CommandLineInterface","to":"SettingsManager","relationType":"uses"}
{"type":"relation","from":"CommandLineInterface","to":"StructuredLogger","relationType":"uses"}
{"type":"relation","from":"CommandLineInterface","to":"spigamonde","relationType":"belongs_to"}
{"type":"relation","from":"DatabaseConnection","to":"spigamonde","relationType":"belongs_to"}
{"type":"relation","from":"SettingsManager","to":"spigamonde","relationType":"belongs_to"}
{"type":"relation","from":"StructuredLogger","to":"spigamonde","relationType":"belongs_to"}
{"type":"relation","from":"web/backend","to":"spigamonde","relationType":"uses"}
{"type":"relation","from":"web/backend","to":"DatabaseConnection","relationType":"uses"}
{"type":"relation","from":"web/backend","to":"SettingsManager","relationType":"uses"}
{"type":"relation","from":"Web Scripts Directory","to":"Web Scripts","relationType":"contains_scripts_for"}
{"type":"relation","from":"Core Examples Directory","to":"Example Scripts","relationType":"contains_scripts_for"}
{"type":"relation","from":"Templates Directory","to":"Template Scripts","relationType":"contains_scripts_for"}
{"type":"relation","from":"CLI Commands Location","to":"CLI Commands","relationType":"implements"}
{"type":"relation","from":"Web Scripts Directory","to":"SpigaMonde Web Interface","relationType":"provides_scripts_for"}
{"type":"relation","from":"Core Examples Directory","to":"SpigaMonde System","relationType":"demonstrates_functionality_of"}
{"type":"relation","from":"Templates Directory","to":"Script Execution Engine","relationType":"provides_templates_for"}
{"type":"relation","from":"CLI Commands Location","to":"CommandLineInterface","relationType":"is_main_implementation_of"}