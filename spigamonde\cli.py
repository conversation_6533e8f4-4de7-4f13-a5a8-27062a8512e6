"""Command-line interface for SpigaMonde."""

import json
import os
import sys
from pathlib import Path
from typing import List, Optional

import click
from loguru import logger
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from sqlalchemy import func

from .config.settings import get_settings, reload_settings
from .database.connection import init_database, reset_database, session_scope
from .models.content import Content, ContentStatus, ContentType, CrawlSession, URL
from .monitoring.logger import setup_logging as setup_enhanced_logging, log_context
from .monitoring.dashboard import run_live_dashboard, print_status_summary
from .monitoring.alerts import get_alert_manager, start_alerting, stop_alerting
from .monitoring.metrics import get_metrics_collector


console = Console()


def setup_logging():
    """Setup enhanced logging configuration."""
    # Use the enhanced logging system
    setup_enhanced_logging()

    # Start alerting system if enabled
    settings = get_settings()
    if settings.debug:
        start_alerting()


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug mode')
@click.option('--config', type=click.Path(exists=True), help='Path to configuration file')
@click.pass_context
def cli(ctx, debug, config):
    """SpigaMonde - A powerful web spider for content discovery."""
    ctx.ensure_object(dict)
    
    # Set debug mode
    if debug:
        os.environ['DEBUG'] = 'true'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        reload_settings()
    
    # Setup logging
    setup_logging()
    
    # Store config path in context
    ctx.obj['config'] = config


@cli.command()
@click.option('--reset', is_flag=True, help='Reset database (drop all tables)')
def init(reset):
    """Initialize the database."""
    try:
        if reset:
            console.print("[yellow]Resetting database...[/yellow]")
            reset_database()
            console.print("[green]Database reset successfully![/green]")
        else:
            console.print("[blue]Initializing database...[/blue]")
            init_database()
            console.print("[green]Database initialized successfully![/green]")
    except Exception as e:
        console.print(f"[red]Error initializing database: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('urls', nargs=-1, required=True)
@click.option('--depth', '-d', default=3, help='Maximum crawl depth')
@click.option('--max-pages', '-p', type=int, help='Maximum pages to crawl')
@click.option('--delay', '-t', default=1.0, help='Download delay in seconds')
@click.option('--output-dir', '-o', type=click.Path(), help='Output directory for downloads')
@click.option('--file-types', '-f', help='Comma-separated list of allowed file types')
@click.option('--concurrent', '-c', default=16, help='Number of concurrent requests')
def crawl(urls, depth, max_pages, delay, output_dir, file_types, concurrent):
    """Start crawling the specified URLs."""
    try:
        # Validate URLs
        if not urls:
            console.print("[red]No URLs provided[/red]")
            sys.exit(1)
        
        # Update settings if options provided
        settings = get_settings()
        if output_dir:
            settings.storage.base_path = Path(output_dir)
        if file_types:
            settings.spider.allowed_file_types = [ft.strip() for ft in file_types.split(',')]
        
        settings.spider.max_depth = depth
        settings.spider.download_delay = delay
        settings.spider.concurrent_requests = concurrent
        if max_pages:
            settings.spider.max_pages = max_pages
        
        console.print(f"[blue]Starting crawl with {len(urls)} seed URL(s)...[/blue]")
        console.print(f"Max depth: {depth}")
        console.print(f"Download delay: {delay}s")
        console.print(f"Output directory: {settings.storage.base_path}")
        
        # Import and run spider
        from scrapy.crawler import CrawlerProcess
        from .spiders.content_spider import ContentSpider
        
        # Configure Scrapy process
        process = CrawlerProcess({
            'USER_AGENT': settings.spider.user_agent,
            'ROBOTSTXT_OBEY': settings.spider.robotstxt_obey,
            'CONCURRENT_REQUESTS': concurrent,
            'DOWNLOAD_DELAY': delay,
            'RANDOMIZE_DOWNLOAD_DELAY': settings.spider.randomize_download_delay,
            'DEPTH_LIMIT': depth,
            'LOG_LEVEL': 'INFO',
        })
        
        # Start crawling
        process.crawl(ContentSpider, start_urls=list(urls))
        process.start()
        
        console.print("[green]Crawl completed![/green]")
        
    except Exception as e:
        console.print(f"[red]Error during crawl: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--limit', '-l', default=50, help='Number of results to show')
@click.option('--content-type', '-t', type=click.Choice([ct.value for ct in ContentType]), help='Filter by content type')
@click.option('--status', '-s', type=click.Choice([cs.value for cs in ContentStatus]), help='Filter by status')
@click.option('--downloaded', is_flag=True, help='Show only downloaded content')
def list_content(limit, content_type, status, downloaded):
    """List discovered content."""
    try:
        with session_scope() as session:
            query = session.query(Content)
            
            # Apply filters
            if content_type:
                query = query.filter(Content.content_type == ContentType(content_type))
            if status:
                query = query.filter(Content.status == ContentStatus(status))
            if downloaded:
                query = query.filter(Content.is_downloaded == True)
            
            # Get results
            content_items = query.order_by(Content.created_at.desc()).limit(limit).all()
            
            if not content_items:
                console.print("[yellow]No content found[/yellow]")
                return
            
            # Create table
            table = Table(title=f"Content ({len(content_items)} items)")
            table.add_column("ID", style="cyan")
            table.add_column("Filename", style="green")
            table.add_column("Type", style="blue")
            table.add_column("Status", style="yellow")
            table.add_column("Size", style="magenta")
            table.add_column("Downloaded", style="red")
            
            for item in content_items:
                size_str = f"{item.file_size:,} bytes" if item.file_size else "Unknown"
                downloaded_str = "✓" if item.is_downloaded else "✗"
                
                table.add_row(
                    str(item.id),
                    item.filename or "Unknown",
                    item.content_type.value,
                    item.status.value,
                    size_str,
                    downloaded_str
                )
            
            console.print(table)
            
    except Exception as e:
        console.print(f"[red]Error listing content: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.option('--limit', '-l', default=20, help='Number of sessions to show')
def list_sessions(limit):
    """List crawl sessions."""
    try:
        with session_scope() as session:
            sessions = session.query(CrawlSession).order_by(CrawlSession.created_at.desc()).limit(limit).all()
            
            if not sessions:
                console.print("[yellow]No crawl sessions found[/yellow]")
                return
            
            # Create table
            table = Table(title=f"Crawl Sessions ({len(sessions)} sessions)")
            table.add_column("ID", style="cyan")
            table.add_column("Name", style="green")
            table.add_column("Start Time", style="blue")
            table.add_column("Status", style="yellow")
            table.add_column("Found", style="magenta")
            table.add_column("Downloaded", style="red")
            
            for session_obj in sessions:
                status = "Active" if session_obj.is_active else ("Completed" if session_obj.is_completed else "Stopped")
                
                table.add_row(
                    str(session_obj.id),
                    session_obj.session_name,
                    session_obj.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    status,
                    str(session_obj.content_found),
                    str(session_obj.content_downloaded)
                )
            
            console.print(table)
            
    except Exception as e:
        console.print(f"[red]Error listing sessions: {e}[/red]")
        sys.exit(1)


@cli.command()
def stats():
    """Show crawling statistics."""
    try:
        with session_scope() as session:
            # Get basic counts
            total_urls = session.query(URL).count()
            crawled_urls = session.query(URL).filter(URL.is_crawled == True).count()
            total_content = session.query(Content).count()
            downloaded_content = session.query(Content).filter(Content.is_downloaded == True).count()
            
            # Get content by type
            content_by_type = session.query(
                Content.content_type, 
                func.count(Content.id)
            ).group_by(Content.content_type).all()
            
            # Get content by status
            content_by_status = session.query(
                Content.status, 
                func.count(Content.id)
            ).group_by(Content.status).all()
            
            # Display statistics
            console.print("[bold blue]SpigaMonde Statistics[/bold blue]")
            console.print(f"Total URLs: {total_urls:,}")
            console.print(f"Crawled URLs: {crawled_urls:,}")
            console.print(f"Total Content: {total_content:,}")
            console.print(f"Downloaded Content: {downloaded_content:,}")
            
            if content_by_type:
                console.print("\n[bold]Content by Type:[/bold]")
                for content_type, count in content_by_type:
                    console.print(f"  {content_type.value}: {count:,}")
            
            if content_by_status:
                console.print("\n[bold]Content by Status:[/bold]")
                for status, count in content_by_status:
                    console.print(f"  {status.value}: {count:,}")
            
    except Exception as e:
        console.print(f"[red]Error getting statistics: {e}[/red]")
        sys.exit(1)


@cli.command()
@click.argument('content_id', type=int)
def show_content(content_id):
    """Show detailed information about specific content."""
    try:
        with session_scope() as session:
            content = session.query(Content).filter(Content.id == content_id).first()
            
            if not content:
                console.print(f"[red]Content with ID {content_id} not found[/red]")
                sys.exit(1)
            
            # Display content details
            console.print(f"[bold blue]Content Details (ID: {content_id})[/bold blue]")
            console.print(f"URL: {content.url}")
            console.print(f"Filename: {content.filename or 'Unknown'}")
            console.print(f"Type: {content.content_type.value}")
            console.print(f"Status: {content.status.value}")
            console.print(f"File Size: {content.file_size:,} bytes" if content.file_size else "Unknown")
            console.print(f"MIME Type: {content.mime_type or 'Unknown'}")
            console.print(f"Downloaded: {'Yes' if content.is_downloaded else 'No'}")
            console.print(f"Local Path: {content.local_path or 'Not downloaded'}")
            console.print(f"Created: {content.created_at}")
            console.print(f"Updated: {content.updated_at}")
            
            if content.error_message:
                console.print(f"[red]Error: {content.error_message}[/red]")
            
    except Exception as e:
        console.print(f"[red]Error showing content: {e}[/red]")
        sys.exit(1)


@cli.command()
def config():
    """Show current configuration."""
    settings = get_settings()
    
    console.print("[bold blue]SpigaMonde Configuration[/bold blue]")
    console.print(f"Database URL: {settings.database.url}")
    console.print(f"Storage Path: {settings.storage.base_path}")
    console.print(f"Max Depth: {settings.spider.max_depth}")
    console.print(f"Download Delay: {settings.spider.download_delay}s")
    console.print(f"Concurrent Requests: {settings.spider.concurrent_requests}")
    console.print(f"Max File Size: {settings.spider.max_file_size:,} bytes")
    console.print(f"Allowed File Types: {', '.join(settings.spider.allowed_file_types)}")
    console.print(f"Log Level: {settings.logging.level}")


@cli.command()
@click.option('--duration', type=float, help='Duration to run dashboard (seconds)')
def monitor(duration):
    """Run the real-time monitoring dashboard."""
    try:
        console.print("[bold blue]Starting SpigaMonde Monitoring Dashboard[/bold blue]")
        console.print("Press Ctrl+C to exit")
        run_live_dashboard(duration)
    except KeyboardInterrupt:
        console.print("\n[yellow]Dashboard stopped by user[/yellow]")


@cli.command()
def status():
    """Show current system status and metrics."""
    print_status_summary()


@cli.command()
def alerts():
    """Show current alerts and alert history."""
    alert_manager = get_alert_manager()

    # Active alerts
    active_alerts = alert_manager.get_active_alerts()
    if active_alerts:
        console.print("\n[bold red]Active Alerts[/bold red]")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Alert", style="cyan")
        table.add_column("Severity", style="red")
        table.add_column("Message", style="white")
        table.add_column("Time", style="dim")

        for alert in active_alerts:
            table.add_row(
                alert.name,
                alert.severity.value.upper(),
                alert.message,
                alert.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            )
        console.print(table)
    else:
        console.print("[green]No active alerts[/green]")

    # Alert summary
    summary = alert_manager.get_alert_summary()
    console.print(f"\nAlert Summary:")
    console.print(f"  Active alerts: {summary['active_alerts']}")
    console.print(f"  Total rules: {summary['total_rules']}")
    console.print(f"  Recent alerts (24h): {summary['recent_alerts_24h']}")
    console.print(f"  Monitoring active: {summary['monitoring_active']}")


@cli.command()
def metrics():
    """Show performance metrics and statistics."""
    metrics_collector = get_metrics_collector()
    summary = metrics_collector.get_summary()

    console.print("\n[bold blue]Performance Metrics[/bold blue]")

    # Performance stats
    if summary['performance_stats']:
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Operation", style="cyan")
        table.add_column("Count", justify="right", style="green")
        table.add_column("Avg Duration", justify="right", style="yellow")
        table.add_column("Success Rate", justify="right", style="blue")

        for operation, stats in summary['performance_stats'].items():
            success_rate = f"{stats['success_rate']:.1%}"
            avg_duration = f"{stats['avg_duration']:.3f}s"

            table.add_row(
                operation,
                str(stats['count']),
                avg_duration,
                success_rate
            )

        console.print(table)
    else:
        console.print("[dim]No performance data available[/dim]")

    # Counters and gauges
    if summary['counters']:
        console.print("\n[bold blue]Counters[/bold blue]")
        for name, value in summary['counters'].items():
            console.print(f"  {name}: {value:,}")

    if summary['gauges']:
        console.print("\n[bold blue]Gauges[/bold blue]")
        for name, value in summary['gauges'].items():
            console.print(f"  {name}: {value:.2f}")


def main():
    """Main entry point for the CLI."""
    cli()


if __name__ == '__main__':
    main()
