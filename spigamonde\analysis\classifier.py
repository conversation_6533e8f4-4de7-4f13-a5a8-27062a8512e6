"""Enhanced content classification system with quality scoring."""

import re
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from urllib.parse import urlparse

from ..monitoring.logger import get_logger
from ..monitoring.metrics import PerformanceTimer, record_crawl_metric


class ContentCategory(Enum):
    """Enhanced content categories beyond basic file types."""
    
    # Document categories
    ACADEMIC_PAPER = "academic_paper"
    TECHNICAL_DOCUMENTATION = "technical_documentation"
    LEGAL_DOCUMENT = "legal_document"
    FINANCIAL_REPORT = "financial_report"
    NEWS_ARTICLE = "news_article"
    BLOG_POST = "blog_post"
    
    # Media categories
    EDUCATIONAL_VIDEO = "educational_video"
    ENTERTAINMENT_VIDEO = "entertainment_video"
    PODCAST = "podcast"
    MUSIC = "music"
    ARTWORK = "artwork"
    PHOTOGRAPHY = "photography"
    
    # Data categories
    DATASET = "dataset"
    API_DOCUMENTATION = "api_documentation"
    CODE_REPOSITORY = "code_repository"
    CONFIGURATION_FILE = "configuration_file"
    
    # Web content
    LANDING_PAGE = "landing_page"
    PRODUCT_PAGE = "product_page"
    CONTACT_INFO = "contact_info"
    ABOUT_PAGE = "about_page"
    
    # Other
    UNKNOWN = "unknown"
    LOW_QUALITY = "low_quality"


class QualityScore(Enum):
    """Content quality scoring levels."""
    EXCELLENT = 5
    GOOD = 4
    AVERAGE = 3
    POOR = 2
    VERY_POOR = 1


@dataclass
class ClassificationResult:
    """Result of content classification analysis."""
    category: ContentCategory
    confidence: float  # 0.0 to 1.0
    quality_score: QualityScore
    metadata: Dict[str, Any] = field(default_factory=dict)
    reasoning: List[str] = field(default_factory=list)


class ContentClassifier:
    """Enhanced content classifier with quality scoring."""
    
    def __init__(self):
        self.logger = get_logger()
        self._load_classification_rules()
    
    def _load_classification_rules(self):
        """Load classification rules and patterns."""
        
        # URL patterns for different content types
        self.url_patterns = {
            ContentCategory.ACADEMIC_PAPER: [
                r'arxiv\.org',
                r'\.edu/.*\.pdf',
                r'researchgate\.net',
                r'scholar\.google',
                r'pubmed\.ncbi',
                r'ieee\.org',
                r'acm\.org'
            ],
            ContentCategory.TECHNICAL_DOCUMENTATION: [
                r'docs?\.',
                r'documentation',
                r'api\.',
                r'developer\.',
                r'github\.io',
                r'readthedocs',
                r'confluence'
            ],
            ContentCategory.NEWS_ARTICLE: [
                r'news\.',
                r'\.com/news/',
                r'reuters\.com',
                r'bbc\.com',
                r'cnn\.com',
                r'nytimes\.com',
                r'washingtonpost\.com'
            ],
            ContentCategory.BLOG_POST: [
                r'blog\.',
                r'\.com/blog/',
                r'medium\.com',
                r'wordpress\.com',
                r'blogspot\.com'
            ],
            ContentCategory.CODE_REPOSITORY: [
                r'github\.com',
                r'gitlab\.com',
                r'bitbucket\.org',
                r'sourceforge\.net'
            ]
        }
        
        # File extension patterns
        self.extension_patterns = {
            ContentCategory.ACADEMIC_PAPER: {'.pdf', '.tex', '.bib'},
            ContentCategory.DATASET: {'.csv', '.json', '.xml', '.xlsx', '.tsv'},
            ContentCategory.CONFIGURATION_FILE: {'.yml', '.yaml', '.toml', '.ini', '.conf'},
            ContentCategory.CODE_REPOSITORY: {'.py', '.js', '.java', '.cpp', '.c', '.go', '.rs'}
        }
        
        # Content keywords for classification
        self.content_keywords = {
            ContentCategory.ACADEMIC_PAPER: [
                'abstract', 'introduction', 'methodology', 'results', 'conclusion',
                'references', 'bibliography', 'doi:', 'arxiv:', 'published',
                'journal', 'conference', 'proceedings', 'research', 'study'
            ],
            ContentCategory.TECHNICAL_DOCUMENTATION: [
                'api', 'endpoint', 'parameter', 'response', 'request',
                'authentication', 'authorization', 'sdk', 'library',
                'installation', 'configuration', 'tutorial', 'guide'
            ],
            ContentCategory.NEWS_ARTICLE: [
                'breaking news', 'reported', 'according to', 'sources say',
                'journalist', 'correspondent', 'news desk', 'editor',
                'published on', 'updated', 'reuters', 'associated press'
            ],
            ContentCategory.LEGAL_DOCUMENT: [
                'whereas', 'therefore', 'pursuant to', 'hereby',
                'contract', 'agreement', 'terms and conditions',
                'privacy policy', 'legal notice', 'copyright'
            ]
        }
        
        # Quality indicators
        self.quality_indicators = {
            'positive': [
                'peer-reviewed', 'published', 'official', 'verified',
                'comprehensive', 'detailed', 'authoritative', 'complete',
                'updated', 'current', 'accurate', 'reliable'
            ],
            'negative': [
                'placeholder', 'lorem ipsum', 'coming soon', 'under construction',
                'error 404', 'page not found', 'broken link', 'spam',
                'advertisement', 'click here', 'buy now', 'limited time'
            ]
        }
    
    def classify_content(self, url: str, content: str = None, 
                        file_path: str = None, mime_type: str = None) -> ClassificationResult:
        """Classify content based on URL, content, and metadata."""
        
        with PerformanceTimer("content_classification"):
            try:
                # Initialize classification result
                result = ClassificationResult(
                    category=ContentCategory.UNKNOWN,
                    confidence=0.0,
                    quality_score=QualityScore.AVERAGE
                )
                
                # Analyze URL patterns
                url_category, url_confidence = self._classify_by_url(url)
                if url_confidence > result.confidence:
                    result.category = url_category
                    result.confidence = url_confidence
                    result.reasoning.append(f"URL pattern match: {url}")
                
                # Analyze file extension
                if file_path:
                    ext_category, ext_confidence = self._classify_by_extension(file_path)
                    if ext_confidence > result.confidence:
                        result.category = ext_category
                        result.confidence = ext_confidence
                        result.reasoning.append(f"File extension match: {Path(file_path).suffix}")
                
                # Analyze MIME type
                if mime_type:
                    mime_category, mime_confidence = self._classify_by_mime_type(mime_type)
                    if mime_confidence > result.confidence:
                        result.category = mime_category
                        result.confidence = mime_confidence
                        result.reasoning.append(f"MIME type match: {mime_type}")
                
                # Analyze content if available
                if content:
                    content_category, content_confidence = self._classify_by_content(content)
                    if content_confidence > result.confidence:
                        result.category = content_category
                        result.confidence = content_confidence
                        result.reasoning.append("Content keyword analysis")
                    
                    # Calculate quality score
                    result.quality_score = self._calculate_quality_score(content, url)
                
                # Add metadata
                result.metadata.update({
                    'url_domain': urlparse(url).netloc,
                    'file_extension': Path(file_path).suffix if file_path else None,
                    'mime_type': mime_type,
                    'content_length': len(content) if content else None
                })
                
                # Log classification result
                self.logger.info(
                    f"Content classified: {result.category.value}",
                    url=url,
                    category=result.category.value,
                    confidence=result.confidence,
                    quality_score=result.quality_score.value
                )
                
                # Record metrics
                record_crawl_metric("content_classified", 1, 
                                  category=result.category.value,
                                  quality=result.quality_score.name)
                
                return result
                
            except Exception as e:
                self.logger.error(f"Content classification failed: {e}", url=url)
                return ClassificationResult(
                    category=ContentCategory.UNKNOWN,
                    confidence=0.0,
                    quality_score=QualityScore.AVERAGE,
                    reasoning=[f"Classification error: {str(e)}"]
                )
    
    def _classify_by_url(self, url: str) -> Tuple[ContentCategory, float]:
        """Classify content based on URL patterns."""
        domain = urlparse(url).netloc.lower()
        path = urlparse(url).path.lower()
        full_url = url.lower()
        
        for category, patterns in self.url_patterns.items():
            for pattern in patterns:
                if re.search(pattern, full_url):
                    confidence = 0.7  # High confidence for URL patterns
                    return category, confidence
        
        return ContentCategory.UNKNOWN, 0.0
    
    def _classify_by_extension(self, file_path: str) -> Tuple[ContentCategory, float]:
        """Classify content based on file extension."""
        extension = Path(file_path).suffix.lower()
        
        for category, extensions in self.extension_patterns.items():
            if extension in extensions:
                confidence = 0.6  # Medium confidence for extensions
                return category, confidence
        
        return ContentCategory.UNKNOWN, 0.0
    
    def _classify_by_mime_type(self, mime_type: str) -> Tuple[ContentCategory, float]:
        """Classify content based on MIME type."""
        mime_lower = mime_type.lower()
        
        if 'pdf' in mime_lower:
            return ContentCategory.ACADEMIC_PAPER, 0.5
        elif 'video' in mime_lower:
            return ContentCategory.EDUCATIONAL_VIDEO, 0.5
        elif 'audio' in mime_lower:
            return ContentCategory.PODCAST, 0.5
        elif 'image' in mime_lower:
            return ContentCategory.PHOTOGRAPHY, 0.5
        elif 'text' in mime_lower or 'html' in mime_lower:
            return ContentCategory.BLOG_POST, 0.3
        
        return ContentCategory.UNKNOWN, 0.0
    
    def _classify_by_content(self, content: str) -> Tuple[ContentCategory, float]:
        """Classify content based on text analysis."""
        content_lower = content.lower()
        
        best_category = ContentCategory.UNKNOWN
        best_score = 0.0
        
        for category, keywords in self.content_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in content_lower:
                    score += 1
            
            # Normalize score by number of keywords
            normalized_score = score / len(keywords)
            
            if normalized_score > best_score:
                best_score = normalized_score
                best_category = category
        
        # Convert to confidence (max 0.8 for content analysis)
        confidence = min(best_score * 2, 0.8)
        
        return best_category, confidence
    
    def _calculate_quality_score(self, content: str, url: str) -> QualityScore:
        """Calculate content quality score."""
        content_lower = content.lower()
        url_lower = url.lower()
        
        positive_score = 0
        negative_score = 0
        
        # Check for quality indicators
        for indicator in self.quality_indicators['positive']:
            if indicator in content_lower or indicator in url_lower:
                positive_score += 1
        
        for indicator in self.quality_indicators['negative']:
            if indicator in content_lower or indicator in url_lower:
                negative_score += 1
        
        # Content length factor
        length_factor = 0
        if len(content) > 10000:  # Long content
            length_factor = 2
        elif len(content) > 1000:  # Medium content
            length_factor = 1
        elif len(content) < 100:  # Very short content
            length_factor = -2
        
        # Calculate final score
        total_score = positive_score - negative_score + length_factor
        
        if total_score >= 4:
            return QualityScore.EXCELLENT
        elif total_score >= 2:
            return QualityScore.GOOD
        elif total_score >= 0:
            return QualityScore.AVERAGE
        elif total_score >= -2:
            return QualityScore.POOR
        else:
            return QualityScore.VERY_POOR


def get_content_classifier() -> ContentClassifier:
    """Get the global content classifier instance."""
    return ContentClassifier()
