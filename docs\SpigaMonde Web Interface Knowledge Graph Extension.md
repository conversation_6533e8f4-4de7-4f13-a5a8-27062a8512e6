# SpigaMonde Web Interface Knowledge Graph Extension

You are tasked with Extending a knowledge graph for SpigaMonde Web Interface,
Use the MCP memory server to store and organize this knowledge graph.
This is a complex task that requires a deep understanding of the SpigaMonde Web Interface architecture and functionality.  Breakdown the task into sub tasks and Use sequentialThinking mcp server to store and organize this knowledge graph.  Use other modes as needed. See SPECIFIC INSTRUCTIONS at the end of this prompt.

## ENTITIES TO ADD/UPDATE:

### Web Interface Components
- **SpigaMonde Web Interface**: Modern web-based control panel for SpigaMonde operations
- **FastAPI Backend**: RESTful API server providing SpigaMonde integration
- **Frontend Dashboard**: HTML/CSS/JS interface with real-time monitoring
- **Dual-Mode Scripts Tab**: Toggle between URL-based commands and standalone scripts
- **Virtual Environment Manager**: UV-based dependency and execution management

### Technical Architecture
- **API Endpoints**: 12 REST endpoints for stats, scripts, health, reset operations
- **Script Execution Engine**: Subprocess-based execution with UV environment isolation
- **Real-time Monitoring**: Auto-refresh statistics and activity logging
- **Cache-Busting System**: Version-controlled asset loading for development
- **CORS Integration**: Cross-origin support for frontend-backend communication

### Critical Components
- **UV Package Manager**: Modern Python dependency manager used by SpigaMonde
- **Virtual Environment Isolation**: Ensures web UI runs in same environment as SpigaMonde
- **Script Mode Toggle**: Command Mode (URL-based) vs Script Mode (standalone execution)
- **Error Handling System**: Comprehensive logging and user feedback mechanisms

## RELATIONSHIPS TO ESTABLISH:

### Integration Relationships
- SpigaMonde Web Interface → INTEGRATES_WITH → SpigaMonde Core
- FastAPI Backend → PROVIDES_API_FOR → SpigaMonde Database
- Frontend Dashboard → COMMUNICATES_WITH → FastAPI Backend
- Script Execution Engine → EXECUTES → SpigaMonde CLI Commands
- UV Package Manager → MANAGES_ENVIRONMENT_FOR → Web Interface

### Dependency Relationships
- Web Interface → REQUIRES → UV Virtual Environment
- Script Execution → DEPENDS_ON → Virtual Environment Isolation
- Backend API → ACCESSES → SpigaMonde Database
- Frontend → DEPENDS_ON → Cache-Busting for Development
- Dual-Mode Scripts → ENABLES → Both URL-based and Standalone Execution

### Critical Issue Relationships
- Virtual Environment Mismatch → CAUSES → Script Execution Failures
- UV Environment Isolation → SOLVES → Import and Segmentation Errors
- Proper Subprocess Commands → PREVENTS → Access Violations
- Unicode Handling → RESOLVES → Windows Console Encoding Issues

### Operational Relationships
- Web Interface → PROVIDES_REAL_TIME → SpigaMonde Statistics
- Dashboard → DISPLAYS → Content Counts, Analysis Results, System Health
- Scripts Tab → EXECUTES → News Aggregators, Crawl Commands, Test Scripts
- Activity Log → TRACKS → All User Actions and API Calls
- Reset System → MANAGES → Database and Download Cleanup

## ATTRIBUTES TO CAPTURE:

### Technical Specifications
- **Port Configuration**: Backend (8000), Frontend (3000)
- **Supported Script Types**: CLI Commands, Web Scripts, Examples, Templates
- **API Response Format**: JSON with timestamps and status indicators
- **Error Handling**: Comprehensive logging with user-friendly messages
- **Performance**: Sub-second response times for most operations

### Critical Learnings
- **Environment Isolation**: Must use 'uv run' for all script execution
- **Startup Commands**: Specific UV-based commands required for proper operation
- **Unicode Compatibility**: Windows console requires ASCII-safe output
- **Development Workflow**: Cache-busting and auto-reload for rapid iteration

### Operational Capabilities
- **Real-time Monitoring**: Live statistics updates every 30 seconds
- **Script Management**: Execute both URL-based crawls and standalone scripts
- **System Administration**: Database reset, download cleanup, health monitoring
- **Development Support**: Enhanced logging, error reporting, troubleshooting guides

## INTEGRATION WITH EXISTING SPIGAMONDE GRAPH:

### Extend Core Entities
- SpigaMonde System → HAS_WEB_INTERFACE → SpigaMonde Web Interface
- SpigaMonde Database → ACCESSED_BY → Web Interface Backend
- SpigaMonde CLI → EXECUTED_BY → Web Interface Script Engine
- SpigaMonde Configuration → MANAGED_BY → Web Interface Settings

### Enhance Workflow Relationships
- Content Crawling → MONITORED_BY → Web Dashboard
- Analysis Results → DISPLAYED_IN → Web Statistics
- Error Handling → LOGGED_TO → Web Activity Log
- System Health → REPORTED_BY → Web Health Endpoint

### Add Development Context
- SpigaMonde Development → INCLUDES → Web Interface Development
- Testing Workflow → ENHANCED_BY → Web Interface Testing Tools
- Documentation → COVERS → Web Interface Setup and Troubleshooting
- Virtual Environment → CRITICAL_FOR → Both Core and Web Interface

## CRITICAL SUCCESS FACTORS TO ENCODE:

1. **Environment Consistency**: Web UI must share SpigaMonde's UV environment
2. **Proper Startup Sequence**: Specific commands prevent common failures
3. **Script Categorization**: Clear separation between command and script modes
4. **Error Prevention**: Comprehensive troubleshooting documentation
5. **Development Workflow**: Cache-busting and real-time updates for productivity

This knowledge graph extension captures the web interface as an integral part of SpigaMonde while highlighting the critical architectural decisions and lessons learned during implementation.


### SPECIFIC INSTRUCTIONS

#### CHUNKING STRATEGY (IMPORTANT)
**Use modest-sized chunks to avoid tool use errors:**

- **Store 3-5 entities per memory operation** - Don't try to store too many nodes at once
- **Process one component category at a time** 
- **Use incremental building** - Start with core entities, then add relationships in separate operations
- **Verify each chunk** - Confirm successful storage before proceeding to next chunk
- **If errors occur** - Reduce chunk size and retry with smaller batches