#!/usr/bin/env python3
"""Test SpigaMonde logging configuration."""

import os
from pathlib import Path

# Set environment variable for file logging
os.environ['LOG_FILE_PATH'] = './logs/spigamonde.log'

# Import SpigaMonde components
from spigamonde.monitoring.logger import get_logger, setup_logging

def test_logging():
    """Test logging configuration."""
    
    # Create logs directory
    Path('./logs').mkdir(exist_ok=True)
    
    # Setup logging
    logger = setup_logging()
    
    print("Testing SpigaMonde logging...")
    
    # Test different log levels
    logger.info("This is an info message")
    logger.debug("This is a debug message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Check if log files were created
    log_files = list(Path('./logs').glob('*.log'))
    
    print(f"\nLog files created: {len(log_files)}")
    for log_file in log_files:
        print(f"  - {log_file}")
        
        # Show first few lines of each log file
        if log_file.exists():
            with open(log_file, 'r') as f:
                lines = f.readlines()[:5]
                print(f"    Content preview ({len(lines)} lines):")
                for line in lines:
                    print(f"      {line.strip()}")
    
    return log_files

if __name__ == "__main__":
    test_logging()
