"""Content analysis and metadata extraction system."""

import hashlib
import re
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from urllib.parse import urlparse

from ..monitoring.logger import get_logger
from ..monitoring.metrics import PerformanceTimer, record_crawl_metric
from .classifier import ContentClassifier, ClassificationResult, ContentCategory, QualityScore


@dataclass
class ContentMetadata:
    """Extracted metadata from content analysis."""
    
    # Basic metadata
    title: Optional[str] = None
    description: Optional[str] = None
    author: Optional[str] = None
    language: Optional[str] = None
    
    # Content characteristics
    word_count: int = 0
    character_count: int = 0
    line_count: int = 0
    paragraph_count: int = 0
    
    # Technical metadata
    encoding: Optional[str] = None
    content_hash: Optional[str] = None
    similarity_hash: Optional[str] = None
    
    # Extracted entities
    emails: List[str] = field(default_factory=list)
    urls: List[str] = field(default_factory=list)
    phone_numbers: List[str] = field(default_factory=list)
    dates: List[str] = field(default_factory=list)
    
    # Keywords and topics
    keywords: List[str] = field(default_factory=list)
    topics: List[str] = field(default_factory=list)
    
    # Quality metrics
    readability_score: Optional[float] = None
    complexity_score: Optional[float] = None
    
    # Additional metadata
    custom_fields: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AnalysisResult:
    """Complete content analysis result."""
    classification: ClassificationResult
    metadata: ContentMetadata
    analysis_time: float
    success: bool = True
    error_message: Optional[str] = None


class ContentAnalyzer:
    """Advanced content analyzer with metadata extraction."""
    
    def __init__(self):
        self.logger = get_logger()
        self.classifier = ContentClassifier()
        self._load_analysis_patterns()
    
    def _load_analysis_patterns(self):
        """Load patterns for entity extraction and analysis."""
        
        # Regular expressions for entity extraction
        self.patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'url': re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'),
            'phone': re.compile(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'),
            'date': re.compile(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b'),
            'title_html': re.compile(r'<title[^>]*>([^<]+)</title>', re.IGNORECASE),
            'meta_description': re.compile(r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']', re.IGNORECASE),
            'meta_author': re.compile(r'<meta[^>]*name=["\']author["\'][^>]*content=["\']([^"\']+)["\']', re.IGNORECASE),
            'heading': re.compile(r'<h[1-6][^>]*>([^<]+)</h[1-6]>', re.IGNORECASE)
        }
        
        # Language detection patterns (basic)
        self.language_patterns = {
            'english': [
                r'\bthe\b', r'\band\b', r'\bof\b', r'\bto\b', r'\ba\b',
                r'\bin\b', r'\bis\b', r'\bit\b', r'\byou\b', r'\bthat\b'
            ],
            'spanish': [
                r'\bel\b', r'\bla\b', r'\bde\b', r'\bque\b', r'\by\b',
                r'\ben\b', r'\bun\b', r'\bes\b', r'\bse\b', r'\bno\b'
            ],
            'french': [
                r'\ble\b', r'\bde\b', r'\bet\b', r'\bà\b', r'\bun\b',
                r'\bil\b', r'\bêtre\b', r'\bce\b', r'\bque\b', r'\bse\b'
            ],
            'german': [
                r'\bder\b', r'\bdie\b', r'\bdas\b', r'\bund\b', r'\bin\b',
                r'\bzu\b', r'\bden\b', r'\bist\b', r'\bsie\b', r'\bmit\b'
            ]
        }
        
        # Common stop words for keyword extraction
        self.stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through',
            'during', 'before', 'after', 'above', 'below', 'between',
            'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would',
            'could', 'should', 'may', 'might', 'must', 'can', 'this',
            'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it',
            'we', 'they', 'me', 'him', 'her', 'us', 'them'
        }
    
    def analyze_content(self, url: str, content: str = None, 
                       file_path: str = None, mime_type: str = None) -> AnalysisResult:
        """Perform comprehensive content analysis."""
        
        start_time = datetime.now()
        
        with PerformanceTimer("content_analysis"):
            try:
                # Classify content
                classification = self.classifier.classify_content(
                    url=url, content=content, file_path=file_path, mime_type=mime_type
                )

                # Extract metadata
                metadata = self._extract_metadata(content, url, file_path)

                result = AnalysisResult(
                    classification=classification,
                    metadata=metadata,
                    analysis_time=0.0,  # Will be calculated below
                    success=True
                )

                # Log analysis result
                self.logger.info(
                    f"Content analysis completed",
                    url=url,
                    category=classification.category.value,
                    quality=classification.quality_score.value,
                    word_count=metadata.word_count,
                    language=metadata.language
                )

                # Record metrics
                record_crawl_metric("content_analyzed", 1,
                                  category=classification.category.value,
                                  language=metadata.language or "unknown")

            except Exception as e:
                self.logger.error(f"Content analysis failed: {e}", url=url)

                # Create a default classification if it failed early
                default_classification = ClassificationResult(
                    category=ContentCategory.UNKNOWN,
                    confidence=0.0,
                    quality_score=QualityScore.AVERAGE,
                    reasoning=[f"Analysis error: {str(e)}"]
                )

                result = AnalysisResult(
                    classification=classification if 'classification' in locals() else default_classification,
                    metadata=ContentMetadata(),
                    analysis_time=0.0,  # Will be calculated below
                    success=False,
                    error_message=str(e)
                )

            # Calculate analysis time outside try/catch to ensure it's always set
            analysis_time = (datetime.now() - start_time).total_seconds()
            result.analysis_time = analysis_time

            return result
    
    def _extract_metadata(self, content: str, url: str, file_path: str = None) -> ContentMetadata:
        """Extract comprehensive metadata from content."""
        metadata = ContentMetadata()
        
        if not content:
            return metadata
        
        # Basic content statistics
        metadata.character_count = len(content)
        metadata.line_count = content.count('\n') + 1
        metadata.word_count = len(content.split())
        metadata.paragraph_count = len([p for p in content.split('\n\n') if p.strip()])
        
        # Content hashes
        metadata.content_hash = hashlib.sha256(content.encode()).hexdigest()
        metadata.similarity_hash = self._calculate_similarity_hash(content)
        
        # Extract entities
        metadata.emails = self._extract_entities(content, 'email')
        metadata.urls = self._extract_entities(content, 'url')
        metadata.phone_numbers = self._extract_entities(content, 'phone')
        metadata.dates = self._extract_entities(content, 'date')
        
        # Extract HTML metadata if content appears to be HTML
        if '<html' in content.lower() or '<title' in content.lower():
            self._extract_html_metadata(content, metadata)
        
        # Language detection
        metadata.language = self._detect_language(content)
        
        # Extract keywords
        metadata.keywords = self._extract_keywords(content)
        
        # Calculate readability and complexity
        metadata.readability_score = self._calculate_readability(content)
        metadata.complexity_score = self._calculate_complexity(content)
        
        # Add URL-based metadata
        parsed_url = urlparse(url)
        metadata.custom_fields.update({
            'domain': parsed_url.netloc,
            'path_depth': len([p for p in parsed_url.path.split('/') if p]),
            'has_query_params': bool(parsed_url.query),
            'file_extension': Path(file_path).suffix if file_path else None
        })
        
        return metadata
    
    def _extract_entities(self, content: str, entity_type: str) -> List[str]:
        """Extract specific entities from content."""
        if entity_type not in self.patterns:
            return []
        
        matches = self.patterns[entity_type].findall(content)
        # Remove duplicates while preserving order
        return list(dict.fromkeys(matches))
    
    def _extract_html_metadata(self, content: str, metadata: ContentMetadata):
        """Extract metadata from HTML content."""
        
        # Extract title
        title_match = self.patterns['title_html'].search(content)
        if title_match:
            metadata.title = title_match.group(1).strip()
        
        # Extract meta description
        desc_match = self.patterns['meta_description'].search(content)
        if desc_match:
            metadata.description = desc_match.group(1).strip()
        
        # Extract author
        author_match = self.patterns['meta_author'].search(content)
        if author_match:
            metadata.author = author_match.group(1).strip()
        
        # Extract headings as topics
        headings = self.patterns['heading'].findall(content)
        metadata.topics = [h.strip() for h in headings[:10]]  # Limit to first 10
    
    def _detect_language(self, content: str) -> Optional[str]:
        """Basic language detection using common word patterns."""
        content_lower = content.lower()
        
        language_scores = {}
        
        for language, patterns in self.language_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, content_lower))
                score += matches
            
            # Normalize by content length
            language_scores[language] = score / len(content.split())
        
        if language_scores:
            best_language = max(language_scores, key=language_scores.get)
            if language_scores[best_language] > 0.01:  # Minimum threshold
                return best_language
        
        return None
    
    def _extract_keywords(self, content: str, max_keywords: int = 20) -> List[str]:
        """Extract keywords from content."""
        # Simple keyword extraction based on word frequency
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        
        # Filter out stop words
        filtered_words = [w for w in words if w not in self.stop_words]
        
        # Count word frequency
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top keywords
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:max_keywords]]
    
    def _calculate_similarity_hash(self, content: str) -> str:
        """Calculate a hash for similarity detection (simplified simhash)."""
        # Simple approach: hash of normalized content
        normalized = re.sub(r'\s+', ' ', content.lower().strip())
        return hashlib.md5(normalized.encode()).hexdigest()
    
    def _calculate_readability(self, content: str) -> float:
        """Calculate basic readability score (simplified Flesch Reading Ease)."""
        if not content.strip():
            return 0.0
        
        sentences = len(re.split(r'[.!?]+', content))
        words = len(content.split())
        syllables = self._count_syllables(content)
        
        if sentences == 0 or words == 0:
            return 0.0
        
        # Simplified Flesch Reading Ease formula
        score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words))
        return max(0.0, min(100.0, score))
    
    def _calculate_complexity(self, content: str) -> float:
        """Calculate content complexity score."""
        if not content.strip():
            return 0.0
        
        words = content.split()
        if not words:
            return 0.0
        
        # Factors contributing to complexity
        avg_word_length = sum(len(word) for word in words) / len(words)
        unique_words = len(set(word.lower() for word in words))
        vocabulary_richness = unique_words / len(words)
        
        # Normalize to 0-100 scale
        complexity = (avg_word_length * 10) + (vocabulary_richness * 50)
        return min(100.0, complexity)
    
    def _count_syllables(self, content: str) -> int:
        """Count syllables in content (simplified approach)."""
        words = re.findall(r'\b[a-zA-Z]+\b', content.lower())
        total_syllables = 0
        
        for word in words:
            # Simple syllable counting: count vowel groups
            syllables = len(re.findall(r'[aeiouy]+', word))
            if syllables == 0:
                syllables = 1  # Every word has at least one syllable
            total_syllables += syllables
        
        return total_syllables


def get_content_analyzer() -> ContentAnalyzer:
    """Get the global content analyzer instance."""
    return ContentAnalyzer()
