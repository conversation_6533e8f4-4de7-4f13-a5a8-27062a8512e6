#!/usr/bin/env python3
"""
HTML Page Section Extractor for SpigaMonde
Demonstrates advanced HTML parsing and section-specific content extraction.

Usage:
    python extract_page_sections.py
    
Features:
    - CSS selector-based content extraction
    - Header, footer, navigation, and content section targeting
    - Script and style tag analysis
    - Link extraction by section
    - Social media link detection
    - SEO metadata extraction
    - Custom entity extraction by page section
"""

import sys
import os
import re
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urljoin, urlparse
import threading

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.dashboard import run_live_dashboard
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree

# Enhanced HTML parsing
try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    HAS_BEAUTIFULSOUP = False
    print("Warning: beautifulsoup4 not installed. Install with: pip install beautifulsoup4")
    sys.exit(1)


class PageSectionExtractor:
    """Advanced HTML page section analyzer."""
    
    def __init__(self):
        # Section selectors (CSS selectors and common patterns)
        self.section_selectors = {
            'header': ['header', '.header', '#header', '.site-header', '.page-header'],
            'navigation': ['nav', '.nav', '.navigation', '.menu', '.navbar'],
            'main_content': ['main', '.main', '#main', '.content', '.page-content', 'article'],
            'sidebar': ['.sidebar', '.side-bar', '#sidebar', '.widget-area'],
            'footer': ['footer', '.footer', '#footer', '.site-footer', '.page-footer'],
            'scripts': ['script'],
            'styles': ['style', 'link[rel="stylesheet"]'],
        }
        
        # Social media patterns
        self.social_patterns = {
            'twitter': re.compile(r'twitter\.com/[A-Za-z0-9_]+', re.IGNORECASE),
            'facebook': re.compile(r'facebook\.com/[A-Za-z0-9._]+', re.IGNORECASE),
            'linkedin': re.compile(r'linkedin\.com/(?:in|company)/[A-Za-z0-9-]+', re.IGNORECASE),
            'github': re.compile(r'github\.com/[A-Za-z0-9_-]+', re.IGNORECASE),
            'youtube': re.compile(r'youtube\.com/(?:channel/|user/|c/)?[A-Za-z0-9_-]+', re.IGNORECASE),
            'instagram': re.compile(r'instagram\.com/[A-Za-z0-9_.]+', re.IGNORECASE),
        }
        
        # Email and phone patterns (enhanced)
        self.contact_patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'phone': re.compile(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'),
            'address': re.compile(r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)', re.IGNORECASE),
        }
    
    def analyze_page_structure(self, html_content, url):
        """Comprehensive page structure analysis."""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        analysis = {
            'url': url,
            'title': self._extract_title(soup),
            'meta_data': self._extract_meta_data(soup),
            'sections': {},
            'links_by_section': {},
            'contact_info_by_section': {},
            'social_media': {},
            'scripts_and_styles': {},
            'page_structure': self._analyze_structure(soup),
        }
        
        # Analyze each section
        for section_name, selectors in self.section_selectors.items():
            section_data = self._analyze_section(soup, section_name, selectors)
            if section_data:
                analysis['sections'][section_name] = section_data
                analysis['links_by_section'][section_name] = section_data.get('links', [])
                analysis['contact_info_by_section'][section_name] = section_data.get('contact_info', {})
        
        # Extract social media links
        analysis['social_media'] = self._extract_social_media(soup)
        
        # Analyze scripts and styles
        analysis['scripts_and_styles'] = self._analyze_scripts_styles(soup)
        
        return analysis
    
    def _extract_title(self, soup):
        """Extract page title."""
        title_tag = soup.find('title')
        return title_tag.get_text().strip() if title_tag else None
    
    def _extract_meta_data(self, soup):
        """Extract meta tags and SEO data."""
        meta_data = {}
        
        # Standard meta tags
        meta_tags = soup.find_all('meta')
        for tag in meta_tags:
            name = tag.get('name') or tag.get('property')
            content = tag.get('content')
            if name and content:
                meta_data[name] = content
        
        # Open Graph and Twitter Card data
        og_tags = soup.find_all('meta', property=re.compile(r'^og:'))
        for tag in og_tags:
            prop = tag.get('property')
            content = tag.get('content')
            if prop and content:
                meta_data[prop] = content
        
        return meta_data
    
    def _analyze_section(self, soup, section_name, selectors):
        """Analyze a specific page section."""
        section_elements = []
        
        # Find elements using CSS selectors
        for selector in selectors:
            elements = soup.select(selector)
            section_elements.extend(elements)
        
        if not section_elements:
            return None
        
        # Combine text from all section elements
        section_text = ' '.join(elem.get_text() for elem in section_elements)
        
        section_data = {
            'element_count': len(section_elements),
            'text_length': len(section_text),
            'word_count': len(section_text.split()),
            'links': self._extract_links_from_elements(section_elements),
            'contact_info': self._extract_contact_info(section_text),
            'headings': self._extract_headings_from_elements(section_elements),
        }
        
        return section_data
    
    def _extract_links_from_elements(self, elements):
        """Extract links from HTML elements."""
        links = []
        for element in elements:
            link_tags = element.find_all('a', href=True)
            for link in link_tags:
                href = link.get('href')
                text = link.get_text().strip()
                if href:
                    links.append({
                        'url': href,
                        'text': text,
                        'is_external': self._is_external_link(href)
                    })
        return links
    
    def _extract_contact_info(self, text):
        """Extract contact information from text."""
        contact_info = {}
        
        for info_type, pattern in self.contact_patterns.items():
            matches = pattern.findall(text)
            if matches:
                contact_info[info_type] = list(set(matches))  # Remove duplicates
        
        return contact_info
    
    def _extract_headings_from_elements(self, elements):
        """Extract headings from elements."""
        headings = []
        for element in elements:
            heading_tags = element.find_all(re.compile(r'^h[1-6]$'))
            for heading in heading_tags:
                headings.append({
                    'level': heading.name,
                    'text': heading.get_text().strip()
                })
        return headings
    
    def _extract_social_media(self, soup):
        """Extract social media links."""
        social_links = {}
        
        # Find all links
        all_links = soup.find_all('a', href=True)
        
        for platform, pattern in self.social_patterns.items():
            platform_links = []
            for link in all_links:
                href = link.get('href')
                if href and pattern.search(href):
                    platform_links.append({
                        'url': href,
                        'text': link.get_text().strip()
                    })
            
            if platform_links:
                social_links[platform] = platform_links
        
        return social_links
    
    def _analyze_scripts_styles(self, soup):
        """Analyze scripts and stylesheets."""
        analysis = {
            'external_scripts': [],
            'inline_scripts': [],
            'external_styles': [],
            'inline_styles': [],
        }
        
        # External scripts
        script_tags = soup.find_all('script', src=True)
        for script in script_tags:
            analysis['external_scripts'].append(script.get('src'))
        
        # Inline scripts
        inline_scripts = soup.find_all('script', src=False)
        for script in inline_scripts:
            content = script.get_text()
            if content.strip():
                analysis['inline_scripts'].append({
                    'length': len(content),
                    'content_preview': content[:100] + '...' if len(content) > 100 else content
                })
        
        # External stylesheets
        link_tags = soup.find_all('link', rel='stylesheet', href=True)
        for link in link_tags:
            analysis['external_styles'].append(link.get('href'))
        
        # Inline styles
        style_tags = soup.find_all('style')
        for style in style_tags:
            content = style.get_text()
            if content.strip():
                analysis['inline_styles'].append({
                    'length': len(content),
                    'content_preview': content[:100] + '...' if len(content) > 100 else content
                })
        
        return analysis
    
    def _analyze_structure(self, soup):
        """Analyze overall page structure."""
        return {
            'total_elements': len(soup.find_all()),
            'div_count': len(soup.find_all('div')),
            'span_count': len(soup.find_all('span')),
            'link_count': len(soup.find_all('a')),
            'image_count': len(soup.find_all('img')),
            'form_count': len(soup.find_all('form')),
            'table_count': len(soup.find_all('table')),
        }
    
    def _is_external_link(self, href):
        """Check if link is external."""
        return href.startswith('http') and not href.startswith('#')


def main():
    """Execute page section analysis."""
    console = Console()
    
    # Display banner
    console.print(Panel.fit(
        "[bold purple]SpigaMonde Page Section Extractor[/bold purple]\n"
        "Advanced HTML parsing and section-specific analysis",
        border_style="purple"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Target URLs for section analysis
    target_urls = [
        "https://www.python.org/",  # Well-structured site
        "https://docs.python.org/3/",  # Documentation site
        # Add your target URLs here
    ]
    
    # Configure crawl
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Section Extractor 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.0,
        'CONCURRENT_REQUESTS': 2,
        'DEPTH_LIMIT': 1,  # Focus on specific pages
        'CLOSESPIDER_PAGECOUNT': 10,
        'LOG_LEVEL': 'INFO',
        'ALLOWED_FILE_TYPES': ['html', 'htm'],
    }
    
    console.print(f"[cyan]Analyzing page sections from {len(target_urls)} URL(s)...[/cyan]")
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=target_urls)
        process.start()
        
        console.print("[green]✓ Section analysis completed![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Analysis error: {e}[/red]")
        return 1
    
    # Demonstrate section analysis on crawled content
    demonstrate_section_analysis(console)
    
    return 0


def demonstrate_section_analysis(console):
    """Demonstrate advanced section analysis on crawled content."""
    
    with session_scope() as session:
        # Get recent HTML content
        html_content = session.query(Content).filter(
            Content.filename.like('%.html')
        ).first()
        
        if not html_content or not html_content.local_path:
            console.print("[yellow]No HTML content found to analyze.[/yellow]")
            return
        
        # Read the HTML file
        try:
            with open(html_content.local_path, 'r', encoding='utf-8') as f:
                html_data = f.read()
        except Exception as e:
            console.print(f"[red]Error reading HTML file: {e}[/red]")
            return
        
        # Perform section analysis
        extractor = PageSectionExtractor()
        analysis = extractor.analyze_page_structure(html_data, html_content.url)
        
        # Display results
        console.print(f"\n[bold cyan]Page Structure Analysis for:[/bold cyan] {analysis['url']}")
        console.print(f"[bold]Title:[/bold] {analysis['title']}")
        
        # Page structure overview
        structure = analysis['page_structure']
        structure_table = Table(title="Page Structure Overview", show_header=True, header_style="bold purple")
        structure_table.add_column("Element Type", style="cyan")
        structure_table.add_column("Count", style="yellow")
        
        for element_type, count in structure.items():
            structure_table.add_row(element_type.replace('_', ' ').title(), str(count))
        
        console.print(structure_table)
        
        # Section analysis
        if analysis['sections']:
            console.print(f"\n[bold]Sections Found:[/bold]")
            for section_name, section_data in analysis['sections'].items():
                console.print(f"  📄 {section_name.title()}: {section_data['word_count']} words, {len(section_data['links'])} links")
                
                # Show contact info if found
                if section_data['contact_info']:
                    for info_type, info_list in section_data['contact_info'].items():
                        console.print(f"    {info_type}: {', '.join(info_list[:3])}")  # Show first 3
        
        # Social media links
        if analysis['social_media']:
            console.print(f"\n[bold]Social Media Links:[/bold]")
            for platform, links in analysis['social_media'].items():
                console.print(f"  🔗 {platform.title()}: {len(links)} link(s)")
        
        # Scripts and styles summary
        scripts_styles = analysis['scripts_and_styles']
        console.print(f"\n[bold]Scripts & Styles:[/bold]")
        console.print(f"  📜 External scripts: {len(scripts_styles['external_scripts'])}")
        console.print(f"  📜 Inline scripts: {len(scripts_styles['inline_scripts'])}")
        console.print(f"  🎨 External styles: {len(scripts_styles['external_styles'])}")
        console.print(f"  🎨 Inline styles: {len(scripts_styles['inline_styles'])}")


if __name__ == "__main__":
    sys.exit(main())
