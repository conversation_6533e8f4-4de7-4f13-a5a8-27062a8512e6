# Web Interface Scripts

**Python scripts specifically designed for the SpigaMonde Web Interface**

## 📁 Directory Purpose

This directory contains Python scripts that are:
- **Web UI Optimized** - Designed for execution via the web interface
- **User-Friendly** - Clear progress reporting and error handling
- **Well-Documented** - Easy to understand and modify
- **Safe for Web Execution** - Appropriate timeouts and resource limits

## 🔄 Script Categories

### **Basic Crawl Scripts**
Simple crawling tasks suitable for web interface execution:
- Limited scope (< 100 pages)
- Clear progress indicators
- Reasonable execution time (< 5 minutes)

### **Analysis Scripts** 
Content analysis and processing:
- Data extraction and transformation
- Report generation
- Statistics and metrics

### **Utility Scripts**
System maintenance and testing:
- Database cleanup
- Configuration validation
- System diagnostics

## 📝 Script Template

Use this template for new web interface scripts:

```python
#!/usr/bin/env python3
"""
Web Interface Script Template
Description: [Brief description of what this script does]
Execution Time: [Estimated time]
Resource Usage: [Memory/CPU requirements]
"""

import sys
import time
from pathlib import Path

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database
from rich.console import Console

def main():
    """Main execution function."""
    console = Console()
    
    try:
        # Initialize
        console.print("[cyan]Initializing...[/cyan]")
        init_database()
        
        # Your script logic here
        console.print("[cyan]Processing...[/cyan]")
        time.sleep(1)  # Replace with actual work
        
        # Complete
        console.print("[green]✓ Script completed successfully![/green]")
        return 0
        
    except Exception as e:
        console.print(f"[red]✗ Script failed: {e}[/red]")
        return 1

if __name__ == "__main__":
    sys.exit(main())
```

## 🚀 Adding New Scripts

1. **Create Script**: Copy template and customize
2. **Test Locally**: Run script directly to verify functionality
3. **Add to Web UI**: Script will be auto-detected by the interface
4. **Document**: Update this README with script description

## 🔒 Security Guidelines

- **Input Validation**: Always validate user inputs
- **Resource Limits**: Set appropriate timeouts and limits
- **Error Handling**: Graceful failure with clear error messages
- **Logging**: Use SpigaMonde logging for consistency

## 📚 Related Documentation

- **Main Scripts**: `../../scripts/` (SpigaMonde example scripts)
- **Web Interface**: `../README.md` (Web UI documentation)
- **CLI Commands**: Use `spiga --help` for direct commands
