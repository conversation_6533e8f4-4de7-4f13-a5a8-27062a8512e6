#!/usr/bin/env python3
"""
Test User-Agent Configuration
Demonstrates that the user-agent configuration is working correctly.
"""

import os
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent))

console = Console()

def test_user_agent():
    """Test current user-agent configuration."""
    
    console.print(Panel(
        "[bold]Testing User-Agent Configuration[/bold]\n"
        "This will show the actual user-agent that would be used in crawls.",
        title="🕷️ User-Agent Test",
        border_style="blue"
    ))
    
    # Method 1: Check environment variable directly
    env_user_agent = os.environ.get('SPIDER_USER_AGENT', 'Not set in environment')
    console.print(f"\n[cyan]Environment Variable:[/cyan] {env_user_agent}")
    
    # Method 2: Check .env file content
    env_file = Path(__file__).parent.parent / '.env'
    if env_file.exists():
        with open(env_file, 'r') as f:
            content = f.read()
            
        # Extract user agent from .env file
        for line in content.split('\n'):
            if line.startswith('SPIDER_USER_AGENT='):
                file_user_agent = line.split('=', 1)[1]
                console.print(f"[cyan].env File Setting:[/cyan] {file_user_agent}")
                break
        else:
            console.print("[yellow].env File:[/yellow] SPIDER_USER_AGENT not found")
    else:
        console.print("[red].env File:[/red] Not found")
    
    # Method 3: Test with a simple Scrapy configuration
    console.print("\n[bold]Testing Scrapy Configuration:[/bold]")
    
    try:
        # Create a simple Scrapy settings test
        from scrapy.settings import Settings
        
        # Load environment variables into Scrapy settings
        scrapy_settings = Settings()
        
        # Set user agent from environment or default
        user_agent = os.environ.get('SPIDER_USER_AGENT', 'SpigaMonde/0.1.0 (+https://github.com/spigamonde/spigamonde)')
        scrapy_settings.set('USER_AGENT', user_agent)
        
        console.print(f"[green]✓ Scrapy would use:[/green] {scrapy_settings.get('USER_AGENT')}")
        
        # Determine configuration type
        if 'TestCrawler' in user_agent:
            console.print("\n[yellow]🧪 TESTING configuration detected[/yellow]")
            console.print("User-agent is anonymized for testing purposes.")
        elif 'SpigaMonde' in user_agent:
            console.print("\n[blue]🚀 PRODUCTION configuration detected[/blue]")
            console.print("User-agent identifies as official SpigaMonde.")
        else:
            console.print("\n[red]❓ UNKNOWN configuration[/red]")
            console.print("User-agent doesn't match expected patterns.")
            
    except Exception as e:
        console.print(f"[red]✗ Scrapy test failed:[/red] {e}")
    
    # Method 4: Show how to switch configurations
    console.print("\n[bold]Configuration Management:[/bold]")
    console.print("To switch to testing:    [cyan]python scripts/switch_config.py testing[/cyan]")
    console.print("To switch to production: [cyan]python scripts/switch_config.py production[/cyan]")
    console.print("To check status:         [cyan]python scripts/switch_config.py status[/cyan]")

if __name__ == "__main__":
    test_user_agent()
