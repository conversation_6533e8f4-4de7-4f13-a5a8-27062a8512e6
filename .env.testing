# SpigaMonde Testing Configuration
# Use this configuration for testing with anonymized user-agent

# Debug mode for testing
DEBUG=true

# Database Configuration (same as production)
DATABASE_URL=sqlite:///spigamonde.db
DATABASE_ECHO=false

# Spider Configuration - TESTING SETTINGS
SPIDER_CONCURRENT_REQUESTS=8
SPIDER_DOWNLOAD_DELAY=2.0
SPIDER_RANDOMIZE_DELAY=true

# ANONYMIZED USER-AGENT FOR TESTING
SPIDER_USER_AGENT=TestCrawler/1.0 (+https://example.com/testcrawler)

SPIDER_MAX_DEPTH=2
SPIDER_MAX_PAGES=20
SPIDER_ROBOTSTXT_OBEY=true

# Conservative file type filtering for testing
SPIDER_ALLOWED_FILE_TYPES=html,htm,xml,rss,txt,pdf

# File size limits (smaller for testing)
SPIDER_MAX_FILE_SIZE=10485760  # 10MB
SPIDER_MIN_FILE_SIZE=1024      # 1KB

# Storage Configuration
STORAGE_BASE_PATH=./test_downloads
STORAGE_ORGANIZE_BY_DATE=true
STORAGE_ORGANIZE_BY_TYPE=true
STORAGE_ORGANIZE_BY_DOMAIN=false

# Logging Configuration
# Note: Logging config has known issues, using defaults for now

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_DASHBOARD_ENABLED=true
MONITORING_ALERTS_ENABLED=false
MONITORING_METRICS_RETENTION_HOURS=12
MONITORING_ALERT_CHECK_INTERVAL=60
MONITORING_PERFORMANCE_THRESHOLD=60.0
MONITORING_ERROR_RATE_THRESHOLD=0.3
