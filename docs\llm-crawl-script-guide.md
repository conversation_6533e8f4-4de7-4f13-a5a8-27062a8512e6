# LLM Guide: Creating Custom SpigaMonde Crawl Scripts

**Purpose**: Guide LLMs to generate custom Python crawl scripts for SpigaMonde  
**Target**: LLMs helping users create tailored crawling solutions  
**Output**: Production-ready Python scripts for specific crawling scenarios

## Overview

SpigaMonde provides a rich programmatic API that allows creating custom crawl scripts beyond the CLI interface. These scripts can implement complex crawling logic, custom analysis workflows, and specialized data processing.

## Script Placement

### Recommended Structure
```
SpigaMonde/
├── scripts/                    # User script library
│   ├── examples/              # Example scripts (provided)
│   │   ├── crawl_academic_papers.py    # Research paper crawler
│   │   ├── monitor_news_sites.py       # Real-time news monitoring
│   │   ├── extract_documentation.py    # Technical docs harvester
│   │   └── analyze_social_content.py   # Social media analyzer
│   ├── templates/             # Script templates for customization
│   │   ├── basic_crawl_template.py     # Simple crawling template
│   │   ├── analysis_focused_template.py # Content analysis emphasis
│   │   └── monitoring_template.py      # Real-time monitoring template
│   ├── user_scripts/          # User-generated scripts go here
│   └── README.md              # Script library documentation
```

### Script Naming Convention
- **Descriptive names**: `crawl_academic_papers.py`, `monitor_news_sites.py`
- **Purpose prefix**: `crawl_`, `analyze_`, `monitor_`, `extract_`
- **Domain suffix**: `_ecommerce.py`, `_research.py`, `_social.py`

### Available Examples
The `scripts/examples/` folder contains ready-to-use scripts:

1. **`crawl_academic_papers.py`**:
   - Targets academic repositories (arXiv, PubMed)
   - Extracts research metadata and citations
   - Classifies papers by research domain
   - Includes quality assessment and export

2. **`monitor_news_sites.py`**:
   - Real-time news monitoring from RSS feeds
   - Live analysis dashboard
   - Sentiment and topic classification
   - Source analysis and trending detection

### Available Templates
The `scripts/templates/` folder provides starting points:

1. **`basic_crawl_template.py`**:
   - Simple, customizable crawling script
   - Clear configuration sections
   - Built-in monitoring and analysis
   - Export functionality included

## Core SpigaMonde API Components

### 1. Essential Imports
```python
# Core crawling
from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider

# Configuration and settings
from spigamonde.config.settings import get_settings, Settings
from spigamonde.database.connection import init_database, session_scope

# Database models
from spigamonde.models.content import Content, ContentStatus, ContentType, CrawlSession, URL

# Content analysis
from spigamonde.analysis.analyzer import get_content_analyzer
from spigamonde.analysis.classifier import ContentCategory, QualityScore

# Monitoring and logging
from spigamonde.monitoring.logger import get_logger, setup_logging
from spigamonde.monitoring.dashboard import run_live_dashboard
from spigamonde.monitoring.metrics import get_metrics_collector

# Utilities
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
```

### 2. Basic Script Structure
```python
#!/usr/bin/env python3
"""
Custom SpigaMonde crawl script for [SPECIFIC PURPOSE]
Generated by: [LLM_NAME] on [DATE]
"""

import sys
import os
from pathlib import Path

# Add SpigaMonde to path if needed
sys.path.insert(0, str(Path(__file__).parent.parent))

from spigamonde.config.settings import get_settings
from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from rich.console import Console

def main():
    """Main crawl execution function."""
    console = Console()
    
    # Initialize database
    from spigamonde.database.connection import init_database
    init_database()
    
    # Configure crawl parameters
    settings = get_settings()
    
    # [CUSTOM LOGIC HERE]
    
    # Execute crawl
    process = CrawlerProcess({
        'USER_AGENT': settings.spider.user_agent,
        'ROBOTSTXT_OBEY': True,
        'LOG_LEVEL': 'INFO',
        # [CUSTOM SCRAPY SETTINGS]
    })
    
    process.crawl(ContentSpider, start_urls=[...])
    process.start()

if __name__ == "__main__":
    main()
```

## Script Input Parameters

### User Requirements Input
When generating scripts, gather these inputs from users:

#### 1. **Crawling Scope**
- **Target URLs**: Starting points for crawling
- **Domain restrictions**: Stay within specific domains
- **Depth limits**: How deep to crawl
- **Page limits**: Maximum pages to process
- **Content types**: Specific file types to target

#### 2. **Content Focus**
- **Content categories**: Academic papers, news, documentation, etc.
- **Quality requirements**: Minimum quality thresholds
- **Language preferences**: Target languages
- **Date ranges**: Recent content vs. historical
- **Size constraints**: File size limits

#### 3. **Analysis Requirements**
- **Classification needs**: Specific content categorization
- **Metadata extraction**: Title, author, keywords, etc.
- **Entity extraction**: Emails, URLs, dates, names
- **Quality assessment**: Readability, complexity scoring
- **Custom analysis**: Domain-specific processing

#### 4. **Output Requirements**
- **Storage location**: Custom download directories
- **Database filtering**: Specific content queries
- **Export formats**: CSV, JSON, reports
- **Real-time monitoring**: Dashboard requirements
- **Notifications**: Alert conditions

#### 5. **Performance Constraints**
- **Rate limiting**: Respectful crawling delays
- **Concurrent requests**: Parallel processing limits
- **Resource usage**: Memory and disk constraints
- **Time limits**: Maximum crawl duration

## Common Script Patterns

### 1. **Academic Research Crawler**
```python
# Target: Research papers and academic content
# Focus: High-quality academic sources
# Analysis: Citation extraction, author identification
# Output: Structured academic database
```

### 2. **News Monitoring Script**
```python
# Target: News websites and RSS feeds
# Focus: Recent articles, breaking news
# Analysis: Sentiment, topic classification
# Output: Real-time news dashboard
```

### 3. **Technical Documentation Harvester**
```python
# Target: API docs, tutorials, guides
# Focus: Technical content, code examples
# Analysis: Code extraction, version tracking
# Output: Developer knowledge base
```

### 4. **E-commerce Product Crawler**
```python
# Target: Product pages, catalogs
# Focus: Product information, pricing
# Analysis: Price tracking, feature extraction
# Output: Product comparison database
```

### 5. **Social Media Content Analyzer**
```python
# Target: Public social media posts
# Focus: Trending topics, user content
# Analysis: Sentiment, engagement metrics
# Output: Social media insights
```

## Advanced Features Integration

### 1. **Custom Content Analysis**
```python
# Implement domain-specific analysis
analyzer = get_content_analyzer()
result = analyzer.analyze_content(url, content, file_path, mime_type)

# Custom classification logic
if result.classification.category == ContentCategory.ACADEMIC_PAPER:
    # Extract citations, references
    pass
```

### 2. **Real-time Monitoring Integration**
```python
# Start monitoring dashboard
import threading
dashboard_thread = threading.Thread(target=run_live_dashboard, args=(duration,))
dashboard_thread.start()

# Execute crawl with monitoring
# Dashboard will show real-time progress
```

### 3. **Database Query and Export**
```python
# Query crawled content
with session_scope() as session:
    content = session.query(Content).filter(
        Content.content_type == ContentType.DOCUMENT,
        Content.status == ContentStatus.COMPLETED
    ).all()

# Export to CSV, JSON, etc.
```

### 4. **Custom Storage Organization**
```python
# Organize downloads by content type, date, domain
settings.storage.base_path = f"./downloads/{domain}/{date}"
```

## LLM Script Generation Prompt

### Input Processing
```
User Request: "I want to crawl [TARGET] for [PURPOSE] with [REQUIREMENTS]"

Extract:
1. Target websites/domains
2. Content focus and types
3. Analysis requirements
4. Output preferences
5. Performance constraints
```

### Script Generation Steps
```
1. Analyze user requirements
2. Select appropriate script pattern
3. Configure crawling parameters
4. Set up content analysis pipeline
5. Implement output formatting
6. Add monitoring and logging
7. Include error handling
8. Generate documentation
```

### Validation Checklist
```
✅ Imports are correct and complete
✅ Database initialization included
✅ Settings configuration appropriate
✅ Crawl parameters match requirements
✅ Content analysis pipeline configured
✅ Output handling implemented
✅ Error handling included
✅ Documentation and comments added
✅ Script follows naming conventions
✅ Performance constraints respected
```

## Error Handling Patterns

### Common Issues and Solutions
```python
# Database connection issues
try:
    init_database()
except Exception as e:
    console.print(f"[red]Database error: {e}[/red]")
    sys.exit(1)

# Crawl execution errors
try:
    process.start()
except Exception as e:
    console.print(f"[red]Crawl error: {e}[/red]")
    # Cleanup and recovery logic

# Analysis pipeline errors
try:
    result = analyzer.analyze_content(...)
except Exception as e:
    logger.error(f"Analysis failed for {url}: {e}")
    # Continue with next item
```

## Performance Optimization

### Best Practices
```python
# Respectful crawling
'DOWNLOAD_DELAY': 1.0,
'RANDOMIZE_DOWNLOAD_DELAY': True,
'CONCURRENT_REQUESTS': 2,

# Memory management
'DEPTH_LIMIT': 3,
'CLOSESPIDER_PAGECOUNT': 1000,

# Content filtering
'ALLOWED_FILE_TYPES': ['pdf', 'html', 'doc'],
'MAX_FILE_SIZE': 10 * 1024 * 1024,  # 10MB
```

## Quick Start for LLMs

### Using Existing Examples
When a user request matches an existing example:

```
User: "I want to crawl academic papers from arXiv"
LLM Response: "I recommend using the existing academic paper crawler:
1. Copy scripts/examples/crawl_academic_papers.py to scripts/user_scripts/
2. Customize the academic_sources list for your specific domains
3. Adjust crawl parameters if needed
4. Run: python scripts/user_scripts/crawl_academic_papers.py"
```

### Customizing Templates
For new use cases, start with a template:

```
User: "I need to crawl e-commerce product pages"
LLM Response: "I'll customize the basic template for e-commerce:
1. Copy scripts/templates/basic_crawl_template.py
2. Modify TARGET_URLS for product catalog pages
3. Adjust EXPECTED_CONTENT_CATEGORIES for product content
4. Add custom product metadata extraction
5. Configure export for product database"
```

### Generating New Scripts
For complex or unique requirements, generate a complete script using the patterns and API components documented above.

## Script Library Integration

### Encourage Script Sharing
- **Save to user_scripts/**: All custom scripts go in `scripts/user_scripts/`
- **Use descriptive names**: Help users find relevant scripts later
- **Include documentation**: Add usage instructions and examples
- **Follow conventions**: Use established patterns for consistency

### Reference Existing Work
- **Check examples first**: See if existing scripts meet user needs
- **Build on templates**: Start with proven patterns
- **Reuse components**: Leverage existing analysis and monitoring code
- **Maintain compatibility**: Ensure scripts work with current SpigaMonde version

This guide provides LLMs with comprehensive information to generate effective, production-ready SpigaMonde crawl scripts tailored to specific user requirements!
