/* SpigaMonde Web Interface - Minimal Styling */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-main {
    text-align: center;
    flex: 1;
}

.header-main h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header-main p {
    font-size: 1.2em;
    opacity: 0.9;
}

.header-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

/* Mode Toggle Button */
.mode-toggle-button {
    background: rgba(255, 255, 255, 0.2);
    padding: 12px 18px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9em;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    position: relative;
    overflow: hidden;
}

.mode-toggle-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.mode-toggle-button.testing {
    background: rgba(255, 193, 7, 0.4);
    border-color: rgba(255, 193, 7, 0.6);
}

.mode-toggle-button.testing:hover {
    background: rgba(255, 193, 7, 0.5);
    border-color: rgba(255, 193, 7, 0.8);
}

.mode-toggle-button.production {
    background: rgba(40, 167, 69, 0.4);
    border-color: rgba(40, 167, 69, 0.6);
}

.mode-toggle-button.production:hover {
    background: rgba(40, 167, 69, 0.5);
    border-color: rgba(40, 167, 69, 0.8);
}

.mode-toggle-button.switching {
    opacity: 0.7;
    cursor: not-allowed;
}

.mode-icon {
    font-size: 1.2em;
    transition: transform 0.3s ease;
}

.mode-toggle-button:hover .mode-icon {
    transform: scale(1.1);
}

.mode-text {
    white-space: nowrap;
    font-weight: 700;
}

.toggle-hint {
    font-size: 0.75em;
    opacity: 0.8;
    font-weight: 400;
    margin-left: 5px;
}

/* Tab Navigation */
.tab-navigation {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95em;
    font-weight: 500;
    color: #6c757d;
    white-space: nowrap;
    flex: 1;
    justify-content: center;
    min-width: 120px;
}

.tab-button:hover {
    background: rgba(255, 255, 255, 0.7);
    color: #495057;
}

.tab-button.active {
    background: white;
    color: #495057;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.tab-icon {
    font-size: 1.2em;
}

.tab-label {
    font-weight: 600;
}

/* Tab Content */
.main-content {
    min-height: 500px;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.dashboard-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.dashboard-card h2 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.dashboard-card p {
    color: #6c757d;
    margin-bottom: 20px;
    font-size: 0.95em;
}

/* Analytics Container */
.analytics-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.analytics-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

/* Analytics Stats Grid */
.analytics-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.analytics-stat {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.analytics-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.analytics-stat .stat-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.analytics-stat .stat-label {
    font-size: 0.9em;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Refresh Indicator */
.refresh-indicator {
    text-align: center;
    margin-top: 20px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.refresh-indicator small {
    color: #6c757d;
    font-size: 0.85em;
}

/* Reset Results Styling */
.summary-stat-value.success {
    color: #28a745;
}

.summary-stat-value.error {
    color: #dc3545;
}

.summary-stat-value.warning {
    color: #ffc107;
}

/* Script Mode Toggle */
.script-mode-toggle {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.mode-selector {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.mode-button {
    flex: 1;
    min-width: 200px;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.mode-button:hover {
    border-color: #007bff;
    background: #e3f2fd;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.2);
}

.mode-button.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.mode-button .mode-icon {
    font-size: 1.5em;
    margin-bottom: 5px;
}

.mode-button .mode-label {
    font-weight: bold;
    font-size: 1.1em;
    margin-bottom: 3px;
}

.mode-button small {
    font-size: 0.85em;
    opacity: 0.8;
}

.mode-button.active small {
    opacity: 0.9;
}

/* Mode Sections */
.mode-section {
    transition: all 0.3s ease;
}

.script-description {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid #e9ecef;
}

.script-info p {
    margin: 5px 0;
    font-size: 0.95em;
}

.script-info strong {
    color: #495057;
}

/* Script Output Display */
.script-output-section,
.script-warnings-section,
.script-metadata-section {
    margin-bottom: 15px;
}

.script-output-section h4,
.script-warnings-section h4,
.script-metadata-section h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 0.95em;
    font-weight: bold;
}

.script-output-text,
.script-warnings-text,
.script-metadata-text {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85em;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
}

.script-output-text {
    color: #2d3748;
    background: #f7fafc;
    border-color: #cbd5e0;
}

.script-warnings-text {
    color: #d69e2e;
    background: #fffbeb;
    border-color: #f6e05e;
}

.script-metadata-text {
    color: #4a5568;
    background: #f7fafc;
    border-color: #e2e8f0;
}

/* System Container */
.system-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.system-section h2 {
    color: #495057;
    margin-bottom: 15px;
}

/* Logs Container */
.logs-container {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.logs-header h2 {
    color: #495057;
    margin: 0;
}

/* Legacy Main Content (now handled by tab content) */

.test-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.test-section:nth-child(3) {
    border-left-color: #dc3545; /* Reset section - red */
}

.test-section:nth-child(4) {
    border-left-color: #28a745; /* Script section - green */
}

.test-section:nth-child(5) {
    border-left-color: #17a2b8; /* Stats section - blue */
    grid-column: 1 / -1; /* Stats widget spans full width */
}

.test-section:nth-child(6) {
    grid-column: 1 / -1; /* Activity log spans full width */
}

.test-section h2 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.5em;
}

.test-section p {
    color: #666;
    margin-bottom: 20px;
}

/* Buttons */
.test-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 200px;
    justify-content: center;
}

.test-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.test-button:active {
    transform: translateY(0);
}

.test-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.button-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.clear-button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    font-size: 0.9em;
    cursor: pointer;
    margin-top: 10px;
}

.clear-button:hover {
    background: #c82333;
}

.script-button {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.script-button:hover {
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.reset-button {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.reset-button:hover {
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

/* Reset Controls */
.reset-controls {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.reset-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.95em;
    color: #495057;
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #dc3545;
}

.checkbox-label:hover {
    color: #212529;
}

/* Reset Results */
.reset-result {
    border-left-color: #dc3545;
}

.reset-summary {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
}

.reset-summary .reset-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f8f9fa;
}

.reset-summary .reset-item:last-child {
    border-bottom: none;
}

.reset-summary .reset-label {
    font-weight: bold;
    color: #495057;
}

.reset-summary .reset-value {
    color: #dc3545;
}

.reset-summary .reset-value.success {
    color: #28a745;
}

/* Result Boxes */
.result-box {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.result-box.error {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.result-box h3 {
    color: #28a745;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.result-box.error h3 {
    color: #dc3545;
}

.result-box pre {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Script Results Styling */
.script-result {
    border-left-color: #28a745;
}

.script-summary {
    background: white;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    margin-bottom: 15px;
}

.script-summary .summary-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f8f9fa;
}

.script-summary .summary-item:last-child {
    border-bottom: none;
}

.script-summary .summary-label {
    font-weight: bold;
    color: #495057;
}

.script-summary .summary-value {
    color: #28a745;
}

.script-summary .summary-value.error {
    color: #dc3545;
}

.script-details {
    margin-top: 10px;
}

.script-details summary {
    cursor: pointer;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    font-weight: bold;
    color: #495057;
}

.script-details summary:hover {
    background: #e9ecef;
}

.script-details[open] summary {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.script-details pre {
    margin-top: 0;
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

/* Stats Widget Styling */
.stats-widget {
    border-left-color: #17a2b8;
}

.stats-button {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.stats-button:hover {
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
}

.stats-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.auto-refresh-control {
    display: flex;
    align-items: center;
}

.stats-display {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* Stats Overview Cards */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #17a2b8;
    line-height: 1;
}

.stat-label {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 5px;
}

/* Stats Details */
.stats-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.stats-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.stats-section h3 {
    color: #17a2b8;
    margin-bottom: 15px;
    font-size: 1.2em;
    border-bottom: 2px solid #17a2b8;
    padding-bottom: 5px;
}

/* Activity Stats */
.activity-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: white;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.activity-label {
    color: #495057;
    font-weight: 500;
}

.activity-count {
    color: #17a2b8;
    font-weight: bold;
}

/* Breakdown Stats */
.breakdown-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.breakdown-label {
    color: #495057;
    font-weight: 500;
    text-transform: capitalize;
}

.breakdown-count {
    color: #17a2b8;
    font-weight: bold;
}

.breakdown-bar {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    margin: 0 10px;
    overflow: hidden;
}

.breakdown-fill {
    height: 100%;
    background: linear-gradient(90deg, #17a2b8, #20c997);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Sources Stats */
.sources-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.source-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    font-size: 0.9em;
}

.source-domain {
    color: #495057;
    font-family: monospace;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
}

.source-count {
    color: #17a2b8;
    font-weight: bold;
}

/* Recent Content List */
.recent-content-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
}

.content-item {
    background: white;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    font-size: 0.9em;
}

.content-url {
    color: #17a2b8;
    font-weight: 500;
    margin-bottom: 4px;
    word-break: break-all;
}

.content-meta {
    display: flex;
    justify-content: space-between;
    color: #6c757d;
    font-size: 0.8em;
}

.content-type {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: bold;
}

.content-size {
    color: #495057;
}

/* Activity Log */
.log-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.log-entry {
    display: flex;
    gap: 15px;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #666;
    font-weight: bold;
    min-width: 80px;
}

.log-message {
    color: #333;
}

.log-entry.success .log-message {
    color: #28a745;
}

.log-entry.error .log-message {
    color: #dc3545;
}

.log-entry.info .log-message {
    color: #17a2b8;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: #666;
    border-top: 1px solid #e9ecef;
}

#backendStatus {
    font-weight: bold;
}

#backendStatus.online {
    color: #28a745;
}

#backendStatus.offline {
    color: #dc3545;
}

/* Scripts Tab */
.scripts-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.scripts-section {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
}

.script-controls {
    margin: 20px 0;
}

.script-selector {
    margin-bottom: 20px;
}

.script-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #fff;
}

.script-dropdown {
    width: 100%;
    max-width: 300px;
    padding: 10px 15px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1em;
    backdrop-filter: blur(10px);
}

.script-dropdown:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
}

.script-params {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.param-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.param-group {
    display: flex;
    flex-direction: column;
}

.param-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #fff;
}

.param-input {
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 0.9em;
}

.param-input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

.script-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.script-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.script-execute-button {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.script-stop-button {
    background: linear-gradient(135deg, #f44336, #da190b);
}

.script-save-button {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.script-status {
    margin: 15px 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-icon {
    font-size: 1.2em;
}

.status-text {
    font-weight: 600;
    color: #fff;
}

.script-progress {
    margin: 20px 0;
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 25px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 12px;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
    color: #fff;
    font-size: 0.9em;
}

.progress-details {
    margin-top: 10px;
    color: #ccc;
    font-size: 0.9em;
}

.script-results {
    margin-top: 20px;
}

.script-summary-widget {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-stat {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.summary-stat-value {
    font-size: 1.5em;
    font-weight: 700;
    color: #4CAF50;
    margin-bottom: 5px;
}

.summary-stat-label {
    color: #ccc;
    font-size: 0.9em;
}

.script-history {
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    display: grid;
    grid-template-columns: 150px 1fr 100px;
    gap: 15px;
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    align-items: center;
}

.history-item:last-child {
    border-bottom: none;
}

.history-time {
    color: #888;
    font-size: 0.9em;
}

.history-script {
    color: #fff;
    font-weight: 600;
}

.history-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    text-align: center;
    font-weight: 600;
}

.history-status.success {
    background: rgba(76, 175, 80, 0.3);
    color: #4CAF50;
}

.history-status.error {
    background: rgba(244, 67, 54, 0.3);
    color: #f44336;
}

.history-status.running {
    background: rgba(33, 150, 243, 0.3);
    color: #2196F3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main {
        grid-template-columns: 1fr;
    }

    .header h1 {
        font-size: 2em;
    }

    .test-button {
        width: 100%;
    }

    .param-grid {
        grid-template-columns: 1fr;
    }

    .script-actions {
        flex-direction: column;
    }

    .history-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}
