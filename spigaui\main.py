"""
SpigaUI FastAPI Application

Main application entry point for the SpigaUI web interface.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from spigaui.api import health, crawl, jobs
from spigaui.core.config import get_settings
from spigaui.core.logging import setup_logging

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    settings = get_settings()
    logger.info("Starting SpigaUI application", version="0.1.0")
    
    # Startup logic
    try:
        # Initialize any required services here
        logger.info("Application startup complete")
        yield
    finally:
        # Cleanup logic
        logger.info("Shutting down SpigaUI application")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="SpigaUI",
        description="Modern web interface for SpigaMonde crawler",
        version="0.1.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        lifespan=lifespan,
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routers
    app.include_router(health.router, prefix="/api", tags=["health"])
    app.include_router(crawl.router, prefix="/api", tags=["crawl"])
    app.include_router(jobs.router, prefix="/api", tags=["jobs"])
    
    # Serve static files (frontend build)
    if settings.serve_static:
        app.mount("/", StaticFiles(directory="frontend/dist", html=True), name="static")
    
    return app


# Create the application instance
app = create_app()


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "SpigaUI - Modern Web Interface for SpigaMonde",
        "version": "0.1.0",
        "docs": "/api/docs",
    }


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "spigaui.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
