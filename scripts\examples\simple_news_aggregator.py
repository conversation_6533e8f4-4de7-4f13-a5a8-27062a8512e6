#!/usr/bin/env python3
"""
Simple News Aggregator for SpigaMonde
Quick and easy news headline extraction with HTML output.

Usage:
    python simple_news_aggregator.py
    
Features:
    - Crawls major news RSS feeds
    - Extracts headlines and metadata
    - Generates clean HTML dashboard
    - Real-time progress tracking
"""

import sys
import os
import json
import re
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content
from rich.console import Console
from rich.panel import Panel
from rich.table import Table


def main():
    """Execute simple news aggregation."""
    console = Console()
    
    console.print(Panel.fit(
        "[bold green]📰 Simple News Aggregator[/bold green]\n"
        "Quick headline extraction and HTML generation",
        border_style="green"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Simple news sources (RSS feeds work best)
    news_sources = [
        "https://feeds.bbci.co.uk/news/rss.xml",  # BBC News
        "https://rss.cnn.com/rss/edition.rss",    # CNN
        "https://feeds.reuters.com/reuters/topNews",  # Reuters
        "https://techcrunch.com/feed/",           # TechCrunch
    ]
    
    console.print(f"[cyan]Crawling {len(news_sources)} news sources...[/cyan]")
    
    # Simple crawl configuration
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Simple News Aggregator 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.0,
        'CONCURRENT_REQUESTS': 2,
        'DEPTH_LIMIT': 1,  # Only RSS feeds, no following links
        'CLOSESPIDER_PAGECOUNT': 20,
        'LOG_LEVEL': 'INFO',
        'ALLOWED_FILE_TYPES': ['xml', 'rss', 'html'],
    }
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=news_sources)
        process.start()
        
        console.print("[green]✓ News sources crawled![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Extract headlines and generate HTML
    console.print("\n[bold cyan]Extracting headlines...[/bold cyan]")
    headlines = extract_headlines_from_crawled_content(console)
    
    if headlines:
        html_file = generate_simple_html_dashboard(headlines, console)
        console.print(f"[green]✓ HTML dashboard created: {html_file}[/green]")
        console.print(f"[cyan]Open in browser: file://{html_file.absolute()}[/cyan]")
    else:
        console.print("[yellow]No headlines extracted.[/yellow]")
    
    return 0


def extract_headlines_from_crawled_content(console):
    """Extract headlines from crawled RSS and HTML content."""
    headlines = []
    
    with session_scope() as session:
        crawled_content = session.query(Content).all()
        
        console.print(f"Processing {len(crawled_content)} crawled items...")
        
        for content in crawled_content:
            if content.local_path and os.path.exists(content.local_path):
                try:
                    with open(content.local_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content_text = f.read()
                    
                    # Extract headlines based on content type
                    if is_rss_content(content_text, content.url):
                        headlines.extend(extract_from_rss(content_text, content.url))
                    else:
                        headlines.extend(extract_from_html(content_text, content.url))
                        
                except Exception as e:
                    console.print(f"[yellow]Warning: Could not process {content.url}: {e}[/yellow]")
    
    console.print(f"[green]✓ Extracted {len(headlines)} headlines[/green]")
    return headlines


def is_rss_content(content, url):
    """Check if content is RSS/XML."""
    return ('rss' in url.lower() or 
            'feed' in url.lower() or 
            '<rss' in content.lower() or 
            '<feed' in content.lower())


def extract_from_rss(rss_content, url):
    """Extract headlines from RSS content using regex."""
    headlines = []
    source_name = get_source_name(url)
    
    # Find all RSS items
    items = re.findall(r'<item[^>]*>(.*?)</item>', rss_content, re.DOTALL | re.IGNORECASE)
    
    for item in items:
        # Extract title
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', item, re.IGNORECASE)
        if title_match:
            title = clean_html_text(title_match.group(1))
            
            # Extract link
            link_match = re.search(r'<link[^>]*>([^<]+)</link>', item, re.IGNORECASE)
            link = link_match.group(1) if link_match else url
            
            # Extract description
            desc_match = re.search(r'<description[^>]*>([^<]+)</description>', item, re.IGNORECASE)
            description = clean_html_text(desc_match.group(1)) if desc_match else ""
            
            # Extract date
            date_match = re.search(r'<pubDate[^>]*>([^<]+)</pubDate>', item, re.IGNORECASE)
            pub_date = date_match.group(1) if date_match else ""
            
            if title and len(title) > 10:  # Valid headline
                headlines.append({
                    'title': title,
                    'link': link,
                    'description': description[:200] + "..." if len(description) > 200 else description,
                    'source': source_name,
                    'date': pub_date,
                    'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
    
    return headlines


def extract_from_html(html_content, url):
    """Extract headlines from HTML content using regex."""
    headlines = []
    source_name = get_source_name(url)
    
    # Extract title
    title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
    if title_match:
        title = clean_html_text(title_match.group(1))
        if title and len(title) > 10:
            headlines.append({
                'title': title,
                'link': url,
                'description': "",
                'source': source_name,
                'date': "",
                'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    # Extract h1, h2 headings that might be headlines
    heading_matches = re.findall(r'<h[12][^>]*>([^<]+)</h[12]>', html_content, re.IGNORECASE)
    for heading in heading_matches:
        heading_text = clean_html_text(heading)
        if heading_text and len(heading_text) > 10 and len(heading_text) < 150:
            headlines.append({
                'title': heading_text,
                'link': url,
                'description': "",
                'source': source_name,
                'date': "",
                'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    return headlines


def get_source_name(url):
    """Extract source name from URL."""
    domain = urlparse(url).netloc.lower()
    
    # Map known domains to friendly names
    source_map = {
        'feeds.bbci.co.uk': 'BBC News',
        'rss.cnn.com': 'CNN',
        'feeds.reuters.com': 'Reuters',
        'techcrunch.com': 'TechCrunch',
        'www.npr.org': 'NPR',
    }
    
    for domain_part, name in source_map.items():
        if domain_part in domain:
            return name
    
    # Default: clean up domain name
    return domain.replace('www.', '').replace('.com', '').replace('.org', '').title()


def clean_html_text(text):
    """Clean HTML tags and entities from text."""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Decode common HTML entities
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    text = text.replace('&#39;', "'")
    text = text.replace('&nbsp;', ' ')
    
    return text.strip()


def generate_simple_html_dashboard(headlines, console):
    """Generate a simple HTML dashboard."""
    
    # Sort headlines by extraction time (most recent first)
    sorted_headlines = sorted(headlines, key=lambda x: x.get('extracted_at', ''), reverse=True)
    
    # Group by source
    by_source = {}
    for headline in headlines:
        source = headline.get('source', 'Unknown')
        if source not in by_source:
            by_source[source] = []
        by_source[source].append(headline)
    
    # Generate HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News Headlines - SpigaMonde</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        .headlines {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }}
        .headline {{
            border-bottom: 1px solid #eee;
            padding: 20px 0;
        }}
        .headline:last-child {{
            border-bottom: none;
        }}
        .headline-title {{
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }}
        .headline-meta {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 8px;
        }}
        .headline-description {{
            color: #555;
            font-size: 0.95em;
        }}
        .source-tag {{
            background: #667eea;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📰 News Headlines</h1>
        <p>Aggregated by SpigaMonde</p>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <div class="stat-number">{len(headlines)}</div>
            <div>Headlines</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(by_source)}</div>
            <div>Sources</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len([h for h in headlines if h.get('description')])}</div>
            <div>With Descriptions</div>
        </div>
    </div>
    
    <div class="headlines">
        <h2>Latest Headlines</h2>
"""
    
    # Add headlines
    for headline in sorted_headlines[:50]:  # Show top 50
        title = headline.get('title', 'No title')
        source = headline.get('source', 'Unknown')
        description = headline.get('description', '')
        date = headline.get('date', '')
        link = headline.get('link', '#')
        
        html_content += f"""
        <div class="headline">
            <div class="headline-title">
                <a href="{link}" target="_blank" style="text-decoration: none; color: inherit;">
                    {title}
                </a>
            </div>
            <div class="headline-meta">
                <span class="source-tag">{source}</span>
                {date}
            </div>
            {f'<div class="headline-description">{description}</div>' if description else ''}
        </div>
        """
    
    html_content += """
    </div>
    
    <div class="footer">
        <p>🔄 Refresh this page to get latest headlines</p>
        <p>Powered by SpigaMonde Web Crawler</p>
    </div>
</body>
</html>
"""
    
    # Write HTML file
    output_dir = Path(__file__).parent / 'output'
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    html_file = output_dir / f'news_headlines_{timestamp}.html'
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # Also create a latest.html for easy access
    latest_file = output_dir / 'latest_news.html'
    with open(latest_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_file


if __name__ == "__main__":
    sys.exit(main())
