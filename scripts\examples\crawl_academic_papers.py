#!/usr/bin/env python3
"""
Academic Paper Crawler for SpigaMonde
Crawls academic repositories and research sites for papers and publications.

Usage:
    python crawl_academic_papers.py
    
Features:
    - Targets academic paper repositories (arXiv, PubMed, etc.)
    - Focuses on high-quality research content
    - Extracts metadata: authors, abstracts, citations
    - Classifies papers by research domain
    - Exports structured results
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import threading

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.dashboard import run_live_dashboard
from rich.console import Console
from rich.panel import Panel
from rich.table import Table


def main():
    """Execute academic paper crawling with monitoring."""
    console = Console()
    
    # Display banner
    console.print(Panel.fit(
        "[bold blue]SpigaMonde Academic Paper Crawler[/bold blue]\n"
        "Discovering and analyzing research publications",
        border_style="blue"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Academic paper sources
    academic_sources = [
        "https://arxiv.org/list/cs.AI/recent",  # AI papers
        "https://arxiv.org/list/cs.LG/recent",  # Machine Learning
        "https://arxiv.org/list/cs.CL/recent",  # Computational Linguistics
        "https://arxiv.org/list/cs.CV/recent",  # Computer Vision
    ]
    
    # Configure crawl parameters for academic content
    settings = get_settings()
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Academic Crawler 1.0 (+https://github.com/spigamonde)',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 2.0,  # Respectful delay for academic sites
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS': 1,  # Conservative for academic repositories
        'DEPTH_LIMIT': 2,  # Focus on paper listings and individual papers
        'CLOSESPIDER_PAGECOUNT': 100,  # Limit for demo purposes
        'LOG_LEVEL': 'INFO',
        
        # Custom settings for academic content
        'ALLOWED_FILE_TYPES': ['pdf', 'html', 'xml'],
        'MAX_FILE_SIZE': 50 * 1024 * 1024,  # 50MB for large papers
    }
    
    console.print(f"[cyan]Crawling {len(academic_sources)} academic sources...[/cyan]")
    console.print(f"[dim]Max depth: {crawl_config['DEPTH_LIMIT']}, Max pages: {crawl_config['CLOSESPIDER_PAGECOUNT']}[/dim]")
    
    # Start monitoring dashboard in background
    console.print("[yellow]Starting monitoring dashboard...[/yellow]")
    dashboard_thread = threading.Thread(
        target=run_live_dashboard, 
        args=(300,),  # 5 minutes
        daemon=True
    )
    dashboard_thread.start()
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=academic_sources)
        process.start()
        
        console.print("[green]✓ Crawl completed successfully![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Analyze results
    console.print("\n[bold cyan]Analyzing crawl results...[/bold cyan]")
    analyze_academic_results(console)
    
    return 0


def analyze_academic_results(console):
    """Analyze and display academic paper crawl results."""
    
    with session_scope() as session:
        # Query academic papers
        academic_papers = session.query(Content).join(ContentAnalysis).filter(
            ContentAnalysis.category == 'ACADEMIC_PAPER'
        ).all()
        
        if not academic_papers:
            console.print("[yellow]No academic papers found in this crawl.[/yellow]")
            return
        
        # Create results table
        table = Table(title="Academic Papers Discovered", show_header=True, header_style="bold magenta")
        table.add_column("Title", style="cyan", width=40)
        table.add_column("Quality", style="green")
        table.add_column("Words", style="blue")
        table.add_column("Confidence", style="yellow")
        
        total_papers = len(academic_papers)
        high_quality = 0
        total_words = 0
        
        for paper in academic_papers[:10]:  # Show top 10
            analysis = paper.content_analysis[0] if paper.content_analysis else None
            if analysis:
                title = paper.filename[:37] + "..." if len(paper.filename) > 40 else paper.filename
                quality = analysis.quality_score
                words = analysis.word_count or 0
                confidence = analysis.confidence
                
                table.add_row(
                    title,
                    quality,
                    str(words),
                    f"{confidence:.2f}"
                )
                
                if quality in ['EXCELLENT', 'GOOD']:
                    high_quality += 1
                total_words += words
        
        console.print(table)
        
        # Summary statistics
        console.print(f"\n[bold green]Summary Statistics:[/bold green]")
        console.print(f"📄 Total papers found: {total_papers}")
        console.print(f"⭐ High quality papers: {high_quality} ({high_quality/total_papers*100:.1f}%)")
        console.print(f"📝 Average words per paper: {total_words/total_papers:.0f}")
        
        # Export option
        export_path = f"academic_papers_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        console.print(f"\n[dim]Results can be exported to: {export_path}[/dim]")
        console.print("[dim]Use: spiga export --format csv --filter academic_paper[/dim]")


if __name__ == "__main__":
    sys.exit(main())
