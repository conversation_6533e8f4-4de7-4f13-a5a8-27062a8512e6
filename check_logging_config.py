#!/usr/bin/env python3
"""Check SpigaMonde logging configuration."""

import os
from spigamonde.config.settings import get_settings

def check_config():
    """Check current logging configuration."""
    
    # Set environment variable
    os.environ['LOG_FILE_PATH'] = './logs/spigamonde.log'
    
    # Get settings
    settings = get_settings()
    
    print("SpigaMonde Logging Configuration:")
    print(f"  Log Level: {settings.logging.level}")
    print(f"  Log Format: {settings.logging.format}")
    print(f"  File Path: {settings.logging.file_path}")
    print(f"  Max File Size: {settings.logging.max_file_size}")
    print(f"  Retention: {settings.logging.retention}")
    print(f"  Structured: {settings.logging.structured_logging}")
    print(f"  Performance: {settings.logging.performance_logging}")
    
    print(f"\nEnvironment Variables:")
    print(f"  LOG_FILE_PATH: {os.environ.get('LOG_FILE_PATH', 'Not set')}")
    print(f"  LOG_LEVEL: {os.environ.get('LOG_LEVEL', 'Not set')}")

if __name__ == "__main__":
    check_config()
