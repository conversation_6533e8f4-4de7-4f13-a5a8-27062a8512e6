#!/usr/bin/env python3
"""
Contact Information Extractor for SpigaMonde
Specialized script for extracting emails, phone numbers, and contact details from web pages.

Usage:
    python extract_contact_info.py
    
Features:
    - Enhanced email extraction with validation
    - Phone number extraction with formatting
    - HTML section targeting (headers, footers, contact pages)
    - Contact page detection and prioritization
    - Advanced regex patterns for better accuracy
    - Export contact database with source tracking
"""

import sys
import os
import re
import json
from pathlib import Path
from datetime import datetime
from urllib.parse import urljoin, urlparse
import threading

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.dashboard import run_live_dashboard
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Try to import BeautifulSoup for enhanced HTML parsing
try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    HAS_BEAUTIFULSOUP = False
    print("Note: Install beautifulsoup4 for enhanced HTML section targeting")


class ContactExtractor:
    """Enhanced contact information extractor."""
    
    def __init__(self):
        # Enhanced email patterns
        self.email_patterns = [
            # Standard email
            re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            # Email with display name
            re.compile(r'["\']?([^"\'<>\s]+)["\']?\s*<([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})>'),
            # Obfuscated emails (at, dot)
            re.compile(r'\b[A-Za-z0-9._%+-]+\s*(?:at|@)\s*[A-Za-z0-9.-]+\s*(?:dot|\.)\s*[A-Z|a-z]{2,}\b', re.IGNORECASE),
        ]
        
        # Enhanced phone patterns
        self.phone_patterns = [
            # US/International formats
            re.compile(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'),
            # International with country code
            re.compile(r'\+\d{1,3}\s?\d{1,4}\s?\d{1,4}\s?\d{1,9}'),
            # Formatted with extensions
            re.compile(r'(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}\s*(?:ext|x|extension)\.?\s*\d+', re.IGNORECASE),
        ]
        
        # Contact page indicators
        self.contact_indicators = [
            'contact', 'about', 'team', 'staff', 'directory', 'support',
            'help', 'info', 'reach', 'connect', 'get-in-touch'
        ]
        
        # HTML sections to prioritize for contact info
        self.priority_sections = [
            'footer', 'header', 'contact', 'about', 'sidebar',
            'nav', 'navigation', 'team', 'staff'
        ]
    
    def extract_from_html(self, html_content, url):
        """Extract contact info with HTML section awareness."""
        results = {
            'emails': set(),
            'phones': set(),
            'sections': {},
            'is_contact_page': self.is_contact_page(url)
        }
        
        if HAS_BEAUTIFULSOUP:
            results.update(self._extract_with_beautifulsoup(html_content))
        else:
            results.update(self._extract_with_regex(html_content))
        
        # Convert sets to lists for JSON serialization
        results['emails'] = list(results['emails'])
        results['phones'] = list(results['phones'])
        
        return results
    
    def _extract_with_beautifulsoup(self, html_content):
        """Enhanced extraction using BeautifulSoup for section targeting."""
        soup = BeautifulSoup(html_content, 'html.parser')
        results = {'emails': set(), 'phones': set(), 'sections': {}}
        
        # Extract from specific sections
        for section_name in self.priority_sections:
            # Try different ways to find sections
            sections = (
                soup.find_all(attrs={'class': re.compile(section_name, re.I)}) +
                soup.find_all(attrs={'id': re.compile(section_name, re.I)}) +
                soup.find_all(section_name) +
                soup.find_all('div', string=re.compile(section_name, re.I))
            )
            
            section_contacts = {'emails': set(), 'phones': set()}
            
            for section in sections:
                text = section.get_text() if section else ""
                section_contacts['emails'].update(self._extract_emails(text))
                section_contacts['phones'].update(self._extract_phones(text))
            
            if section_contacts['emails'] or section_contacts['phones']:
                results['sections'][section_name] = {
                    'emails': list(section_contacts['emails']),
                    'phones': list(section_contacts['phones'])
                }
                results['emails'].update(section_contacts['emails'])
                results['phones'].update(section_contacts['phones'])
        
        # Also extract from full page
        full_text = soup.get_text()
        results['emails'].update(self._extract_emails(full_text))
        results['phones'].update(self._extract_phones(full_text))
        
        return results
    
    def _extract_with_regex(self, html_content):
        """Fallback extraction using regex only."""
        results = {'emails': set(), 'phones': set(), 'sections': {}}
        
        # Extract from full content
        results['emails'].update(self._extract_emails(html_content))
        results['phones'].update(self._extract_phones(html_content))
        
        return results
    
    def _extract_emails(self, text):
        """Extract emails using multiple patterns."""
        emails = set()
        
        for pattern in self.email_patterns:
            matches = pattern.findall(text)
            for match in matches:
                if isinstance(match, tuple):
                    # Handle patterns with groups
                    email = match[1] if len(match) > 1 else match[0]
                else:
                    email = match
                
                # Clean and validate
                email = email.strip().lower()
                if self._is_valid_email(email):
                    emails.add(email)
        
        return emails
    
    def _extract_phones(self, text):
        """Extract phone numbers using multiple patterns."""
        phones = set()
        
        for pattern in self.phone_patterns:
            matches = pattern.findall(text)
            for match in matches:
                phone = match if isinstance(match, str) else match[0]
                phone = self._clean_phone(phone)
                if phone:
                    phones.add(phone)
        
        return phones
    
    def _is_valid_email(self, email):
        """Validate email format."""
        if not email or len(email) < 5:
            return False
        if email.count('@') != 1:
            return False
        if '.' not in email.split('@')[1]:
            return False
        return True
    
    def _clean_phone(self, phone):
        """Clean and format phone number."""
        # Remove non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone)
        if len(cleaned) >= 10:  # Minimum valid phone length
            return cleaned
        return None
    
    def is_contact_page(self, url):
        """Determine if URL is likely a contact page."""
        url_lower = url.lower()
        return any(indicator in url_lower for indicator in self.contact_indicators)


def main():
    """Execute contact information extraction crawl."""
    console = Console()
    
    # Display banner
    console.print(Panel.fit(
        "[bold green]SpigaMonde Contact Information Extractor[/bold green]\n"
        "Specialized extraction of emails, phones, and contact details",
        border_style="green"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Target URLs - focus on sites likely to have contact info
    target_urls = [
        "https://www.python.org/about/",  # About page with contact info
        "https://docs.python.org/3/",     # Documentation with contact details
        # Add your target URLs here
    ]
    
    # Configure for contact extraction
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Contact Extractor 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.5,  # Respectful crawling
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS': 2,
        'DEPTH_LIMIT': 2,  # Go deeper for contact pages
        'CLOSESPIDER_PAGECOUNT': 50,
        'LOG_LEVEL': 'INFO',
        
        # Focus on HTML content
        'ALLOWED_FILE_TYPES': ['html', 'htm'],
        'MAX_FILE_SIZE': 5 * 1024 * 1024,  # 5MB
    }
    
    console.print(f"[cyan]Extracting contact info from {len(target_urls)} target URL(s)...[/cyan]")
    
    # Start monitoring
    console.print("[yellow]Starting monitoring dashboard...[/yellow]")
    dashboard_thread = threading.Thread(
        target=run_live_dashboard,
        args=(300,),  # 5 minutes
        daemon=True
    )
    dashboard_thread.start()
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=target_urls)
        process.start()
        
        console.print("[green]✓ Contact extraction completed![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Extraction error: {e}[/red]")
        return 1
    
    # Analyze extracted contact information
    console.print("\n[bold cyan]Analyzing extracted contact information...[/bold cyan]")
    analyze_contact_results(console)
    
    return 0


def analyze_contact_results(console):
    """Analyze and display extracted contact information."""
    
    with session_scope() as session:
        # Query content with analysis results
        analyzed_content = session.query(Content).join(ContentAnalysis).all()
        
        if not analyzed_content:
            console.print("[yellow]No analyzed content found.[/yellow]")
            return
        
        # Collect all contact information
        all_emails = set()
        all_phones = set()
        contact_pages = []
        
        for content in analyzed_content:
            analysis = content.content_analysis[0] if content.content_analysis else None
            if analysis:
                # Parse stored JSON data
                if analysis.emails:
                    emails = json.loads(analysis.emails)
                    all_emails.update(emails)
                
                if analysis.phone_numbers:
                    phones = json.loads(analysis.phone_numbers)
                    all_phones.update(phones)
                
                # Check if it's a contact page
                extractor = ContactExtractor()
                if extractor.is_contact_page(content.url):
                    contact_pages.append(content.url)
        
        # Display results
        console.print(f"📧 [bold]Emails Found:[/bold] {len(all_emails)}")
        if all_emails:
            email_table = Table(title="Extracted Email Addresses", show_header=True, header_style="bold green")
            email_table.add_column("Email", style="cyan")
            email_table.add_column("Domain", style="yellow")
            
            for email in sorted(all_emails):
                domain = email.split('@')[1] if '@' in email else 'unknown'
                email_table.add_row(email, domain)
            
            console.print(email_table)
        
        console.print(f"\n📞 [bold]Phone Numbers Found:[/bold] {len(all_phones)}")
        if all_phones:
            for phone in sorted(all_phones):
                console.print(f"  {phone}")
        
        console.print(f"\n📄 [bold]Contact Pages Detected:[/bold] {len(contact_pages)}")
        for page in contact_pages:
            console.print(f"  {page}")
        
        # Export suggestion
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        console.print(f"\n[dim]Export contact database:[/dim]")
        console.print(f"[dim]spiga export --format csv --output contacts_{timestamp}.csv[/dim]")


if __name__ == "__main__":
    sys.exit(main())
