/**
 * SpigaMonde Web Interface - Simple Working Version
 * Focus on getting basic functionality working first
 */

// Configuration
const API_BASE_URL = 'http://127.0.0.1:8000';

// DOM Elements
let testConnectionBtn, getStatsBtn, runScriptBtn, resetSystemBtn, refreshStatsBtn, clearLogBtn;
let connectionResult, statsResult, scriptResult, resetResult, activityLog, backendStatus;
let connectionData, statsData, scriptData, scriptSummary, resetSummary;
let modeToggleBtn, clearDatabase, clearDownloads, clearLogs;

// Scripts Tab Elements
let scriptSelect, scriptParams, executeScriptBtn, stopScriptBtn, saveConfigBtn;
let scriptStatus, scriptProgress, scriptResults, scriptHistory;
let targetUrl, maxDepth, maxPages, crawlDelay, followExternal, downloadMedia, analyzeContent;

// Script Mode Elements
let commandModeBtn, scriptModeBtn, commandModeSection, scriptModeSection;
let standaloneScriptSelect, executeStandaloneBtn, scriptDescription;

/**
 * Initialize the application
 */
function init() {
    console.log('SpigaMonde Web Interface - Simple Version');
    
    // Get DOM elements
    testConnectionBtn = document.getElementById('testConnectionBtn');
    getStatsBtn = document.getElementById('getStatsBtn');
    runScriptBtn = document.getElementById('runScriptBtn');
    resetSystemBtn = document.getElementById('resetSystemBtn');
    refreshStatsBtn = document.getElementById('refreshStatsBtn');
    clearLogBtn = document.getElementById('clearLogBtn');
    
    connectionResult = document.getElementById('connectionResult');
    statsResult = document.getElementById('statsResult');
    scriptResult = document.getElementById('scriptResult');
    resetResult = document.getElementById('resetResult');
    activityLog = document.getElementById('activityLog');
    backendStatus = document.getElementById('backendStatus');
    modeToggleBtn = document.getElementById('modeToggleBtn');
    
    connectionData = document.getElementById('connectionData');
    statsData = document.getElementById('statsData');
    scriptData = document.getElementById('scriptData');
    scriptSummary = document.getElementById('scriptSummary');
    resetSummary = document.getElementById('resetSummary');
    
    // Reset options
    clearDatabase = document.getElementById('clearDatabase');
    clearDownloads = document.getElementById('clearDownloads');
    clearLogs = document.getElementById('clearLogs');

    // Scripts tab elements
    scriptSelect = document.getElementById('scriptSelect');
    scriptParams = document.getElementById('scriptParams');
    executeScriptBtn = document.getElementById('executeScriptBtn');
    stopScriptBtn = document.getElementById('stopScriptBtn');
    saveConfigBtn = document.getElementById('saveConfigBtn');
    scriptStatus = document.getElementById('scriptStatus');
    scriptProgress = document.getElementById('scriptProgress');
    scriptResults = document.getElementById('scriptResults');
    scriptHistory = document.getElementById('scriptHistory');

    // Script parameters
    targetUrl = document.getElementById('targetUrl');
    maxDepth = document.getElementById('maxDepth');
    maxPages = document.getElementById('maxPages');
    crawlDelay = document.getElementById('crawlDelay');
    followExternal = document.getElementById('followExternal');
    downloadMedia = document.getElementById('downloadMedia');
    analyzeContent = document.getElementById('analyzeContent');

    // Script Mode Elements
    commandModeBtn = document.getElementById('commandModeBtn');
    scriptModeBtn = document.getElementById('scriptModeBtn');
    commandModeSection = document.getElementById('commandModeSection');
    scriptModeSection = document.getElementById('scriptModeSection');
    standaloneScriptSelect = document.getElementById('standaloneScriptSelect');
    executeStandaloneBtn = document.getElementById('executeStandaloneBtn');
    scriptDescription = document.getElementById('scriptDescription');
    
    console.log('DOM elements found:', {
        testConnectionBtn: !!testConnectionBtn,
        getStatsBtn: !!getStatsBtn,
        runScriptBtn: !!runScriptBtn,
        modeToggleBtn: !!modeToggleBtn,
        connectionResult: !!connectionResult,
        activityLog: !!activityLog
    });
    
    // Add event listeners
    if (testConnectionBtn) {
        testConnectionBtn.addEventListener('click', testConnection);
        console.log('Connection button listener added');
    }
    if (getStatsBtn) {
        getStatsBtn.addEventListener('click', getSpigamondeStats);
    }
    if (runScriptBtn) {
        runScriptBtn.addEventListener('click', runTestScript);
    }
    if (resetSystemBtn) {
        resetSystemBtn.addEventListener('click', resetSystem);
    }
    if (refreshStatsBtn) {
        refreshStatsBtn.addEventListener('click', refreshDetailedStats);
    }
    if (clearLogBtn) {
        clearLogBtn.addEventListener('click', clearLog);
    }
    if (modeToggleBtn) {
        modeToggleBtn.addEventListener('click', toggleMode);
        console.log('Mode toggle button listener added');
    }

    // Scripts tab event listeners
    if (scriptSelect) {
        scriptSelect.addEventListener('change', onScriptSelect);
        console.log('Script select listener added');
    }
    if (executeScriptBtn) {
        executeScriptBtn.addEventListener('click', executeScript);
        console.log('Execute script button listener added');
    }
    if (stopScriptBtn) {
        stopScriptBtn.addEventListener('click', stopScript);
    }
    if (saveConfigBtn) {
        saveConfigBtn.addEventListener('click', saveScriptConfig);
    }

    // Script Mode event listeners
    if (executeStandaloneBtn) {
        executeStandaloneBtn.addEventListener('click', executeStandaloneScript);
        console.log('Execute standalone script button listener added');
    }
    if (commandModeBtn) {
        commandModeBtn.addEventListener('click', () => switchScriptMode('command'));
        console.log('Command mode button listener added');
    }
    if (scriptModeBtn) {
        scriptModeBtn.addEventListener('click', () => switchScriptMode('script'));
        console.log('Script mode button listener added');
    }
    if (standaloneScriptSelect) {
        standaloneScriptSelect.addEventListener('change', onStandaloneScriptChange);
        console.log('Standalone script select listener added');
    }
    
    // Initialize tab system
    initTabSystem();
    
    // Initial setup
    logMessage('Simple interface initialized', 'info');

    // Add debug info to page
    if (backendStatus) {
        backendStatus.textContent = 'Initializing...';
        logMessage('Backend status element found, starting connection check', 'info');
    } else {
        logMessage('ERROR: Backend status element not found!', 'error');
    }

    checkBackendStatus();
    updateModeDisplay();
    loadAvailableScripts();
    setupAutoRefresh();
    initializeScriptMode();
    
    console.log('SpigaMonde Web Interface initialized');
}

/**
 * Initialize tab system
 */
function initTabSystem() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    console.log('Tab system init:', { buttons: tabButtons.length, contents: tabContents.length });
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            console.log('Tab clicked:', tabId);
            switchTab(tabId);
        });
    });
}

/**
 * Switch to a specific tab
 */
function switchTab(tabId) {
    console.log('Switching to tab:', tabId);
    
    // Update button states
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }
    
    // Update content visibility
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    const activeContent = document.getElementById(`${tabId}-tab`);
    if (activeContent) {
        activeContent.classList.add('active');
    }

    // Auto-refresh analytics when switching to analytics tab
    if (tabId === 'analytics') {
        setTimeout(() => {
            refreshDetailedStats();
        }, 100); // Small delay to ensure tab is visible
    }

    logMessage(`Switched to ${tabId} tab`, 'info');
}

/**
 * Test connection to backend
 */
async function testConnection() {
    console.log('Testing connection...');
    logMessage('Testing backend connection...', 'info');
    
    // Update button state
    setButtonLoading(testConnectionBtn, true);
    hideResult(connectionResult);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/test`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        console.log('Response status:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('Response data:', data);
        
        // Display success result
        if (connectionData) {
            connectionData.textContent = JSON.stringify(data, null, 2);
        }
        if (connectionResult) {
            connectionResult.classList.remove('error');
            connectionResult.style.display = 'block';
        }
        
        logMessage('Connection test successful', 'success');
        
    } catch (error) {
        console.error('Connection test failed:', error);
        
        // Display error result
        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/test'
        };
        
        if (connectionData) {
            connectionData.textContent = JSON.stringify(errorData, null, 2);
        }
        if (connectionResult) {
            connectionResult.classList.add('error');
            connectionResult.style.display = 'block';
        }
        
        logMessage(`Connection test failed: ${error.message}`, 'error');
        
    } finally {
        setButtonLoading(testConnectionBtn, false);
    }
}

/**
 * Set button loading state
 */
function setButtonLoading(button, isLoading) {
    if (!button) return;
    
    const text = button.querySelector('.button-text');
    const spinner = button.querySelector('.button-spinner');
    
    if (isLoading) {
        if (text) text.style.display = 'none';
        if (spinner) spinner.style.display = 'inline';
        button.disabled = true;
    } else {
        if (text) text.style.display = 'inline';
        if (spinner) spinner.style.display = 'none';
        button.disabled = false;
    }
}

/**
 * Hide result element
 */
function hideResult(resultElement) {
    if (resultElement) {
        resultElement.style.display = 'none';
    }
}

/**
 * Log message to activity log
 */
function logMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    if (!activityLog) {
        console.log('Activity log element not found');
        return;
    }
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    
    logEntry.innerHTML = `
        <span class="log-time">${timestamp}</span>
        <span class="log-message">${message}</span>
    `;
    
    activityLog.appendChild(logEntry);
    activityLog.scrollTop = activityLog.scrollHeight;
}

/**
 * Clear activity log
 */
function clearLog() {
    if (activityLog) {
        activityLog.innerHTML = '';
        logMessage('Activity log cleared', 'info');
    }
}

/**
 * Check backend status
 */
async function checkBackendStatus() {
    console.log('Checking backend status...');
    console.log('API_BASE_URL:', API_BASE_URL);
    console.log('backendStatus element:', backendStatus);

    try {
        console.log('Making fetch request to:', `${API_BASE_URL}/api/health`);
        const response = await fetch(`${API_BASE_URL}/api/health`);
        console.log('Response received:', response);

        if (response.ok) {
            const data = await response.json();
            console.log('Response data:', data);

            if (backendStatus) {
                backendStatus.textContent = '✅ Connected';
                backendStatus.className = 'status-value connected';
                console.log('Backend status updated to connected');
            } else {
                console.error('backendStatus element not found!');
            }
            logMessage('Backend connection established', 'success');
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.error('Backend connection error:', error);
        if (backendStatus) {
            backendStatus.textContent = '❌ Disconnected';
            backendStatus.className = 'status-value disconnected';
        } else {
            console.error('backendStatus element not found for error handling!');
        }
        logMessage(`Backend connection failed: ${error.message}`, 'error');
    }
}

/**
 * Update mode display
 */
async function updateModeDisplay() {
    if (!modeToggleBtn) return;

    try {
        const response = await fetch(`${API_BASE_URL}/api/mode`);
        if (response.ok) {
            const data = await response.json();
            const mode = data.mode;

            const modeIcon = modeToggleBtn.querySelector('.mode-icon');
            const modeText = modeToggleBtn.querySelector('.mode-text');

            if (modeIcon) modeIcon.textContent = mode.icon;
            if (modeText) modeText.textContent = mode.description;

            modeToggleBtn.className = `mode-toggle-button ${mode.type}`;
            logMessage(`Current mode: ${mode.type}`, 'info');
        }
    } catch (error) {
        console.error('Mode check failed:', error);
        // Set default to testing mode on error
        setModeDisplay('testing', '🧪', 'Testing Mode - Anonymized crawling');
    }
}

/**
 * Set mode display manually
 */
function setModeDisplay(modeType, icon, description) {
    if (!modeToggleBtn) return;

    const modeIcon = modeToggleBtn.querySelector('.mode-icon');
    const modeText = modeToggleBtn.querySelector('.mode-text');

    if (modeIcon) modeIcon.textContent = icon;
    if (modeText) modeText.textContent = description;

    modeToggleBtn.className = `mode-toggle-button ${modeType}`;
}

/**
 * Toggle between testing and production mode
 */
async function toggleMode() {
    if (!modeToggleBtn) return;

    console.log('Toggling mode...');
    logMessage('Switching mode...', 'info');

    // Add switching state
    modeToggleBtn.classList.add('switching');
    modeToggleBtn.disabled = true;

    try {
        const response = await fetch(`${API_BASE_URL}/api/mode/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Mode toggle response:', data);

        // Update display with new mode
        const newMode = data.new_mode;
        setModeDisplay(newMode.type, newMode.icon, newMode.description);

        logMessage(`Mode switched to ${newMode.type}`, 'success');

        // Refresh other components that might depend on mode
        setTimeout(() => {
            checkBackendStatus();
        }, 500);

    } catch (error) {
        console.error('Mode toggle failed:', error);
        logMessage(`Mode toggle failed: ${error.message}`, 'error');

    } finally {
        // Remove switching state
        modeToggleBtn.classList.remove('switching');
        modeToggleBtn.disabled = false;
    }
}

/**
 * Get SpigaMonde statistics
 */
async function getSpigamondeStats() {
    console.log('Getting SpigaMonde stats...');
    logMessage('Fetching SpigaMonde statistics...', 'info');

    setButtonLoading(getStatsBtn, true);
    hideResult(statsResult);

    try {
        const response = await fetch(`${API_BASE_URL}/api/spiga/stats`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (statsData) {
            statsData.textContent = JSON.stringify(data, null, 2);
        }
        if (statsResult) {
            statsResult.classList.remove('error');
            statsResult.style.display = 'block';
        }

        logMessage('Statistics retrieved successfully', 'success');

    } catch (error) {
        console.error('Stats fetch failed:', error);

        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/spiga/stats'
        };

        if (statsData) {
            statsData.textContent = JSON.stringify(errorData, null, 2);
        }
        if (statsResult) {
            statsResult.classList.add('error');
            statsResult.style.display = 'block';
        }

        logMessage(`Statistics fetch failed: ${error.message}`, 'error');

    } finally {
        setButtonLoading(getStatsBtn, false);
    }
}

/**
 * Run test script
 */
async function runTestScript() {
    console.log('Running test script...');
    logMessage('Starting test script execution...', 'info');

    setButtonLoading(runScriptBtn, true);
    hideResult(scriptResult);

    try {
        const response = await fetch(`${API_BASE_URL}/api/run-script`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        });

        console.log('Script response status:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Script response data:', data);

        // Display success result
        showScriptResult(scriptResult, data, false);
        logMessage('Test script completed successfully', 'success');

    } catch (error) {
        console.error('Script execution failed:', error);

        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/run-script'
        };

        showScriptResult(scriptResult, errorData, true);
        logMessage(`Test script failed: ${error.message}`, 'error');

    } finally {
        setButtonLoading(runScriptBtn, false);
    }
}

/**
 * Show script result
 */
function showScriptResult(resultElement, data, isError = false) {
    if (!resultElement || !scriptSummary) return;

    // Update styling
    if (isError) {
        resultElement.classList.add('error');

        // Show simple error for script failures
        scriptSummary.innerHTML = `
            <div class="summary-item">
                <span class="summary-label">Status:</span>
                <span class="summary-value error">Error</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Error:</span>
                <span class="summary-value error">${data.error || 'Unknown error'}</span>
            </div>
        `;

        if (scriptData) {
            scriptData.textContent = JSON.stringify(data, null, 2);
        }

    } else {
        resultElement.classList.remove('error');

        // Create summary display
        const results = data.results || {};
        const summary = data.summary || {};

        let summaryHtml = `
            <div class="summary-item">
                <span class="summary-label">Overall Status:</span>
                <span class="summary-value ${summary.overall_status === 'SUCCESS' ? 'success' : 'error'}">${summary.overall_status || 'Unknown'}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Tests Passed:</span>
                <span class="summary-value">${summary.successful_tests || 0}/${summary.total_tests || 0}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Execution Time:</span>
                <span class="summary-value">${summary.execution_time || 'Unknown'}</span>
            </div>
        `;

        scriptSummary.innerHTML = summaryHtml;

        if (scriptData) {
            scriptData.textContent = JSON.stringify(data, null, 2);
        }
    }

    // Show the result
    resultElement.style.display = 'block';
}

/**
 * Analytics Tab Functions
 */

// Auto-refresh interval
let statsRefreshInterval = null;

/**
 * Refresh detailed statistics for Analytics tab
 */
async function refreshDetailedStats() {
    console.log('Refreshing detailed statistics...');
    logMessage('Refreshing analytics data...', 'info');

    if (!refreshStatsBtn) return;

    setButtonLoading(refreshStatsBtn, true);

    try {
        const response = await fetch(`${API_BASE_URL}/api/stats/detailed`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Detailed stats data:', data);

        // Update Analytics tab display
        updateAnalyticsDisplay(data);

        logMessage('Analytics data refreshed successfully', 'success');

    } catch (error) {
        console.error('Failed to refresh stats:', error);
        logMessage(`Failed to refresh analytics: ${error.message}`, 'error');
        showStatsError(error.message);
    } finally {
        setButtonLoading(refreshStatsBtn, false);
    }
}

/**
 * Update the Analytics tab display with fresh data
 */
function updateAnalyticsDisplay(data) {
    console.log('Updating analytics display with data:', data);

    // Show the stats widget
    const statsWidget = document.getElementById('statsWidget');
    const statsError = document.getElementById('statsError');

    if (statsWidget && data.status === 'success') {
        statsWidget.style.display = 'block';
        if (statsError) statsError.style.display = 'none';

        // Update overview cards with detailed stats data
        const overview = data.overview || {};

        // Update total content
        const totalContentEl = document.getElementById('totalContent');
        if (totalContentEl) {
            totalContentEl.textContent = overview.total_content || 0;
        }

        // Update analyzed content
        const analyzedContentEl = document.getElementById('analyzedContent');
        if (analyzedContentEl) {
            analyzedContentEl.textContent = overview.analyzed_content || 0;
        }

        // Update storage metrics
        const totalStorageEl = document.getElementById('totalStorage');
        if (totalStorageEl && data.storage_metrics) {
            totalStorageEl.textContent = `${data.storage_metrics.total_storage_mb || 0} MB`;
        }

        // Update analysis progress
        const analysisProgressEl = document.getElementById('analysisProgress');
        if (analysisProgressEl) {
            analysisProgressEl.textContent = `${overview.analysis_coverage_pct || 0}%`;
        }

        // Update detailed sections
        updateRecentActivity(data.recent_activity || {});
        updateContentBreakdown(data.content_breakdown || {});
        updateTopSources(data.top_domains || []);
        updateRecentContent(data.recent_samples || []);

        console.log('Analytics display updated successfully');
    } else {
        console.error('Failed to update analytics display:', data);
    }
}

/**
 * Show statistics error
 */
function showStatsError(errorMessage) {
    const statsWidget = document.getElementById('statsWidget');
    const statsError = document.getElementById('statsError');
    const statsErrorMessage = document.getElementById('statsErrorMessage');

    if (statsWidget) statsWidget.style.display = 'none';
    if (statsError) {
        statsError.style.display = 'block';
        if (statsErrorMessage) {
            statsErrorMessage.textContent = errorMessage;
        }
    }
}

/**
 * Update recent activity section
 */
function updateRecentActivity(recentActivity) {
    const recentActivityEl = document.getElementById('recentActivity');
    if (!recentActivityEl) return;

    const html = `
        <div class="activity-item">
            <span class="activity-label">Last Hour:</span>
            <span class="activity-value">${recentActivity.last_hour || 0}</span>
        </div>
        <div class="activity-item">
            <span class="activity-label">Last 24 Hours:</span>
            <span class="activity-value">${recentActivity.last_24h || 0}</span>
        </div>
        <div class="activity-item">
            <span class="activity-label">Last Week:</span>
            <span class="activity-value">${recentActivity.last_week || 0}</span>
        </div>
        <div class="activity-item">
            <span class="activity-label">Last Month:</span>
            <span class="activity-value">${recentActivity.last_month || 0}</span>
        </div>
    `;
    recentActivityEl.innerHTML = html;
}

/**
 * Update content breakdown section
 */
function updateContentBreakdown(contentBreakdown) {
    const contentBreakdownEl = document.getElementById('contentBreakdown');
    if (!contentBreakdownEl) return;

    const byStatus = contentBreakdown.by_status || {};
    const byType = contentBreakdown.by_type || {};

    let html = '<div class="breakdown-section"><h4>By Status</h4>';
    for (const [status, count] of Object.entries(byStatus)) {
        if (count > 0) {
            html += `<div class="breakdown-item">
                <span class="breakdown-label">${status}:</span>
                <span class="breakdown-value">${count}</span>
            </div>`;
        }
    }
    html += '</div>';

    if (Object.keys(byType).length > 0) {
        html += '<div class="breakdown-section"><h4>By Type</h4>';
        for (const [type, count] of Object.entries(byType)) {
            html += `<div class="breakdown-item">
                <span class="breakdown-label">${type}:</span>
                <span class="breakdown-value">${count}</span>
            </div>`;
        }
        html += '</div>';
    }

    contentBreakdownEl.innerHTML = html;
}

/**
 * Update top sources section
 */
function updateTopSources(topDomains) {
    const topSourcesEl = document.getElementById('topSources');
    if (!topSourcesEl) return;

    if (topDomains.length === 0) {
        topSourcesEl.innerHTML = '<div class="no-data">No sources found</div>';
        return;
    }

    let html = '';
    topDomains.slice(0, 10).forEach(domain => {
        html += `<div class="source-item">
            <span class="source-domain">${domain.domain}</span>
            <span class="source-count">${domain.count}</span>
        </div>`;
    });

    topSourcesEl.innerHTML = html;
}

/**
 * Update recent content section
 */
function updateRecentContent(recentSamples) {
    const recentContentEl = document.getElementById('recentContent');
    if (!recentContentEl) return;

    if (recentSamples.length === 0) {
        recentContentEl.innerHTML = '<div class="no-data">No recent content found</div>';
        return;
    }

    let html = '';
    recentSamples.slice(0, 10).forEach(item => {
        const createdAt = item.created_at ? new Date(item.created_at).toLocaleString() : 'Unknown';
        html += '<div class="content-item">' +
            '<div class="content-header">' +
                '<span class="content-filename">' + (item.filename || 'Unknown') + '</span>' +
                '<span class="content-type">' + (item.content_type || 'unknown') + '</span>' +
            '</div>' +
            '<div class="content-details">' +
                '<span class="content-url">' + item.url + '</span>' +
                '<span class="content-size">' + (item.file_size_mb || 0) + ' MB</span>' +
                '<span class="content-date">' + createdAt + '</span>' +
            '</div>' +
        '</div>';
    });

    recentContentEl.innerHTML = html;
}

/**
 * Create analytics display elements
 */
function createAnalyticsDisplay(data) {
    const analyticsContainer = document.querySelector('.analytics-container');
    if (!analyticsContainer) return;

    // Create stats grid
    const statsGrid = document.createElement('div');
    statsGrid.className = 'analytics-stats-grid';
    statsGrid.innerHTML = `
        <div class="analytics-stat" data-stat="total">
            <div class="stat-value">${data.statistics?.total_content || 0}</div>
            <div class="stat-label">Total Content</div>
        </div>
        <div class="analytics-stat" data-stat="analyzed">
            <div class="stat-value">${data.statistics?.analyzed_content || 0}</div>
            <div class="stat-label">Analyzed Content</div>
        </div>
        <div class="analytics-stat" data-stat="recent">
            <div class="stat-value">${data.statistics?.recent_content_24h || 0}</div>
            <div class="stat-label">Recent (24h)</div>
        </div>
        <div class="analytics-stat" data-stat="coverage">
            <div class="stat-value">${data.statistics?.analysis_coverage || '0%'}</div>
            <div class="stat-label">Analysis Coverage</div>
        </div>
    `;

    // Add refresh time indicator
    const refreshIndicator = document.createElement('div');
    refreshIndicator.className = 'refresh-indicator';
    refreshIndicator.innerHTML = `
        <small>Last updated: <span id="lastRefreshTime">${new Date().toLocaleTimeString()}</span></small>
    `;

    // Insert after header
    const header = analyticsContainer.querySelector('.analytics-header');
    if (header) {
        header.insertAdjacentElement('afterend', statsGrid);
        statsGrid.insertAdjacentElement('afterend', refreshIndicator);
    }
}

/**
 * Setup auto-refresh functionality
 */
function setupAutoRefresh() {
    const autoRefreshCheckbox = document.getElementById('autoRefreshStats');
    if (!autoRefreshCheckbox) return;

    autoRefreshCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Start auto-refresh every 30 seconds
            statsRefreshInterval = setInterval(refreshDetailedStats, 30000);
            logMessage('Auto-refresh enabled (30s interval)', 'info');
        } else {
            // Stop auto-refresh
            if (statsRefreshInterval) {
                clearInterval(statsRefreshInterval);
                statsRefreshInterval = null;
            }
            logMessage('Auto-refresh disabled', 'info');
        }
    });
}

/**
 * Reset system functionality
 */
async function resetSystem() {
    console.log('Reset system requested...');
    logMessage('System reset requested...', 'info');

    if (!resetSystemBtn) return;

    // Get reset options from checkboxes
    const clearDatabaseCheckbox = document.getElementById('clearDatabase');
    const clearDownloadsCheckbox = document.getElementById('clearDownloads');
    const clearLogsCheckbox = document.getElementById('clearLogs');

    const resetOptions = {
        clear_database: clearDatabaseCheckbox ? clearDatabaseCheckbox.checked : true,
        clear_downloads: clearDownloadsCheckbox ? clearDownloadsCheckbox.checked : true,
        clear_logs: clearLogsCheckbox ? clearLogsCheckbox.checked : false
    };

    // Confirm with user
    const confirmMessage = `Are you sure you want to reset the system?\n\n` +
        `This will:\n` +
        `${resetOptions.clear_database ? '✓ Clear all database content\n' : ''}` +
        `${resetOptions.clear_downloads ? '✓ Clear all download directories\n' : ''}` +
        `${resetOptions.clear_logs ? '✓ Clear all log files\n' : ''}` +
        `\nThis action cannot be undone!`;

    if (!confirm(confirmMessage)) {
        logMessage('System reset cancelled by user', 'info');
        return;
    }

    setButtonLoading(resetSystemBtn, true);
    hideResult(resetResult);

    try {
        const response = await fetch(`${API_BASE_URL}/api/reset-system`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(resetOptions)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Reset system response:', data);

        // Display results
        showResetResults(data);

        if (data.status === 'success') {
            logMessage('System reset completed successfully', 'success');
        } else if (data.status === 'partial_success') {
            logMessage(`System reset partially completed: ${data.message}`, 'warning');
        } else {
            logMessage(`System reset failed: ${data.message}`, 'error');
        }

    } catch (error) {
        console.error('Reset system failed:', error);
        logMessage(`System reset failed: ${error.message}`, 'error');

        const errorResults = {
            status: 'error',
            message: error.message,
            timestamp: new Date().toISOString()
        };

        showResetResults(errorResults);

    } finally {
        setButtonLoading(resetSystemBtn, false);
    }
}

/**
 * Display reset system results
 */
function showResetResults(data) {
    if (!resetResult) return;

    const resetData = document.getElementById('resetData');
    const resetSummaryWidget = document.getElementById('resetSummaryWidget');

    if (resetSummaryWidget) {
        let summaryHtml = '';

        if (data.status === 'success' || data.status === 'partial_success') {
            if (data.operations) {
                // Database operations
                if (data.operations.database) {
                    const db = data.operations.database;
                    summaryHtml += `
                        <div class="summary-stat">
                            <div class="summary-stat-value ${db.status === 'success' ? 'success' : 'error'}">${db.content_deleted || 0}</div>
                            <div class="summary-stat-label">Content Deleted</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value ${db.status === 'success' ? 'success' : 'error'}">${db.analysis_deleted || 0}</div>
                            <div class="summary-stat-label">Analyses Deleted</div>
                        </div>
                    `;
                }

                // Downloads operations
                if (data.operations.downloads) {
                    const dl = data.operations.downloads;
                    summaryHtml += `
                        <div class="summary-stat">
                            <div class="summary-stat-value ${dl.status === 'success' ? 'success' : 'error'}">${dl.directories_cleared?.length || 0}</div>
                            <div class="summary-stat-label">Directories Cleared</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value ${dl.status === 'success' ? 'success' : 'error'}">${dl.total_size_mb || 0} MB</div>
                            <div class="summary-stat-label">Space Freed</div>
                        </div>
                    `;
                }

                // Logs operations
                if (data.operations.logs) {
                    const logs = data.operations.logs;
                    summaryHtml += `
                        <div class="summary-stat">
                            <div class="summary-stat-value ${logs.status === 'success' ? 'success' : 'error'}">${logs.files_cleared?.length || 0}</div>
                            <div class="summary-stat-label">Log Files Cleared</div>
                        </div>
                    `;
                }
            }
        } else {
            // Error case
            summaryHtml = `
                <div class="summary-stat">
                    <div class="summary-stat-value error">ERROR</div>
                    <div class="summary-stat-label">Status</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value error">FAILED</div>
                    <div class="summary-stat-label">Result</div>
                </div>
            `;
        }

        resetSummaryWidget.innerHTML = summaryHtml;
    }

    if (resetData) {
        resetData.textContent = JSON.stringify(data, null, 2);
    }

    resetResult.style.display = 'block';
}

/**
 * Script Mode Functions
 */

/**
 * Initialize script mode functionality
 */
function initializeScriptMode() {
    // Set default mode to command
    switchScriptMode('command');

    // Load standalone scripts
    loadStandaloneScripts();
}

/**
 * Switch between command and script modes
 */
function switchScriptMode(mode) {
    console.log('Switching to script mode:', mode);

    // Update button states
    if (commandModeBtn && scriptModeBtn) {
        commandModeBtn.classList.remove('active');
        scriptModeBtn.classList.remove('active');

        if (mode === 'command') {
            commandModeBtn.classList.add('active');
        } else {
            scriptModeBtn.classList.add('active');
        }
    }

    // Update section visibility
    if (commandModeSection && scriptModeSection) {
        if (mode === 'command') {
            commandModeSection.style.display = 'block';
            scriptModeSection.style.display = 'none';
        } else {
            commandModeSection.style.display = 'none';
            scriptModeSection.style.display = 'block';
        }
    }

    logMessage(`Switched to ${mode} mode`, 'info');
}

/**
 * Load standalone scripts for script mode
 */
async function loadStandaloneScripts() {
    if (!standaloneScriptSelect) return;

    try {
        console.log('Loading standalone scripts...');
        const response = await fetch(`${API_BASE_URL}/api/available-scripts`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Available scripts for standalone mode:', data);

        // Clear existing options
        standaloneScriptSelect.innerHTML = '<option value="">Choose a script...</option>';

        // Add web scripts (designed for standalone execution)
        if (data.scripts.web_scripts && Object.keys(data.scripts.web_scripts).length > 0) {
            const webGroup = document.createElement('optgroup');
            webGroup.label = '🌐 Web UI Scripts';

            for (const [key, script] of Object.entries(data.scripts.web_scripts)) {
                const option = document.createElement('option');
                option.value = `web_${key}`;
                option.textContent = script.name;
                option.dataset.description = script.description;
                option.dataset.type = 'Web UI Script';
                option.dataset.time = 'Variable';
                webGroup.appendChild(option);
            }

            standaloneScriptSelect.appendChild(webGroup);
        }

        // Add example scripts (may work standalone)
        if (data.scripts.example_scripts && Object.keys(data.scripts.example_scripts).length > 0) {
            const exampleGroup = document.createElement('optgroup');
            exampleGroup.label = '📚 Example Scripts';

            for (const [key, script] of Object.entries(data.scripts.example_scripts)) {
                const option = document.createElement('option');
                option.value = `example_${key}`;
                option.textContent = script.name;
                option.dataset.description = script.description;
                option.dataset.type = 'Example Script';
                option.dataset.time = 'Variable (may require URLs)';
                exampleGroup.appendChild(option);
            }

            standaloneScriptSelect.appendChild(exampleGroup);
        }

        logMessage(`Loaded ${data.total_count} scripts for standalone execution`, 'success');

    } catch (error) {
        console.error('Failed to load standalone scripts:', error);
        logMessage(`Failed to load standalone scripts: ${error.message}`, 'error');
    }
}

/**
 * Handle standalone script selection change
 */
function onStandaloneScriptChange() {
    const selectedOption = standaloneScriptSelect.selectedOptions[0];

    if (selectedOption && selectedOption.value) {
        // Show script description
        if (scriptDescription) {
            const descText = document.getElementById('scriptDescText');
            const timeText = document.getElementById('scriptTimeText');
            const typeText = document.getElementById('scriptTypeText');

            if (descText) descText.textContent = selectedOption.dataset.description || 'No description available';
            if (timeText) timeText.textContent = selectedOption.dataset.time || 'Unknown';
            if (typeText) typeText.textContent = selectedOption.dataset.type || 'Unknown';

            scriptDescription.style.display = 'block';
        }

        // Enable execute button
        if (executeStandaloneBtn) {
            executeStandaloneBtn.disabled = false;
        }

        logMessage(`Selected script: ${selectedOption.textContent}`, 'info');
    } else {
        // Hide description and disable button
        if (scriptDescription) {
            scriptDescription.style.display = 'none';
        }
        if (executeStandaloneBtn) {
            executeStandaloneBtn.disabled = true;
        }
    }
}

/**
 * Execute standalone script
 */
async function executeStandaloneScript() {
    console.log('Executing standalone script...');

    if (!standaloneScriptSelect || !standaloneScriptSelect.value) {
        logMessage('Please select a script to execute', 'error');
        return;
    }

    const scriptType = standaloneScriptSelect.value;
    const scriptName = standaloneScriptSelect.selectedOptions[0].textContent;

    logMessage(`Executing standalone script: ${scriptName}`, 'info');

    if (!executeStandaloneBtn) return;

    setButtonLoading(executeStandaloneBtn, true);
    updateScriptStatus('🔄', 'Executing standalone script...', 'info');

    try {
        // Create script configuration for standalone execution
        const scriptConfig = {
            script_type: scriptType,
            target_url: '', // No URL needed for standalone scripts
            standalone_mode: true,
            verbose_output: document.getElementById('verboseOutput')?.checked || false,
            save_results: document.getElementById('saveResults')?.checked || true
        };

        console.log('Standalone script config:', scriptConfig);

        // Execute script via API
        const response = await fetch(`${API_BASE_URL}/api/execute-script`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(scriptConfig)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('Standalone script execution result:', result);

        // Show results with actual script output
        const standaloneResults = {
            execution_id: result.execution_id,
            script_type: result.script_type,
            execution_method: result.execution_method,
            status: result.status,
            message: result.message,
            output: result.output,  // Include actual script output
            warnings: result.warnings,  // Include any warnings
            return_code: result.return_code,
            timestamp: result.timestamp,
            standalone_mode: true
        };

        showScriptResults(standaloneResults);
        updateScriptStatus('✅', `Standalone script executed: ${result.execution_id}`, 'success');
        addToHistory(scriptType, 'executed');

        logMessage(`Standalone script executed successfully: ${result.execution_id}`, 'success');

    } catch (error) {
        console.error('Standalone script execution failed:', error);
        logMessage(`Standalone script execution failed: ${error.message}`, 'error');

        updateScriptStatus('❌', `Execution failed: ${error.message}`, 'error');

        const errorResults = {
            error: error.message,
            timestamp: new Date().toISOString(),
            script_type: scriptType,
            status: 'failed',
            standalone_mode: true
        };

        showScriptResults(errorResults);
        addToHistory(scriptType, 'error');

    } finally {
        setButtonLoading(executeStandaloneBtn, false);
    }
}

/**
 * Scripts Tab Functions
 */

/**
 * Load available scripts from backend
 */
async function loadAvailableScripts() {
    if (!scriptSelect) return;

    try {
        console.log('Loading available scripts...');
        const response = await fetch(`${API_BASE_URL}/api/available-scripts`);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Available scripts:', data);

        // Clear existing options except the first one
        scriptSelect.innerHTML = '<option value="">Choose a script...</option>';

        // Add CLI commands
        if (data.scripts.cli_commands) {
            const cliGroup = document.createElement('optgroup');
            cliGroup.label = '🔧 CLI Commands (Direct spiga commands)';

            for (const [key, script] of Object.entries(data.scripts.cli_commands)) {
                const option = document.createElement('option');
                option.value = key;
                option.textContent = `${script.name} - ${script.command}`;
                option.title = script.description;
                cliGroup.appendChild(option);
            }

            scriptSelect.appendChild(cliGroup);
        }

        // Add web scripts
        if (data.scripts.web_scripts && Object.keys(data.scripts.web_scripts).length > 0) {
            const webGroup = document.createElement('optgroup');
            webGroup.label = '🌐 Web UI Scripts (Python scripts for web interface)';

            for (const [key, script] of Object.entries(data.scripts.web_scripts)) {
                const option = document.createElement('option');
                option.value = `web_${key}`;
                option.textContent = script.name;
                option.title = script.description;
                webGroup.appendChild(option);
            }

            scriptSelect.appendChild(webGroup);
        }

        // Add example scripts
        if (data.scripts.example_scripts && Object.keys(data.scripts.example_scripts).length > 0) {
            const exampleGroup = document.createElement('optgroup');
            exampleGroup.label = '📚 SpigaMonde Examples (Python scripts from main project)';

            for (const [key, script] of Object.entries(data.scripts.example_scripts)) {
                const option = document.createElement('option');
                option.value = `example_${key}`;
                option.textContent = script.name;
                option.title = script.description;
                exampleGroup.appendChild(option);
            }

            scriptSelect.appendChild(exampleGroup);
        }

        // Add template scripts
        if (data.scripts.template_scripts && Object.keys(data.scripts.template_scripts).length > 0) {
            const templateGroup = document.createElement('optgroup');
            templateGroup.label = '📝 SpigaMonde Templates (Customizable script templates)';

            for (const [key, script] of Object.entries(data.scripts.template_scripts)) {
                const option = document.createElement('option');
                option.value = `template_${key}`;
                option.textContent = script.name;
                option.title = script.description;
                templateGroup.appendChild(option);
            }

            scriptSelect.appendChild(templateGroup);
        }

        logMessage(`Loaded ${data.total_count} available scripts`, 'success');

    } catch (error) {
        console.error('Failed to load available scripts:', error);
        logMessage(`Failed to load scripts: ${error.message}`, 'error');

        // Add fallback options
        scriptSelect.innerHTML = `
            <option value="">Choose a script...</option>
            <option value="basic-crawl">Basic Crawl (CLI)</option>
        `;
    }
}

/**
 * Handle script selection change
 */
function onScriptSelect() {
    const selectedScript = scriptSelect.value;
    console.log('Script selected:', selectedScript);

    if (selectedScript) {
        // Show parameters section
        if (scriptParams) {
            scriptParams.style.display = 'block';
        }

        // Enable execute button
        if (executeScriptBtn) {
            executeScriptBtn.disabled = false;
        }

        // Enable save config button
        if (saveConfigBtn) {
            saveConfigBtn.disabled = false;
        }

        // Set default parameters based on script type
        setDefaultParameters(selectedScript);

        logMessage(`Selected script: ${selectedScript}`, 'info');
    } else {
        // Hide parameters section
        if (scriptParams) {
            scriptParams.style.display = 'none';
        }

        // Disable buttons
        if (executeScriptBtn) {
            executeScriptBtn.disabled = true;
        }
        if (saveConfigBtn) {
            saveConfigBtn.disabled = true;
        }
    }
}

/**
 * Set default parameters for selected script
 */
function setDefaultParameters(scriptType) {
    if (!targetUrl || !maxDepth || !maxPages || !crawlDelay) return;

    switch (scriptType) {
        case 'basic-crawl':
            maxDepth.value = 2;
            maxPages.value = 25;
            crawlDelay.value = 1.0;
            if (followExternal) followExternal.checked = false;
            if (downloadMedia) downloadMedia.checked = false;
            if (analyzeContent) analyzeContent.checked = true;
            break;

        case 'deep-crawl':
            maxDepth.value = 5;
            maxPages.value = 100;
            crawlDelay.value = 0.5;
            if (followExternal) followExternal.checked = true;
            if (downloadMedia) downloadMedia.checked = true;
            if (analyzeContent) analyzeContent.checked = true;
            break;

        case 'site-map':
            maxDepth.value = 10;
            maxPages.value = 500;
            crawlDelay.value = 0.2;
            if (followExternal) followExternal.checked = false;
            if (downloadMedia) downloadMedia.checked = false;
            if (analyzeContent) analyzeContent.checked = false;
            break;

        case 'custom':
            // Keep current values
            break;
    }
}

/**
 * Execute selected script with parameters
 */
async function executeScript() {
    if (!scriptSelect || !scriptSelect.value) {
        logMessage('No script selected', 'error');
        return;
    }

    if (!targetUrl || !targetUrl.value) {
        logMessage('Target URL is required', 'error');
        return;
    }

    console.log('Executing script...');
    logMessage('Starting script execution...', 'info');

    // Update UI state
    setButtonLoading(executeScriptBtn, true);
    if (stopScriptBtn) stopScriptBtn.disabled = false;

    // Update status
    updateScriptStatus('🚀', 'Executing script...', 'running');

    // Show progress
    if (scriptProgress) {
        scriptProgress.style.display = 'block';
        updateProgress(0, 'Initializing...');
    }

    try {
        // Collect parameters
        const scriptConfig = {
            script_type: scriptSelect.value,
            target_url: targetUrl.value,
            max_depth: parseInt(maxDepth?.value || 3),
            max_pages: parseInt(maxPages?.value || 50),
            crawl_delay: parseFloat(crawlDelay?.value || 1.0),
            follow_external: followExternal?.checked || false,
            download_media: downloadMedia?.checked || false,
            analyze_content: analyzeContent?.checked || true
        };

        console.log('Script config:', scriptConfig);

        // Execute real script via API (no fallback) - don't wait for completion
        executeRealScript(scriptConfig);

    } catch (error) {
        console.error('Script execution failed:', error);
        updateScriptStatus('❌', `Script failed: ${error.message}`, 'error');
        logMessage(`Script execution failed: ${error.message}`, 'error');

    } finally {
        setButtonLoading(executeScriptBtn, false);
        if (stopScriptBtn) stopScriptBtn.disabled = true;
    }
}

/**
 * Execute real script via backend API
 */
async function executeRealScript(config) {
    try {
        // Start progress simulation immediately
        updateProgress(10, 'Script execution started...');
        logMessage('Starting script execution...', 'info');

        // Start progress simulation in parallel
        const progressPromise = simulateProgressForRealScript({ execution_id: 'pending' });

        // Call the real script execution endpoint
        const response = await fetch(`${API_BASE_URL}/api/execute-script`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('Script execution completed:', result);

        // Wait for progress simulation to complete
        await progressPromise;

        // Show final results (in a real implementation, you'd poll for actual results)
        const finalResults = {
            execution_id: result.execution_id,
            script_type: result.script_type,
            target_url: result.target_url,
            status: 'completed',
            estimated_duration: result.estimated_duration,
            message: 'Real script execution initiated successfully'
        };

        showScriptResults(finalResults);
        updateScriptStatus('✅', 'Real script execution started', 'success');
        addToHistory(config.script_type, 'started');

        logMessage('Real script execution initiated successfully', 'success');

    } catch (error) {
        console.error('Real script execution failed:', error);
        logMessage(`Real script execution failed: ${error.message}`, 'error');

        // Show error status and results
        updateScriptStatus('❌', `Script execution failed: ${error.message}`, 'error');

        const errorResults = {
            error: error.message,
            timestamp: new Date().toISOString(),
            script_type: config.script_type,
            target_url: config.target_url,
            status: 'failed'
        };

        showScriptResults(errorResults);
        addToHistory(config.script_type, 'error');

        // Re-throw error to be handled by calling function
        throw error;
    }
}

/**
 * Simulate progress updates for real script execution
 */
async function simulateProgressForRealScript(executionResult) {
    const steps = [
        { progress: 20, message: 'Initializing Scrapy crawler...' },
        { progress: 30, message: 'Validating target URL...' },
        { progress: 40, message: 'Starting content spider...' },
        { progress: 60, message: 'Crawl in progress (check backend logs)...' },
        { progress: 80, message: 'Processing discovered content...' },
        { progress: 95, message: 'Finalizing results...' },
        { progress: 100, message: 'Script execution initiated!' }
    ];

    for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800));
        updateProgress(step.progress, step.message);
    }
}

/**
 * Simulate script execution with progress updates (FALLBACK)
 */
async function simulateScriptExecution(config) {
    const steps = [
        { progress: 10, message: 'Validating target URL...' },
        { progress: 25, message: 'Initializing crawler...' },
        { progress: 40, message: 'Discovering pages...' },
        { progress: 60, message: 'Processing content...' },
        { progress: 80, message: 'Analyzing data...' },
        { progress: 95, message: 'Generating report...' },
        { progress: 100, message: 'Complete!' }
    ];

    for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        updateProgress(step.progress, step.message);
    }

    // Show results
    const results = {
        pages_crawled: Math.floor(Math.random() * config.max_pages) + 1,
        content_analyzed: Math.floor(Math.random() * 50) + 10,
        links_found: Math.floor(Math.random() * 200) + 50,
        execution_time: '45.2s',
        status: 'success'
    };

    showScriptResults(results);
    updateScriptStatus('✅', 'Script completed successfully', 'success');
    addToHistory(config.script_type, 'success');

    logMessage('Script execution completed successfully', 'success');
}

/**
 * Update progress bar and message
 */
function updateProgress(percentage, message) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const progressDetails = document.getElementById('progressDetails');

    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }
    if (progressText) {
        progressText.textContent = `${percentage}%`;
    }
    if (progressDetails) {
        progressDetails.textContent = message;
    }
}

/**
 * Update script status indicator
 */
function updateScriptStatus(icon, text, type) {
    if (!scriptStatus) return;

    const statusIcon = scriptStatus.querySelector('.status-icon');
    const statusText = scriptStatus.querySelector('.status-text');

    if (statusIcon) statusIcon.textContent = icon;
    if (statusText) statusText.textContent = text;

    // Update status styling
    scriptStatus.className = `script-status ${type}`;
}

/**
 * Show script execution results
 */
function showScriptResults(results) {
    if (!scriptResults) return;

    const summaryWidget = document.getElementById('scriptSummaryWidget');
    const scriptOutput = document.getElementById('scriptOutput');

    if (summaryWidget) {
        // Handle different result types
        if (results.error) {
            // Error results
            summaryWidget.innerHTML = `
                <div class="summary-stat">
                    <div class="summary-stat-value error">${results.status || 'FAILED'}</div>
                    <div class="summary-stat-label">Status</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.script_type || 'Unknown'}</div>
                    <div class="summary-stat-label">Script Type</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value error">ERROR</div>
                    <div class="summary-stat-label">Result</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.timestamp ? new Date(results.timestamp).toLocaleTimeString() : 'N/A'}</div>
                    <div class="summary-stat-label">Time</div>
                </div>
            `;
        } else if (results.execution_id) {
            // Real script execution results
            summaryWidget.innerHTML = `
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.script_type}</div>
                    <div class="summary-stat-label">Script Type</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.execution_id}</div>
                    <div class="summary-stat-label">Execution ID</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value success">${results.status || 'STARTED'}</div>
                    <div class="summary-stat-label">Status</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.estimated_duration || 'N/A'}</div>
                    <div class="summary-stat-label">Est. Duration</div>
                </div>
            `;
        } else {
            // Fallback for other result types
            summaryWidget.innerHTML = `
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.pages_crawled || 0}</div>
                    <div class="summary-stat-label">Pages Crawled</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.content_analyzed || 0}</div>
                    <div class="summary-stat-label">Content Analyzed</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.links_found || 0}</div>
                    <div class="summary-stat-label">Links Found</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value">${results.execution_time || 'N/A'}</div>
                    <div class="summary-stat-label">Execution Time</div>
                </div>
            `;
        }
    }

    if (scriptOutput) {
        // Show actual script output if available, otherwise show JSON
        if (results.output) {
            // Display the actual script output in a readable format
            scriptOutput.innerHTML = `
                <div class="script-output-section">
                    <h4>Script Output:</h4>
                    <pre class="script-output-text">${results.output}</pre>
                </div>
                ${results.warnings ? `
                <div class="script-warnings-section">
                    <h4>Warnings:</h4>
                    <pre class="script-warnings-text">${results.warnings}</pre>
                </div>
                ` : ''}
                <div class="script-metadata-section">
                    <h4>Execution Details:</h4>
                    <pre class="script-metadata-text">Execution ID: ${results.execution_id || 'N/A'}
Method: ${results.execution_method || 'N/A'}
Return Code: ${results.return_code || 'N/A'}
Timestamp: ${results.timestamp || 'N/A'}</pre>
                </div>
            `;
        } else {
            // Fallback to JSON display
            scriptOutput.textContent = JSON.stringify(results, null, 2);
        }
    }

    scriptResults.style.display = 'block';
}

/**
 * Stop script execution
 */
function stopScript() {
    console.log('Stopping script...');
    logMessage('Script execution stopped by user', 'info');

    updateScriptStatus('⏹️', 'Script stopped', 'stopped');

    if (scriptProgress) {
        scriptProgress.style.display = 'none';
    }

    setButtonLoading(executeScriptBtn, false);
    if (stopScriptBtn) stopScriptBtn.disabled = true;
}

/**
 * Save script configuration
 */
function saveScriptConfig() {
    if (!scriptSelect || !scriptSelect.value) {
        logMessage('No script selected to save', 'error');
        return;
    }

    const config = {
        script_type: scriptSelect.value,
        target_url: targetUrl?.value || '',
        max_depth: parseInt(maxDepth?.value || 3),
        max_pages: parseInt(maxPages?.value || 50),
        crawl_delay: parseFloat(crawlDelay?.value || 1.0),
        follow_external: followExternal?.checked || false,
        download_media: downloadMedia?.checked || false,
        analyze_content: analyzeContent?.checked || true,
        saved_at: new Date().toISOString()
    };

    // Save to localStorage (in a real app, this would be saved to backend)
    const configName = `${config.script_type}_${Date.now()}`;
    localStorage.setItem(`script_config_${configName}`, JSON.stringify(config));

    logMessage(`Script configuration saved as: ${configName}`, 'success');
}

/**
 * Add execution to history
 */
function addToHistory(scriptType, status) {
    if (!scriptHistory) return;

    const timestamp = new Date().toLocaleTimeString();
    const historyItem = document.createElement('div');
    historyItem.className = 'history-item';

    historyItem.innerHTML = `
        <span class="history-time">${timestamp}</span>
        <span class="history-script">${scriptType}</span>
        <span class="history-status ${status}">${status.toUpperCase()}</span>
    `;

    // Remove "no recent executions" message if it exists
    const noExecutions = scriptHistory.querySelector('.history-item');
    if (noExecutions && noExecutions.textContent.includes('No recent executions')) {
        scriptHistory.innerHTML = '';
    }

    scriptHistory.insertBefore(historyItem, scriptHistory.firstChild);

    // Keep only last 10 executions
    const items = scriptHistory.querySelectorAll('.history-item');
    if (items.length > 10) {
        items[items.length - 1].remove();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', init);
