"""Tests for content analysis and classification functionality."""

import pytest
from unittest.mock import Mock, patch

from spigamonde.analysis.classifier import ContentClassifier, ContentCategory, QualityScore
from spigamonde.analysis.analyzer import ContentAnalyzer, ContentMetadata


class TestContentClassifier:
    """Test content classification functionality."""
    
    def setup_method(self):
        """Setup test classifier."""
        self.classifier = ContentClassifier()
    
    def test_classifier_initialization(self):
        """Test classifier initializes correctly."""
        assert self.classifier is not None
        assert hasattr(self.classifier, 'url_patterns')
        assert hasattr(self.classifier, 'content_keywords')
    
    def test_classify_by_url_academic(self):
        """Test URL-based classification for academic papers."""
        url = "https://arxiv.org/pdf/2023.12345.pdf"
        result = self.classifier.classify_content(url=url)
        
        assert result.category == ContentCategory.ACADEMIC_PAPER
        assert result.confidence > 0.5
        assert "URL pattern match" in result.reasoning[0]
    
    def test_classify_by_url_news(self):
        """Test URL-based classification for news articles."""
        url = "https://www.bbc.com/news/world-12345"
        result = self.classifier.classify_content(url=url)
        
        assert result.category == ContentCategory.NEWS_ARTICLE
        assert result.confidence > 0.5
    
    def test_classify_by_extension(self):
        """Test file extension-based classification."""
        url = "https://example.com/data.csv"
        result = self.classifier.classify_content(url=url, file_path="/path/to/data.csv")
        
        assert result.category == ContentCategory.DATASET
        assert result.confidence > 0.5
    
    def test_classify_by_content_academic(self):
        """Test content-based classification for academic papers."""
        url = "https://example.com/paper.pdf"
        content = """
        Abstract: This paper presents a novel approach to machine learning.
        
        1. Introduction
        The methodology used in this research follows established protocols.
        
        2. Results
        Our findings demonstrate significant improvements.
        
        3. Conclusion
        This study contributes to the field of artificial intelligence.
        
        References:
        [1] Smith, J. et al. (2023). Journal of AI Research.
        """
        
        result = self.classifier.classify_content(url=url, content=content)
        
        assert result.category == ContentCategory.ACADEMIC_PAPER
        assert result.confidence > 0.3
    
    def test_quality_score_calculation(self):
        """Test quality score calculation."""
        # High quality content
        high_quality_content = """
        This is a comprehensive, peer-reviewed article published in a reputable journal.
        The content is detailed, accurate, and provides authoritative information.
        """
        
        url = "https://example.com/article"
        result = self.classifier.classify_content(url=url, content=high_quality_content)
        
        assert result.quality_score in [QualityScore.GOOD, QualityScore.EXCELLENT]
        
        # Low quality content
        low_quality_content = """
        Lorem ipsum dolor sit amet. Click here for amazing deals!
        This page is under construction. Buy now, limited time offer!
        """
        
        result = self.classifier.classify_content(url=url, content=low_quality_content)
        
        assert result.quality_score in [QualityScore.POOR, QualityScore.VERY_POOR]
    
    def test_unknown_content_classification(self):
        """Test classification of unknown content."""
        url = "https://example.com/unknown"
        content = "Random text without clear indicators."
        
        result = self.classifier.classify_content(url=url, content=content)
        
        # Should default to unknown with low confidence
        assert result.category == ContentCategory.UNKNOWN or result.confidence < 0.5


class TestContentAnalyzer:
    """Test content analysis functionality."""
    
    def setup_method(self):
        """Setup test analyzer."""
        self.analyzer = ContentAnalyzer()
    
    def test_analyzer_initialization(self):
        """Test analyzer initializes correctly."""
        assert self.analyzer is not None
        assert hasattr(self.analyzer, 'classifier')
        assert hasattr(self.analyzer, 'patterns')
    
    def test_metadata_extraction_basic(self):
        """Test basic metadata extraction."""
        url = "https://example.com/test"
        content = """
        This is a test document with multiple paragraphs.
        
        It contains several sentences and words for analysis.
        The content should be analyzed for various metrics.
        """
        
        result = self.analyzer.analyze_content(url=url, content=content)
        
        assert result.success is True
        assert result.metadata.word_count > 0
        assert result.metadata.character_count > 0
        assert result.metadata.line_count > 0
        assert result.metadata.paragraph_count > 0
        assert result.metadata.content_hash is not None
    
    def test_html_metadata_extraction(self):
        """Test HTML metadata extraction."""
        url = "https://example.com/page"
        html_content = """
        <html>
        <head>
            <title>Test Page Title</title>
            <meta name="description" content="This is a test page description">
            <meta name="author" content="Test Author">
        </head>
        <body>
            <h1>Main Heading</h1>
            <h2>Subheading</h2>
            <p>This is the main content of the page.</p>
        </body>
        </html>
        """
        
        result = self.analyzer.analyze_content(url=url, content=html_content)
        
        assert result.metadata.title == "Test Page Title"
        assert result.metadata.description == "This is a test page description"
        assert result.metadata.author == "Test Author"
        assert "Main Heading" in result.metadata.topics
        assert "Subheading" in result.metadata.topics
    
    def test_entity_extraction(self):
        """Test entity extraction from content."""
        url = "https://example.com/contact"
        content = """
        Contact <NAME_EMAIL> or <EMAIL>
        Call us at ******-123-4567 or (*************
        Visit our website at https://www.example.com
        Meeting scheduled for 12/25/2023 and 2024-01-15
        """
        
        result = self.analyzer.analyze_content(url=url, content=content)
        
        assert len(result.metadata.emails) >= 2
        assert "<EMAIL>" in result.metadata.emails
        assert len(result.metadata.phone_numbers) >= 1
        assert len(result.metadata.urls) >= 1
        assert len(result.metadata.dates) >= 1
    
    def test_language_detection(self):
        """Test language detection."""
        # English content
        english_content = "This is an English document with common English words like the, and, of, to, a, in."
        result = self.analyzer.analyze_content("https://example.com", content=english_content)
        assert result.metadata.language == "english"
        
        # Spanish content
        spanish_content = "Este es un documento en español con palabras comunes como el, la, de, que, y, en."
        result = self.analyzer.analyze_content("https://example.com", content=spanish_content)
        assert result.metadata.language == "spanish"
    
    def test_keyword_extraction(self):
        """Test keyword extraction."""
        url = "https://example.com/tech"
        content = """
        Machine learning and artificial intelligence are transforming technology.
        Deep learning algorithms use neural networks for pattern recognition.
        Data science involves analyzing large datasets using statistical methods.
        Python programming language is popular for machine learning projects.
        """
        
        result = self.analyzer.analyze_content(url=url, content=content)
        
        assert len(result.metadata.keywords) > 0
        # Should contain relevant technical terms
        keywords_text = " ".join(result.metadata.keywords)
        assert any(term in keywords_text for term in ["machine", "learning", "data", "python"])
    
    def test_readability_calculation(self):
        """Test readability score calculation."""
        # Simple, readable content
        simple_content = "This is easy to read. Short sentences. Simple words."
        result = self.analyzer.analyze_content("https://example.com", content=simple_content)
        simple_score = result.metadata.readability_score
        
        # Complex content
        complex_content = """
        The implementation of sophisticated algorithmic methodologies necessitates 
        comprehensive understanding of multidimensional computational frameworks 
        and their interdisciplinary applications across various technological domains.
        """
        result = self.analyzer.analyze_content("https://example.com", content=complex_content)
        complex_score = result.metadata.readability_score
        
        # Simple content should have higher readability score
        assert simple_score > complex_score
    
    def test_analysis_error_handling(self):
        """Test error handling in content analysis."""
        # Test with None content
        result = self.analyzer.analyze_content("https://example.com", content=None)
        assert result.success is True  # Should handle gracefully
        assert result.metadata.word_count == 0
        
        # Test with empty content
        result = self.analyzer.analyze_content("https://example.com", content="")
        assert result.success is True
        assert result.metadata.word_count == 0
    
    @patch('spigamonde.analysis.analyzer.ContentAnalyzer._extract_metadata')
    def test_analysis_exception_handling(self, mock_extract):
        """Test exception handling during analysis."""
        import time

        def slow_exception(*args, **kwargs):
            time.sleep(0.001)  # Small delay to ensure measurable time
            raise Exception("Test error")

        mock_extract.side_effect = slow_exception

        result = self.analyzer.analyze_content("https://example.com", content="test")

        assert result.success is False
        assert result.error_message == "Test error"
        assert result.analysis_time > 0


class TestContentAnalysisIntegration:
    """Test integration between classifier and analyzer."""
    
    def test_full_analysis_pipeline(self):
        """Test complete analysis pipeline."""
        analyzer = ContentAnalyzer()
        
        url = "https://arxiv.org/pdf/2023.12345.pdf"
        content = """
        Abstract: This paper presents a comprehensive study of neural networks.
        
        Introduction: Machine learning has revolutionized artificial intelligence.
        The methodology involves deep learning algorithms and statistical analysis.
        
        Results: Our experiments demonstrate significant improvements in accuracy.
        The findings contribute to the field of computer science research.
        
        Conclusion: This work advances the state of the art in AI research.
        
        References:
        [1] Smith, J. (2023). Journal of Machine Learning Research.
        [2] Doe, A. (2022). Proceedings of Neural Information Processing Systems.
        """
        
        result = analyzer.analyze_content(
            url=url,
            content=content,
            file_path="/path/to/paper.pdf",
            mime_type="application/pdf"
        )
        
        # Should be classified as academic paper
        assert result.classification.category == ContentCategory.ACADEMIC_PAPER
        assert result.classification.confidence > 0.5
        
        # Should have good quality score
        assert result.classification.quality_score.value >= 3
        
        # Should extract meaningful metadata
        assert result.metadata.word_count > 0
        assert result.metadata.language is not None
        assert len(result.metadata.keywords) > 0
        
        # Should have reasonable analysis time
        assert result.analysis_time > 0
        assert result.analysis_time < 10  # Should be fast
        
        # Should be successful
        assert result.success is True
        assert result.error_message is None
