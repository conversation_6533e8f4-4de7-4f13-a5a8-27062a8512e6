/**
 * Dashboard Module
 * Quick access widgets for common operations
 */

// DOM Elements
let testConnectionBtn, getStatsBtn, runScriptBtn;
let connectionResult, statsResult, scriptResult;
let connectionData, statsData, scriptData, scriptSummary;

/**
 * Initialize dashboard module
 */
async function initDashboard() {
    // Get DOM elements
    testConnectionBtn = document.getElementById('testConnectionBtn');
    getStatsBtn = document.getElementById('getStatsBtn');
    runScriptBtn = document.getElementById('runScriptBtn');
    
    connectionResult = document.getElementById('connectionResult');
    statsResult = document.getElementById('statsResult');
    scriptResult = document.getElementById('scriptResult');
    
    connectionData = document.getElementById('connectionData');
    statsData = document.getElementById('statsData');
    scriptData = document.getElementById('scriptData');
    scriptSummary = document.getElementById('scriptSummary');
    
    // Add event listeners
    testConnectionBtn.addEventListener('click', testConnection);
    getStatsBtn.addEventListener('click', getSpigamondeStats);
    runScriptBtn.addEventListener('click', runTestScript);
    
    console.log('Dashboard module initialized');
}

/**
 * Test connection to backend
 */
async function testConnection() {
    logMessage('Testing backend connection...', 'info');
    
    // Update button state
    setButtonLoading(testConnectionBtn, true);
    hideResult(connectionResult);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/test-connection`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Display success result
        connectionData.textContent = JSON.stringify(data, null, 2);
        connectionResult.classList.remove('error');
        connectionResult.style.display = 'block';
        
        logMessage('Connection test successful', 'success');
        
    } catch (error) {
        // Display error result
        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/test-connection'
        };
        
        connectionData.textContent = JSON.stringify(errorData, null, 2);
        connectionResult.classList.add('error');
        connectionResult.style.display = 'block';
        
        logMessage(`Connection test failed: ${error.message}`, 'error');
        
    } finally {
        setButtonLoading(testConnectionBtn, false);
    }
}

/**
 * Get SpigaMonde statistics
 */
async function getSpigamondeStats() {
    logMessage('Fetching SpigaMonde statistics...', 'info');
    
    // Update button state
    setButtonLoading(getStatsBtn, true);
    hideResult(statsResult);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/spigamonde-stats`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Display success result
        statsData.textContent = JSON.stringify(data, null, 2);
        statsResult.classList.remove('error');
        statsResult.style.display = 'block';
        
        logMessage('Statistics retrieved successfully', 'success');
        
    } catch (error) {
        // Display error result
        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/spigamonde-stats'
        };
        
        statsData.textContent = JSON.stringify(errorData, null, 2);
        statsResult.classList.add('error');
        statsResult.style.display = 'block';
        
        logMessage(`Statistics fetch failed: ${error.message}`, 'error');
        
    } finally {
        setButtonLoading(getStatsBtn, false);
    }
}

/**
 * Run test script
 */
async function runTestScript() {
    logMessage('Starting test script execution...', 'info');
    
    // Update button state
    setButtonLoading(runScriptBtn, true);
    hideResult(scriptResult);
    
    try {
        const response = await fetch(`${API_BASE_URL}/api/run-script`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Display success result
        showScriptResult(scriptResult, data, false);
        logMessage('Test script completed successfully', 'success');
        
    } catch (error) {
        // Display error result
        const errorData = {
            error: error.message,
            timestamp: new Date().toISOString(),
            endpoint: '/api/run-script'
        };
        
        showScriptResult(scriptResult, errorData, true);
        logMessage(`Test script failed: ${error.message}`, 'error');
        
    } finally {
        setButtonLoading(runScriptBtn, false);
    }
}

/**
 * Show script result
 */
function showScriptResult(resultElement, data, isError = false) {
    // Update styling
    if (isError) {
        resultElement.classList.add('error');
        
        // Show simple error for script failures
        scriptSummary.innerHTML = `
            <div class="summary-item">
                <span class="summary-label">Status:</span>
                <span class="summary-value error">Error</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Error:</span>
                <span class="summary-value error">${data.error || 'Unknown error'}</span>
            </div>
        `;
        
        scriptData.textContent = JSON.stringify(data, null, 2);
        
    } else {
        resultElement.classList.remove('error');
        
        // Create summary display
        const results = data.results || {};
        const summary = data.summary || {};
        
        let summaryHtml = `
            <div class="summary-item">
                <span class="summary-label">Overall Status:</span>
                <span class="summary-value ${summary.overall_status === 'SUCCESS' ? 'success' : 'error'}">${summary.overall_status || 'Unknown'}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Tests Passed:</span>
                <span class="summary-value">${summary.successful_tests || 0}/${summary.total_tests || 0}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Execution Time:</span>
                <span class="summary-value">${summary.execution_time || 'Unknown'}</span>
            </div>
        `;
        
        scriptSummary.innerHTML = summaryHtml;
        scriptData.textContent = JSON.stringify(data, null, 2);
    }
    
    // Show the result
    resultElement.style.display = 'block';
}

// Export functions
window.initDashboard = initDashboard;
window.testConnection = testConnection;
window.getSpigamondeStats = getSpigamondeStats;
window.runTestScript = runTestScript;
