#!/usr/bin/env python3
"""
SpigaMonde Configuration Switcher
Easily switch between testing and production configurations.

Usage:
    python scripts/switch_config.py testing
    python scripts/switch_config.py production
    python scripts/switch_config.py status
"""

import sys
import shutil
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

def get_project_root():
    """Get the project root directory."""
    return Path(__file__).parent.parent

def get_current_config():
    """Get current configuration details."""
    project_root = get_project_root()
    env_file = project_root / '.env'
    
    if not env_file.exists():
        return None, "No .env file found"
    
    # Read current .env file
    with open(env_file, 'r') as f:
        content = f.read()
    
    # Check user agent to determine config type
    if 'TestCrawler' in content:
        return 'testing', 'TestCrawler/1.0 (+https://example.com/testcrawler)'
    elif 'SpigaMonde' in content:
        return 'production', 'SpigaMonde/0.1.0 (+https://github.com/spigamonde/spigamonde)'
    else:
        return 'unknown', 'Unknown user agent'

def switch_to_testing():
    """Switch to testing configuration."""
    project_root = get_project_root()
    testing_config = project_root / '.env.testing'
    env_file = project_root / '.env'
    
    if not testing_config.exists():
        console.print("[red]✗ Testing configuration file not found: .env.testing[/red]")
        return False
    
    try:
        # Backup current .env if it exists
        if env_file.exists():
            backup_file = project_root / '.env.backup'
            shutil.copy2(env_file, backup_file)
            console.print(f"[yellow]📄 Current .env backed up to .env.backup[/yellow]")
        
        # Copy testing config to .env
        shutil.copy2(testing_config, env_file)
        
        console.print("[green]✅ Switched to TESTING configuration[/green]")
        console.print("[yellow]⚠️  User-Agent: TestCrawler/1.0[/yellow]")
        console.print("[dim]Downloads will go to: ./test_downloads[/dim]")
        return True
        
    except Exception as e:
        console.print(f"[red]✗ Failed to switch configuration: {e}[/red]")
        return False

def switch_to_production():
    """Switch to production configuration."""
    project_root = get_project_root()
    production_config = project_root / '.env.production'
    env_file = project_root / '.env'
    
    if not production_config.exists():
        console.print("[red]✗ Production configuration file not found: .env.production[/red]")
        return False
    
    try:
        # Backup current .env if it exists
        if env_file.exists():
            backup_file = project_root / '.env.backup'
            shutil.copy2(env_file, backup_file)
            console.print(f"[yellow]📄 Current .env backed up to .env.backup[/yellow]")
        
        # Copy production config to .env
        shutil.copy2(production_config, env_file)
        
        console.print("[green]✅ Switched to PRODUCTION configuration[/green]")
        console.print("[blue]🕷️  User-Agent: SpigaMonde/0.1.0[/blue]")
        console.print("[dim]Downloads will go to: ./downloads[/dim]")
        return True
        
    except Exception as e:
        console.print(f"[red]✗ Failed to switch configuration: {e}[/red]")
        return False

def show_status():
    """Show current configuration status."""
    config_type, user_agent = get_current_config()
    
    if config_type is None:
        console.print(Panel(
            "[red]No .env configuration file found[/red]\n\n"
            "Run: [cyan]python scripts/switch_config.py testing[/cyan]\n"
            "Or:  [cyan]python scripts/switch_config.py production[/cyan]",
            title="❌ Configuration Status",
            border_style="red"
        ))
        return
    
    # Create status table
    table = Table(title="Current Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="yellow")
    
    table.add_row("Configuration Type", config_type.upper())
    table.add_row("User-Agent", user_agent)
    
    if config_type == 'testing':
        table.add_row("Downloads Directory", "./test_downloads")
        table.add_row("Log Level", "DEBUG")
        table.add_row("Max File Size", "10MB")
        table.add_row("Max Pages", "20")
        
        console.print(Panel(
            table,
            title="🧪 TESTING Configuration Active",
            border_style="yellow"
        ))
        
        console.print("\n[yellow]⚠️  Currently in TESTING mode[/yellow]")
        console.print("To switch to production: [cyan]python scripts/switch_config.py production[/cyan]")
        
    elif config_type == 'production':
        table.add_row("Downloads Directory", "./downloads")
        table.add_row("Log Level", "INFO")
        table.add_row("Max File Size", "100MB")
        table.add_row("Max Pages", "Unlimited")
        
        console.print(Panel(
            table,
            title="🚀 PRODUCTION Configuration Active",
            border_style="green"
        ))
        
        console.print("\n[green]✅ Currently in PRODUCTION mode[/green]")
        console.print("To switch to testing: [cyan]python scripts/switch_config.py testing[/cyan]")
        
    else:
        console.print(Panel(
            f"[yellow]Unknown configuration detected[/yellow]\n\n"
            f"User-Agent: {user_agent}\n\n"
            "Consider switching to a known configuration.",
            title="❓ Unknown Configuration",
            border_style="yellow"
        ))

def main():
    """Main function."""
    if len(sys.argv) != 2:
        console.print(Panel(
            "[bold]SpigaMonde Configuration Switcher[/bold]\n\n"
            "Usage:\n"
            "  [cyan]python scripts/switch_config.py testing[/cyan]     - Switch to testing config\n"
            "  [cyan]python scripts/switch_config.py production[/cyan]  - Switch to production config\n"
            "  [cyan]python scripts/switch_config.py status[/cyan]      - Show current config\n\n"
            "Testing config uses: [yellow]TestCrawler/1.0[/yellow] user-agent\n"
            "Production config uses: [blue]SpigaMonde/0.1.0[/blue] user-agent",
            title="🔧 Configuration Switcher",
            border_style="blue"
        ))
        return 1
    
    command = sys.argv[1].lower()
    
    if command == 'testing':
        success = switch_to_testing()
        if success:
            console.print("\n[green]🎯 Ready for testing![/green]")
            console.print("You can now run crawls with the TestCrawler user-agent.")
        return 0 if success else 1
        
    elif command == 'production':
        success = switch_to_production()
        if success:
            console.print("\n[green]🚀 Ready for production![/green]")
            console.print("You can now run crawls with the official SpigaMonde user-agent.")
        return 0 if success else 1
        
    elif command == 'status':
        show_status()
        return 0
        
    else:
        console.print(f"[red]✗ Unknown command: {command}[/red]")
        console.print("Valid commands: testing, production, status")
        return 1

if __name__ == "__main__":
    sys.exit(main())
