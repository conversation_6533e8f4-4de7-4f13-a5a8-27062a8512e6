#!/usr/bin/env python3
"""
SpigaMonde Web Interface - Single Script Launcher
Starts both backend and frontend servers with one command
Usage: python start.py
"""

import os
import sys
import subprocess
import threading
import time
import signal
from pathlib import Path

class WebUILauncher:
    def __init__(self):
        self.web_dir = Path(__file__).parent.absolute()
        self.project_root = self.web_dir.parent
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_requirements(self):
        """Verify all required files exist"""
        backend_script = self.web_dir / "backend" / "main.py"
        frontend_index = self.web_dir / "frontend" / "index.html"
        
        if not backend_script.exists():
            print(f"❌ Backend not found: {backend_script}")
            return False
            
        if not frontend_index.exists():
            print(f"❌ Frontend not found: {frontend_index}")
            return False
            
        return True
    
    def start_backend(self):
        """Start the FastAPI backend server"""
        print("🚀 Starting backend server...")
        
        # Change to web directory for uvicorn
        os.chdir(self.web_dir)
        
        cmd = [
            "uv", "run", "uvicorn", 
            "backend.main:app", 
            "--reload", 
            "--host", "127.0.0.1", 
            "--port", "8000"
        ]
        
        try:
            self.backend_process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT,
                text=True
            )
            print("✅ Backend started on http://127.0.0.1:8000")
            return True
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False
    
    def start_frontend(self):
        """Start the frontend HTTP server"""
        print("🚀 Starting frontend server...")
        
        # Change to frontend directory
        frontend_dir = self.web_dir / "frontend"
        os.chdir(frontend_dir)
        
        cmd = ["python", "-m", "http.server", "3000"]
        
        try:
            self.frontend_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )
            print("✅ Frontend started on http://localhost:3000")
            return True
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False
    
    def stop_servers(self):
        """Stop both servers"""
        print("\n🛑 Stopping servers...")
        self.running = False
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend stopped")
            except:
                self.backend_process.kill()
                print("🔥 Backend force-killed")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend stopped")
            except:
                self.frontend_process.kill()
                print("🔥 Frontend force-killed")
    
    def monitor_processes(self):
        """Monitor both processes and restart if needed"""
        while self.running:
            time.sleep(2)
            
            # Check backend
            if self.backend_process and self.backend_process.poll() is not None:
                if self.running:
                    print("⚠️  Backend crashed, restarting...")
                    self.start_backend()
            
            # Check frontend
            if self.frontend_process and self.frontend_process.poll() is not None:
                if self.running:
                    print("⚠️  Frontend crashed, restarting...")
                    self.start_frontend()
    
    def run(self):
        """Main execution method"""
        print("🌐 SpigaMonde Web Interface Launcher")
        print("=" * 50)
        
        # Check requirements
        if not self.check_requirements():
            sys.exit(1)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, lambda s, f: self.stop_servers())
        signal.signal(signal.SIGTERM, lambda s, f: self.stop_servers())
        
        # Start servers
        if not self.start_backend():
            sys.exit(1)
        
        time.sleep(3)  # Give backend time to start
        
        if not self.start_frontend():
            self.stop_servers()
            sys.exit(1)
        
        print("\n🎉 SpigaMonde Web Interface is ready!")
        print("📊 Frontend: http://localhost:3000")
        print("🔧 Backend:  http://127.0.0.1:8000")
        print("📚 API Docs: http://127.0.0.1:8000/docs")
        print("\nPress Ctrl+C to stop both servers")
        print("=" * 50)
        
        try:
            # Monitor processes
            self.monitor_processes()
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_servers()
            print("👋 SpigaMonde Web Interface stopped")

def main():
    """Entry point"""
    launcher = WebUILauncher()
    launcher.run()

if __name__ == "__main__":
    main()
