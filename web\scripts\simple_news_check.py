#!/usr/bin/env python3
"""
Simple News Check Script
Description: Check news sources and log findings (no complex crawling)
Execution Time: ~15 seconds
Resource Usage: Low (HTTP requests only, no Scrapy)
"""

import sys
import time
import requests
import json
from datetime import datetime
from pathlib import Path

# Debug configuration
DEBUG_MODE = True
EXECUTION_ID = f"news_check_{int(time.time())}"

def debug_log(stage, message, data=None):
    """ ***************************************
    Enhanced debug logging for GUI integration tracking
    **********************************************
    """
    timestamp = datetime.now().isoformat()

    log_entry = {
        "timestamp": timestamp,
        "execution_id": EXECUTION_ID,
        "stage": stage,
        "message": message,
        "data": data
    }

    if DEBUG_MODE:
        # Console output for immediate feedback
        print(f"[DEBUG] {timestamp} | {stage} | {message}")
        if data:
            print(f"[DEBUG] Data: {json.dumps(data, indent=2)}")

    # TODO: Could also write to a debug log file for GUI to read
    # debug_file = Path(__file__).parent.parent / "logs" / f"script_debug_{EXECUTION_ID}.json"
    # with open(debug_file, "a") as f:
    #     f.write(json.dumps(log_entry) + "\n")

def progress_update(percentage, stage, details=""):
    """Progress tracking for GUI integration"""
    progress_data = {
        "percentage": percentage,
        "stage": stage,
        "details": details,
        "timestamp": datetime.now().isoformat()
    }

    debug_log("PROGRESS", f"{percentage}% - {stage}", progress_data)

    # Simulate what GUI expects to see
    print(f"PROGRESS: {percentage}% - {stage}")
    if details:
        print(f"DETAILS: {details}")

def main():
    """Check news sources and log findings."""
    debug_log("STARTUP", "Script execution started", {"script": "simple_news_check.py", "execution_id": EXECUTION_ID})
    progress_update(0, "Initializing", "Starting simple news check...")

    try:
        # Add SpigaMonde to path
        project_root = Path(__file__).parent.parent.parent
        debug_log("SETUP", f"Project root detected: {project_root}")
        sys.path.insert(0, str(project_root))

        progress_update(10, "Loading Dependencies", "Importing SpigaMonde modules...")
        from spigamonde.database.connection import init_database
        from spigamonde.config.settings import get_settings
        debug_log("SETUP", "SpigaMonde modules imported successfully")

        # Initialize
        progress_update(20, "Database Initialization", "Connecting to SpigaMonde database...")
        debug_log("DATABASE", "Initializing database connection")
        init_database()
        debug_log("DATABASE", "Database initialized successfully")

        progress_update(30, "Configuration", "Loading SpigaMonde settings...")
        settings = get_settings()
        debug_log("CONFIG", "Settings loaded", {
            "user_agent": settings.spider.user_agent,
            "database_url": str(settings.database.url)
        })

        # News sources to check (simple HTTP requests)
        news_sources = [
            {"name": "BBC News", "url": "https://www.bbc.com/news", "expected": "BBC"},
            {"name": "Reuters", "url": "https://www.reuters.com", "expected": "Reuters"},
            {"name": "AP News", "url": "https://apnews.com", "expected": "Associated Press"}
        ]

        debug_log("SOURCES", f"Configured {len(news_sources)} news sources", {
            "sources": [s["name"] for s in news_sources]
        })

        results = []
        progress_update(40, "Source Checking", f"Starting checks on {len(news_sources)} news sources...")

        for i, source in enumerate(news_sources, 1):
            # Calculate progress for this source (40% to 80% range)
            source_progress = 40 + (40 * i / len(news_sources))
            progress_update(int(source_progress), "Checking Source", f"[{i}/{len(news_sources)}] {source['name']}")

            debug_log("SOURCE_START", f"Checking source {i}/{len(news_sources)}", {
                "source_name": source['name'],
                "url": source['url'],
                "expected_content": source['expected']
            })

            try:
                # Simple HTTP request with timeout
                debug_log("HTTP_REQUEST", f"Making HTTP request to {source['url']}")
                response = requests.get(
                    source['url'],
                    timeout=5,
                    headers={'User-Agent': settings.spider.user_agent}
                )

                debug_log("HTTP_RESPONSE", f"Received response", {
                    "status_code": response.status_code,
                    "content_length": len(response.text),
                    "headers": dict(response.headers)
                })
                
                if response.status_code == 200:
                    content_length = len(response.text)
                    has_expected = source['expected'].lower() in response.text.lower()

                    result = {
                        'source': source['name'],
                        'url': source['url'],
                        'status': 'success',
                        'status_code': response.status_code,
                        'content_length': content_length,
                        'has_expected_content': has_expected
                    }

                    debug_log("SOURCE_SUCCESS", f"Successfully checked {source['name']}", result)
                    print(f"      SUCCESS - {content_length} chars, expected content: {has_expected}")

                else:
                    result = {
                        'source': source['name'],
                        'url': source['url'],
                        'status': 'error',
                        'status_code': response.status_code,
                        'error': f"HTTP {response.status_code}"
                    }

                    debug_log("SOURCE_ERROR", f"HTTP error for {source['name']}", result)
                    print(f"      ERROR - HTTP {response.status_code}")

            except requests.RequestException as e:
                result = {
                    'source': source['name'],
                    'url': source['url'],
                    'status': 'error',
                    'error': str(e)
                }

                debug_log("SOURCE_EXCEPTION", f"Request exception for {source['name']}", {
                    "error": str(e),
                    "error_type": type(e).__name__
                })
                print(f"      ERROR - {e}")

            results.append(result)
            debug_log("SOURCE_COMPLETE", f"Completed check for {source['name']}")

            # Be polite to servers
            if i < len(news_sources):  # Don't sleep after last source
                debug_log("DELAY", "Waiting 1 second between requests")
                time.sleep(1)

        # Summary and final processing
        progress_update(85, "Processing Results", "Analyzing check results...")

        successful = len([r for r in results if r['status'] == 'success'])
        total = len(results)

        summary_data = {
            "total_sources": total,
            "successful": successful,
            "failed": total - successful,
            "success_rate": (successful / total * 100) if total > 0 else 0
        }

        debug_log("SUMMARY", "News check summary calculated", summary_data)

        progress_update(90, "Generating Report", "Creating detailed results...")
        print(f"\nNews check completed!")
        print(f"   Successful: {successful}/{total}")
        print(f"   Failed: {total - successful}/{total}")

        # Log detailed results
        debug_log("RESULTS_DETAIL", "Detailed results", {"results": results})
        print("\nDetailed results:")
        for result in results:
            if result['status'] == 'success':
                print(f"   SUCCESS {result['source']}: {result['content_length']} chars")
            else:
                print(f"   ERROR {result['source']}: {result.get('error', 'Unknown error')}")

        progress_update(100, "Complete", "News check completed successfully!")
        debug_log("COMPLETION", "Script execution completed successfully", {
            "execution_time": f"{time.time() - int(EXECUTION_ID.split('_')[-1])} seconds",
            "final_summary": summary_data
        })

        print(f"\nSimple news check completed successfully!")
        return 0

    except Exception as e:
        debug_log("FATAL_ERROR", "Script execution failed", {
            "error": str(e),
            "error_type": type(e).__name__
        })
        print(f"News check failed: {e}")
        import traceback
        traceback.print_exc()
        progress_update(0, "Failed", f"Script failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
