#!/usr/bin/env python3
"""
Simple News Check Script
Description: Check news sources and log findings (no complex crawling)
Execution Time: ~15 seconds
Resource Usage: Low (HTTP requests only, no Scrapy)
"""

import sys
import time
import requests
from pathlib import Path

def main():
    """Check news sources and log findings."""
    print("Starting simple news check...")
    
    try:
        # Add SpigaMonde to path
        project_root = Path(__file__).parent.parent.parent
        sys.path.insert(0, str(project_root))
        
        from spigamonde.database.connection import init_database
        from spigamonde.config.settings import get_settings
        
        # Initialize
        print("Initializing SpigaMonde...")
        init_database()
        settings = get_settings()

        # News sources to check (simple HTTP requests)
        news_sources = [
            {"name": "BBC News", "url": "https://www.bbc.com/news", "expected": "BBC"},
            {"name": "Reuters", "url": "https://www.reuters.com", "expected": "Reuters"},
            {"name": "AP News", "url": "https://apnews.com", "expected": "Associated Press"}
        ]

        results = []

        print(f"Checking {len(news_sources)} news sources...")
        
        for i, source in enumerate(news_sources, 1):
            print(f"   [{i}/{len(news_sources)}] Checking {source['name']}...")
            
            try:
                # Simple HTTP request with timeout
                response = requests.get(
                    source['url'], 
                    timeout=5,
                    headers={'User-Agent': settings.spider.user_agent}
                )
                
                if response.status_code == 200:
                    content_length = len(response.text)
                    has_expected = source['expected'].lower() in response.text.lower()
                    
                    result = {
                        'source': source['name'],
                        'url': source['url'],
                        'status': 'success',
                        'status_code': response.status_code,
                        'content_length': content_length,
                        'has_expected_content': has_expected
                    }
                    
                    print(f"      SUCCESS - {content_length} chars, expected content: {has_expected}")

                else:
                    result = {
                        'source': source['name'],
                        'url': source['url'],
                        'status': 'error',
                        'status_code': response.status_code,
                        'error': f"HTTP {response.status_code}"
                    }

                    print(f"      ERROR - HTTP {response.status_code}")
                
            except requests.RequestException as e:
                result = {
                    'source': source['name'],
                    'url': source['url'],
                    'status': 'error',
                    'error': str(e)
                }
                
                print(f"      ERROR - {e}")

            results.append(result)
            time.sleep(1)  # Be polite to servers

        # Summary
        successful = len([r for r in results if r['status'] == 'success'])
        total = len(results)

        print(f"\nNews check completed!")
        print(f"   Successful: {successful}/{total}")
        print(f"   Failed: {total - successful}/{total}")

        # Log results
        print("\nDetailed results:")
        for result in results:
            if result['status'] == 'success':
                print(f"   SUCCESS {result['source']}: {result['content_length']} chars")
            else:
                print(f"   ERROR {result['source']}: {result.get('error', 'Unknown error')}")

        print(f"\nSimple news check completed successfully!")
        return 0

    except Exception as e:
        print(f"News check failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
