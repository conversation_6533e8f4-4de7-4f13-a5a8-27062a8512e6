# SpigaMonde Entity Extraction Guide

**Complete guide to extracting specific information from web pages**

## 🔍 **Current Built-in Capabilities**

### ✅ **Automatic Entity Extraction (Already Working!)**

SpigaMonde **automatically extracts** the following entities from all crawled content:

#### **📧 Email Addresses**
- **Pattern**: `<EMAIL>` format
- **Regex**: `\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b`
- **Storage**: JSON array in `content_analysis.emails`

#### **📞 Phone Numbers**
- **Patterns**: US/International formats with optional country codes
- **Regex**: `(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}`
- **Storage**: JSON array in `content_analysis.phone_numbers`

#### **🔗 URLs**
- **Pattern**: HTTP/HTTPS links
- **Regex**: `http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+`
- **Storage**: JSON array in `content_analysis.urls`

#### **📅 Dates**
- **Patterns**: MM/DD/YYYY, YYYY-MM-DD formats
- **Regex**: `\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b`
- **Storage**: JSON array in `content_analysis.dates`

#### **📝 HTML Metadata**
- **Title**: `<title>` tag content
- **Description**: Meta description tags
- **Author**: Meta author tags
- **Headings**: H1-H6 tags as topics

### 📊 **Database Storage**

All extracted entities are automatically stored in the database:

```sql
-- Query emails from all analyzed content
SELECT url, emails FROM content 
JOIN content_analysis ON content.id = content_analysis.content_id 
WHERE emails IS NOT NULL;

-- Query phone numbers
SELECT url, phone_numbers FROM content 
JOIN content_analysis ON content.id = content_analysis.content_id 
WHERE phone_numbers IS NOT NULL;
```

## 🚀 **Enhanced Extraction Scripts**

### **1. Contact Information Extractor**
**File**: `scripts/examples/extract_contact_info.py`

**Enhanced Features**:
- **Multiple email patterns**: Standard, display name, obfuscated formats
- **Advanced phone patterns**: Extensions, international formats
- **Contact page detection**: Automatically identifies contact pages
- **HTML section targeting**: Prioritizes headers, footers, contact sections
- **Validation**: Email format validation and phone number cleaning

**Usage**:
```bash
python scripts/examples/extract_contact_info.py
```

**Output**:
- Comprehensive contact database
- Section-specific contact information
- Contact page identification
- Export-ready CSV format

### **2. Page Section Extractor**
**File**: `scripts/examples/extract_page_sections.py`

**Advanced Features**:
- **CSS selector targeting**: Header, footer, navigation, content sections
- **Social media detection**: Twitter, Facebook, LinkedIn, GitHub links
- **Script analysis**: External and inline JavaScript/CSS
- **SEO metadata**: Open Graph, Twitter Card data
- **Link categorization**: Internal vs. external links by section

**Usage**:
```bash
python scripts/examples/extract_page_sections.py
```

**Output**:
- Complete page structure analysis
- Section-specific content extraction
- Social media link inventory
- Technical asset analysis

## 🎯 **Targeting Specific Page Areas**

### **HTML Section Targeting**

The enhanced scripts can target specific page sections:

#### **CSS Selectors Supported**:
```python
section_selectors = {
    'header': ['header', '.header', '#header', '.site-header'],
    'navigation': ['nav', '.nav', '.navigation', '.menu'],
    'main_content': ['main', '.main', '#main', '.content'],
    'sidebar': ['.sidebar', '#sidebar', '.widget-area'],
    'footer': ['footer', '.footer', '#footer', '.site-footer'],
    'contact': ['.contact', '#contact', '.contact-info'],
}
```

#### **Priority Sections for Contact Info**:
1. **Footer**: Most common location for contact details
2. **Header**: Company contact information
3. **Contact pages**: Dedicated contact sections
4. **About pages**: Team and company information
5. **Sidebar**: Secondary contact information

### **Script and Style Analysis**

Extract information from technical elements:

```python
# External scripts and stylesheets
external_scripts = soup.find_all('script', src=True)
external_styles = soup.find_all('link', rel='stylesheet')

# Inline code analysis
inline_scripts = soup.find_all('script', src=False)
inline_styles = soup.find_all('style')
```

## 📝 **Custom Entity Extraction**

### **Adding New Entity Types**

You can extend the extraction patterns:

```python
# Custom patterns for specific entities
custom_patterns = {
    'social_security': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
    'credit_card': re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
    'ip_address': re.compile(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'),
    'bitcoin_address': re.compile(r'\b[13][a-km-zA-HJ-NP-Z1-9]{25,34}\b'),
    'github_repo': re.compile(r'github\.com/[A-Za-z0-9_-]+/[A-Za-z0-9_-]+'),
}
```

### **Domain-Specific Extraction**

Create specialized extractors for specific domains:

```python
# Academic paper extraction
academic_patterns = {
    'doi': re.compile(r'10\.\d{4,}/[^\s]+'),
    'arxiv_id': re.compile(r'arXiv:\d{4}\.\d{4,5}'),
    'pmid': re.compile(r'PMID:\s*\d+'),
    'isbn': re.compile(r'ISBN[-\s]?(?:\d{1,5}[-\s]?)?\d{1,7}[-\s]?\d{1,6}[-\s]?\d'),
}

# E-commerce extraction
ecommerce_patterns = {
    'price': re.compile(r'\$\d+(?:\.\d{2})?'),
    'sku': re.compile(r'SKU[:\s]*[A-Z0-9-]+', re.IGNORECASE),
    'product_id': re.compile(r'Product\s*ID[:\s]*[A-Z0-9-]+', re.IGNORECASE),
}
```

## 🛠️ **Creating Custom Extraction Scripts**

### **Basic Template**

```python
#!/usr/bin/env python3
"""Custom entity extraction script."""

import re
from bs4 import BeautifulSoup

class CustomExtractor:
    def __init__(self):
        self.patterns = {
            'custom_entity': re.compile(r'your_pattern_here'),
            # Add more patterns
        }
    
    def extract_from_html(self, html_content, url):
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Target specific sections
        target_sections = soup.select('.your-target-class')
        
        results = {}
        for section in target_sections:
            text = section.get_text()
            for entity_type, pattern in self.patterns.items():
                matches = pattern.findall(text)
                if matches:
                    results[entity_type] = matches
        
        return results
```

### **Integration with SpigaMonde**

```python
# In your crawl script
from spigamonde.analysis.analyzer import ContentAnalyzer

# Extend the analyzer
class CustomAnalyzer(ContentAnalyzer):
    def analyze_content(self, url, content, file_path, mime_type):
        # Call parent analysis
        result = super().analyze_content(url, content, file_path, mime_type)
        
        # Add custom extraction
        custom_extractor = CustomExtractor()
        custom_entities = custom_extractor.extract_from_html(content, url)
        
        # Add to metadata
        result.metadata.custom_fields.update(custom_entities)
        
        return result
```

## 📊 **Querying Extracted Entities**

### **SQL Queries**

```sql
-- Find all emails from specific domains
SELECT url, emails FROM content 
JOIN content_analysis ON content.id = content_analysis.content_id 
WHERE emails LIKE '%@company.com%';

-- Find pages with phone numbers
SELECT url, phone_numbers FROM content 
JOIN content_analysis ON content.id = content_analysis.content_id 
WHERE phone_numbers IS NOT NULL;

-- Find contact pages with multiple contact methods
SELECT url, emails, phone_numbers FROM content 
JOIN content_analysis ON content.id = content_analysis.content_id 
WHERE emails IS NOT NULL AND phone_numbers IS NOT NULL;
```

### **Python Queries**

```python
# Using SpigaMonde session
with session_scope() as session:
    # Find content with emails
    content_with_emails = session.query(Content).join(ContentAnalysis).filter(
        ContentAnalysis.emails.isnot(None)
    ).all()
    
    # Extract and process emails
    all_emails = set()
    for content in content_with_emails:
        analysis = content.content_analysis[0]
        emails = json.loads(analysis.emails)
        all_emails.update(emails)
```

## 🎯 **Best Practices**

### **Regex Patterns**
- **Test thoroughly**: Use regex testing tools
- **Handle edge cases**: Account for formatting variations
- **Validate results**: Check extracted data quality
- **Performance**: Optimize patterns for speed

### **HTML Parsing**
- **Use BeautifulSoup**: More reliable than regex for HTML
- **Target specific sections**: Focus on relevant page areas
- **Handle malformed HTML**: Graceful error handling
- **Respect structure**: Use semantic HTML elements

### **Data Quality**
- **Validation**: Verify extracted entities
- **Deduplication**: Remove duplicate entries
- **Normalization**: Standardize formats
- **Context**: Consider extraction context

### **Privacy and Ethics**
- **Respect robots.txt**: Follow crawling guidelines
- **Rate limiting**: Don't overwhelm servers
- **Data sensitivity**: Handle personal information carefully
- **Legal compliance**: Follow applicable laws and regulations

---

**SpigaMonde provides powerful entity extraction capabilities out of the box, with extensive customization options for specialized use cases!** 🚀
