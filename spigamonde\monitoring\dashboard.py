"""Real-time monitoring dashboard for SpigaMonde."""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.progress import Progress, BarColumn, TextColumn, TimeRemainingColumn
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from sqlalchemy import text

from .metrics import get_metrics_collector, MetricsCollector
from .logger import get_logger
from ..database.connection import session_scope
from ..models.content import Content, ContentStatus, URL, CrawlSession


class MonitoringDashboard:
    """Real-time monitoring dashboard for crawl operations."""
    
    def __init__(self):
        self.console = Console()
        self.metrics = get_metrics_collector()
        self.logger = get_logger()
        self._last_update = datetime.utcnow()
        self._update_interval = 2.0  # seconds
    
    def create_layout(self) -> Layout:
        """Create the dashboard layout."""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        layout["left"].split_column(
            Layout(name="performance", ratio=2),
            Layout(name="crawl_stats", ratio=1)
        )
        
        layout["right"].split_column(
            Layout(name="system_health", ratio=1),
            Layout(name="recent_activity", ratio=2)
        )
        
        return layout
    
    def get_header_panel(self) -> Panel:
        """Create header panel with basic info."""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        header_text = Text()
        header_text.append("SpigaMonde Monitoring Dashboard", style="bold blue")
        header_text.append(f" | {current_time}", style="dim")
        
        return Panel(header_text, style="blue")
    
    def get_footer_panel(self) -> Panel:
        """Create footer panel with controls."""
        footer_text = Text()
        footer_text.append("Press ", style="dim")
        footer_text.append("Ctrl+C", style="bold red")
        footer_text.append(" to exit | Updates every ", style="dim")
        footer_text.append(f"{self._update_interval}s", style="bold")
        
        return Panel(footer_text, style="dim")
    
    def get_performance_panel(self) -> Panel:
        """Create performance metrics panel."""
        table = Table(title="Performance Metrics", show_header=True, header_style="bold magenta")
        table.add_column("Operation", style="cyan")
        table.add_column("Count", justify="right", style="green")
        table.add_column("Avg Duration", justify="right", style="yellow")
        table.add_column("Success Rate", justify="right", style="blue")
        table.add_column("Last Run", style="dim")
        
        performance_stats = self.metrics.get_performance_stats()
        
        for operation, stats in performance_stats.items():
            if stats and stats.count > 0:
                success_rate = f"{(stats.success_count / stats.count * 100):.1f}%"
                avg_duration = f"{stats.avg_duration:.3f}s"
                last_run = stats.last_execution.strftime("%H:%M:%S") if stats.last_execution else "Never"
                
                table.add_row(
                    operation,
                    str(stats.count),
                    avg_duration,
                    success_rate,
                    last_run
                )
        
        if not performance_stats:
            table.add_row("No data available", "", "", "", "")
        
        return Panel(table, title="Performance", border_style="green")
    
    def get_crawl_stats_panel(self) -> Panel:
        """Create crawl statistics panel."""
        try:
            with session_scope() as session:
                # Get basic counts
                total_urls = session.query(URL).count()
                crawled_urls = session.query(URL).filter(URL.is_crawled == True).count()
                total_content = session.query(Content).count()
                downloaded_content = session.query(Content).filter(Content.is_downloaded == True).count()
                
                # Get active sessions
                active_sessions = session.query(CrawlSession).filter(CrawlSession.is_active == True).count()
                
                table = Table(show_header=False, box=None)
                table.add_column("Metric", style="cyan")
                table.add_column("Value", justify="right", style="green")
                
                table.add_row("URLs Discovered", f"{total_urls:,}")
                table.add_row("URLs Crawled", f"{crawled_urls:,}")
                table.add_row("Content Found", f"{total_content:,}")
                table.add_row("Content Downloaded", f"{downloaded_content:,}")
                table.add_row("Active Sessions", f"{active_sessions:,}")
                
                # Calculate rates
                if total_urls > 0:
                    crawl_rate = (crawled_urls / total_urls) * 100
                    table.add_row("Crawl Progress", f"{crawl_rate:.1f}%")
                
                if total_content > 0:
                    download_rate = (downloaded_content / total_content) * 100
                    table.add_row("Download Progress", f"{download_rate:.1f}%")
        
        except Exception as e:
            table = Table(show_header=False, box=None)
            table.add_column("Error", style="red")
            table.add_row(f"Database error: {str(e)}")
        
        return Panel(table, title="Crawl Statistics", border_style="blue")
    
    def get_system_health_panel(self) -> Panel:
        """Create system health panel."""
        table = Table(show_header=False, box=None)
        table.add_column("Metric", style="cyan")
        table.add_column("Status", justify="right")
        
        # Get metrics summary
        summary = self.metrics.get_summary()
        
        # Database connectivity
        try:
            with session_scope() as session:
                session.execute(text("SELECT 1"))
            db_status = Text("✓ Connected", style="green")
        except Exception:
            db_status = Text("✗ Error", style="red")
        
        table.add_row("Database", db_status)
        
        # Performance stats
        perf_count = len(summary.get("performance_stats", {}))
        table.add_row("Tracked Operations", f"{perf_count}")
        
        # Metrics count
        metric_count = sum(summary.get("metric_counts", {}).values())
        table.add_row("Metric Points", f"{metric_count:,}")
        
        # Memory usage (approximate)
        import sys
        memory_mb = sys.getsizeof(summary) / 1024 / 1024
        table.add_row("Memory Usage", f"{memory_mb:.1f} MB")
        
        return Panel(table, title="System Health", border_style="yellow")
    
    def get_recent_activity_panel(self) -> Panel:
        """Create recent activity panel."""
        table = Table(title="Recent Activity", show_header=True, header_style="bold magenta")
        table.add_column("Time", style="dim")
        table.add_column("Activity", style="cyan")
        table.add_column("Details", style="white")
        
        # Get recent metrics (last 5 minutes)
        since = datetime.utcnow() - timedelta(minutes=5)
        recent_metrics = self.metrics.get_metrics(since=since)
        
        # Collect recent activities
        activities = []
        for metric_name, points in recent_metrics.items():
            for point in points[-10:]:  # Last 10 points per metric
                activities.append({
                    "timestamp": point.timestamp,
                    "activity": metric_name,
                    "value": point.value,
                    "labels": point.labels
                })
        
        # Sort by timestamp (most recent first)
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # Add to table
        for activity in activities[:15]:  # Show last 15 activities
            time_str = activity["timestamp"].strftime("%H:%M:%S")
            activity_name = activity["activity"].replace("_", " ").title()
            
            details = f"Value: {activity['value']}"
            if activity["labels"]:
                label_str = ", ".join(f"{k}={v}" for k, v in activity["labels"].items())
                details += f" ({label_str})"
            
            table.add_row(time_str, activity_name, details)
        
        if not activities:
            table.add_row("--:--:--", "No recent activity", "")
        
        return Panel(table, title="Recent Activity", border_style="magenta")
    
    def update_dashboard(self, layout: Layout):
        """Update all dashboard panels."""
        layout["header"].update(self.get_header_panel())
        layout["footer"].update(self.get_footer_panel())
        layout["performance"].update(self.get_performance_panel())
        layout["crawl_stats"].update(self.get_crawl_stats_panel())
        layout["system_health"].update(self.get_system_health_panel())
        layout["recent_activity"].update(self.get_recent_activity_panel())
    
    def run_dashboard(self, duration: Optional[float] = None):
        """Run the live dashboard."""
        layout = self.create_layout()
        
        start_time = time.time()
        
        with Live(layout, refresh_per_second=1, screen=True) as live:
            while True:
                try:
                    self.update_dashboard(layout)
                    time.sleep(self._update_interval)
                    
                    # Check duration limit
                    if duration and (time.time() - start_time) >= duration:
                        break
                        
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    self.logger.error(f"Dashboard error: {e}")
                    time.sleep(1)
    
    def print_summary(self):
        """Print a one-time summary of current status."""
        self.console.print("\n[bold blue]SpigaMonde Status Summary[/bold blue]\n")
        
        # Performance summary
        self.console.print(self.get_performance_panel())
        self.console.print()
        
        # Crawl stats
        self.console.print(self.get_crawl_stats_panel())
        self.console.print()
        
        # System health
        self.console.print(self.get_system_health_panel())


def create_dashboard() -> MonitoringDashboard:
    """Create a new monitoring dashboard instance."""
    return MonitoringDashboard()


def run_live_dashboard(duration: Optional[float] = None):
    """Run the live monitoring dashboard."""
    dashboard = create_dashboard()
    dashboard.run_dashboard(duration)


def print_status_summary():
    """Print a status summary."""
    dashboard = create_dashboard()
    dashboard.print_summary()
