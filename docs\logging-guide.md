# SpigaMonde Logging Guide

**Complete guide to SpigaMonde logging system, current status, and known issues**

## 🎯 **Current Logging Status**

### ✅ **What's Working**
- **Console logging** - All logs appear in terminal output with rich formatting
- **Structured logging** - JSON-formatted logs with timestamps and context
- **Log levels** - INFO, DEBUG, WARNING, ERROR, CRITICAL levels working
- **Real-time output** - Logs appear immediately during operations
- **Rich formatting** - Color-coded output with proper structure

### ⚠️ **Known Issues (To Fix Later)**
- **File logging not working** - Environment variables not being applied properly
- **Settings caching** - Configuration changes require restart
- **Log file creation** - No automatic file output despite configuration
- **Environment variable precedence** - Settings not picking up runtime env vars

## 📍 **Where Logs Are Currently Located**

### **1. Console Output (Primary)**
All SpigaMonde logs currently appear in console/terminal output:

```bash
# Example console output:
spiga stats
# 2025-08-27 05:53:01 | INFO | N/A | spigamonde.database.connection:initialize:63 - Database initialized

spiga crawl <URL>
# 2025-08-27 14:40:29 | INFO | N/A | spigamonde.spiders.content_spider:parse:45 - Processing URL: https://example.com
# 2025-08-27 14:40:30 | INFO | N/A | spigamonde.analysis.analyzer:analyze_content:120 - Content analyzed: 1500 words
```

### **2. Manual Log Capture (Workaround)**
Since automatic file logging isn't working, use terminal redirection:

```bash
# Capture all output to file
spiga crawl <URL> 2>&1 | tee crawl_$(date +%Y%m%d_%H%M%S).log

# Capture only errors
spiga crawl <URL> 2> errors_$(date +%Y%m%d_%H%M%S).log

# Background logging
spiga crawl <URL> > crawl_output.log 2>&1 &

# Real-time monitoring with file capture
spiga crawl <URL> 2>&1 | tee -a logs/spigamonde_manual.log
```

## 🔧 **Logging Configuration**

### **Current Configuration**
Based on testing, here's the current logging setup:

```python
# From spigamonde/config/settings.py
class LoggingSettings(BaseSettings):
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}"
    file_path: Optional[str] = None  # ⚠️ Not working properly
    max_file_size: str = "10 MB"
    retention: str = "30 days"
    structured_logging: bool = True
    performance_logging: bool = True
```

### **Environment Variables (Intended)**
These should work but currently don't:

```bash
# File logging (NOT WORKING)
export LOG_FILE_PATH=./logs/spigamonde.log

# Log level
export LOG_LEVEL=DEBUG

# Log format
export LOG_FORMAT="{time} | {level} | {message}"
```

### **Configuration Files**
The `.env.example` file shows intended configuration:

```env
# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT={time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}
LOG_FILE_PATH=./logs/spigamonde.log
LOG_MAX_FILE_SIZE=10 MB
LOG_RETENTION=30 days
LOG_STRUCTURED=true
LOG_PERFORMANCE=true
```

## 📊 **Log Content Examples**

### **Database Operations**
```
2025-08-27 05:53:01 | INFO | N/A | spigamonde.database.connection:initialize:63 - Database initialized with URL: sqlite:///spigamonde.db
2025-08-27 05:53:01 | INFO | N/A | spigamonde.database.connection:create_tables:89 - Database tables created successfully
```

### **Crawling Operations**
```
2025-08-27 14:40:29 | INFO | N/A | spigamonde.spiders.content_spider:parse:45 - Processing URL: https://docs.python.org/3/tutorial/
2025-08-27 14:40:30 | INFO | N/A | spigamonde.spiders.content_spider:download:78 - Downloaded: tutorial.html (15.2 KB)
2025-08-27 14:40:30 | INFO | N/A | spigamonde.analysis.analyzer:analyze_content:120 - Content analyzed: 1500 words, category: TECHNICAL_DOCUMENTATION
```

### **Error Messages**
```
2025-08-27 14:40:31 | ERROR | N/A | spigamonde.spiders.content_spider:parse:67 - Failed to download: Connection timeout
2025-08-27 14:40:31 | WARNING | N/A | spigamonde.spiders.content_spider:retry:89 - Retrying URL after 2.0 seconds
```

### **Performance Logs**
```
2025-08-27 14:40:32 | INFO | N/A | spigamonde.monitoring.metrics:record_timing:45 - Content analysis completed in 2.3ms
2025-08-27 14:40:32 | INFO | N/A | spigamonde.monitoring.metrics:update_stats:67 - Crawl rate: 12.5 pages/minute
```

## 🔍 **How to Monitor Logs**

### **Real-time Monitoring**
```bash
# Basic crawl with visible logs
spiga crawl <URL>

# Verbose output (more detailed logs)
spiga crawl <URL> --debug

# Real-time dashboard with logs
spiga monitor

# Monitor specific operations
spiga list-content  # Shows database operation logs
spiga stats         # Shows system status logs
```

### **Log Analysis**
```bash
# Search for specific terms in console output
spiga crawl <URL> 2>&1 | grep "ERROR"
spiga crawl <URL> 2>&1 | grep "analysis"
spiga crawl <URL> 2>&1 | grep "download"

# Count log entries
spiga crawl <URL> 2>&1 | wc -l

# Filter by log level
spiga crawl <URL> 2>&1 | grep "INFO"
spiga crawl <URL> 2>&1 | grep "WARNING\|ERROR"
```

### **Performance Monitoring**
```bash
# Monitor crawl performance
spiga crawl <URL> 2>&1 | grep "timing\|rate\|performance"

# Monitor memory usage (if available)
spiga crawl <URL> 2>&1 | grep "memory\|usage"
```

## 📁 **Manual Log File Management**

### **Create Log Directory**
```bash
mkdir -p logs
```

### **Manual Log Capture Scripts**
Created `scripts/examples/manual_logging_crawl.py` for manual log capture:

```python
# Captures SpigaMonde output to timestamped log files
python scripts/examples/manual_logging_crawl.py
```

### **Log File Organization**
```
logs/
├── crawl_20250827_143045.log      # Main crawl logs
├── crawl_errors_20250827_143045.log  # Error-only logs
├── spigamonde_manual.log          # Continuous manual log
└── analysis_20250827_143045.log   # Analysis-specific logs
```

## 🐛 **Known Issues and Troubleshooting**

### **Issue #1: File Logging Not Working**
**Problem**: Environment variables for file logging are not being applied
**Status**: Unresolved
**Workaround**: Use terminal redirection (`2>&1 | tee logfile.log`)
**Investigation needed**: 
- Pydantic settings configuration
- Environment variable precedence
- Settings caching mechanism

### **Issue #2: Settings Not Updating**
**Problem**: Configuration changes require application restart
**Status**: Unresolved
**Workaround**: Restart SpigaMonde after configuration changes
**Investigation needed**: Settings reload mechanism

### **Issue #3: Environment Variable Precedence**
**Problem**: Runtime environment variables not overriding defaults
**Status**: Unresolved
**Evidence**: 
```python
# Environment variable set but not applied:
os.environ['LOG_FILE_PATH'] = './logs/spigamonde.log'
settings.logging.file_path  # Returns: None
```

### **Issue #4: Segmentation Faults in Complex Scripts**
**Problem**: Some logging-heavy scripts cause segmentation faults
**Status**: Unresolved
**Workaround**: Use simpler scripts or avoid certain library combinations
**Investigation needed**: Library conflicts, memory management

## 🔧 **Temporary Solutions**

### **1. Manual Log Capture**
```bash
# Create a logging wrapper script
#!/bin/bash
LOG_FILE="logs/spiga_$(date +%Y%m%d_%H%M%S).log"
mkdir -p logs
spiga "$@" 2>&1 | tee "$LOG_FILE"
```

### **2. Log Analysis Scripts**
```bash
# Extract errors from console output
spiga crawl <URL> 2>&1 | grep -E "(ERROR|CRITICAL)" > errors.log

# Extract performance metrics
spiga crawl <URL> 2>&1 | grep -E "(timing|rate|performance)" > performance.log
```

### **3. Structured Log Parsing**
```python
# Parse console output for structured analysis
import re
log_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s+\| ([^|]+) \| (.+)'
# Use to parse captured log files
```

## 🎯 **Future Improvements Needed**

### **High Priority**
1. **Fix file logging** - Resolve environment variable application
2. **Settings reload** - Allow runtime configuration changes
3. **Log rotation** - Implement automatic log file rotation
4. **Performance logging** - Separate performance metrics to dedicated files

### **Medium Priority**
1. **Log aggregation** - Centralized logging for distributed operations
2. **Log filtering** - Runtime log level filtering
3. **Structured exports** - JSON/CSV log exports
4. **Dashboard integration** - Real-time log viewing in web interface

### **Low Priority**
1. **Remote logging** - Send logs to external systems
2. **Log compression** - Automatic compression of old logs
3. **Log analytics** - Built-in log analysis tools

## 📚 **Related Documentation**

- **Configuration Guide**: See `.env.example` for all logging options
- **Monitoring Guide**: `docs/monitoring-guide.md` (if exists)
- **Troubleshooting**: `docs/troubleshooting.md` (if exists)
- **Development Log**: `docs/dev_log.md` - Contains development history

## 🚀 **Quick Start**

### **For Users (Current Working Method)**
```bash
# 1. Create logs directory
mkdir -p logs

# 2. Run with log capture
spiga crawl <URL> 2>&1 | tee logs/crawl_$(date +%Y%m%d_%H%M%S).log

# 3. Monitor in real-time
tail -f logs/crawl_*.log
```

### **For Developers (Debugging)**
```bash
# 1. Enable debug logging
spiga crawl <URL> --debug 2>&1 | tee debug.log

# 2. Test logging configuration
python -c "from spigamonde.config.settings import get_settings; print(get_settings().logging)"

# 3. Manual logging test
python scripts/examples/manual_logging_crawl.py
```

---

**Note**: This guide will be updated as logging issues are resolved and new features are implemented. The manual workarounds should be sufficient for current operations while we address the underlying configuration issues.
