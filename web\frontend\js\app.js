/**
 * SpigaMonde Web Interface - Main Application
 * Tabbed interface with modular architecture
 */

// Configuration
const API_BASE_URL = 'http://127.0.0.1:8000';

// Global state
let currentTab = 'dashboard';
let autoRefreshTimer = null;

// Core DOM elements
let testModeIndicator, backendStatus;

/**
 * Initialize the application
 */
async function init() {
    console.log('SpigaMonde Web Interface - Tabbed Version');

    // Get core DOM elements
    testModeIndicator = document.getElementById('testModeIndicator');
    backendStatus = document.getElementById('backendStatus');

    console.log('Core DOM elements found:', {
        testModeIndicator: !!testModeIndicator,
        backendStatus: !!backendStatus
    });

    // Initialize tab system
    initTabSystem();

    // Initialize modules (check if functions exist first)
    console.log('Checking module functions:', {
        initDashboard: typeof initDashboard,
        initAnalytics: typeof initAnalytics,
        initSystem: typeof initSystem,
        initLogs: typeof initLogs
    });

    if (typeof initDashboard === 'function') await initDashboard();
    if (typeof initAnalytics === 'function') await initAnalytics();
    if (typeof initSystem === 'function') await initSystem();
    if (typeof initLogs === 'function') await initLogs();

    // Initial setup
    logMessage('Tabbed interface initialized', 'info');
    checkBackendStatus();
    updateModeIndicator();

    console.log('SpigaMonde Web Interface initialized with tabs');
}

/**
 * Initialize tab system
 */
function initTabSystem() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            switchTab(tabId);
        });
    });
}

/**
 * Switch to a specific tab
 */
function switchTab(tabId) {
    // Update button states
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    
    // Update content visibility
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabId}-tab`).classList.add('active');
    
    // Update current tab
    currentTab = tabId;
    
    // Tab-specific initialization
    onTabSwitch(tabId);
    
    logMessage(`Switched to ${tabId} tab`, 'info');
}

/**
 * Handle tab switch events
 */
function onTabSwitch(tabId) {
    switch(tabId) {
        case 'analytics':
            // Refresh stats when switching to analytics
            if (window.refreshDetailedStats) {
                window.refreshDetailedStats();
            }
            break;
        case 'logs':
            // Scroll to bottom of logs
            const activityLog = document.getElementById('activityLog');
            if (activityLog) {
                activityLog.scrollTop = activityLog.scrollHeight;
            }
            break;
    }
}

/**
 * Update mode indicator
 */
async function updateModeIndicator() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/mode`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (response.ok) {
            const data = await response.json();
            const mode = data.mode;
            
            // Update indicator
            const modeIcon = testModeIndicator.querySelector('.mode-icon');
            const modeText = testModeIndicator.querySelector('.mode-text');
            
            modeIcon.textContent = mode.icon;
            modeText.textContent = mode.description;
            
            // Update styling
            testModeIndicator.className = `mode-indicator ${mode.type}`;
            
            logMessage(`Mode detected: ${mode.type} (${mode.user_agent})`, 'info');
            
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
        
    } catch (error) {
        const modeIcon = testModeIndicator.querySelector('.mode-icon');
        const modeText = testModeIndicator.querySelector('.mode-text');
        
        modeIcon.textContent = '❌';
        modeText.textContent = 'Mode Check Failed';
        testModeIndicator.className = 'mode-indicator error';
        
        logMessage(`Mode check failed: ${error.message}`, 'error');
    }
}

/**
 * Check backend status
 */
async function checkBackendStatus() {
    try {
        const response = await fetch(`${API_BASE_URL}/api/health`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        if (response.ok) {
            const data = await response.json();
            backendStatus.textContent = '✅ Connected';
            backendStatus.className = 'status-value connected';
            logMessage('Backend connection established', 'success');
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
        
    } catch (error) {
        backendStatus.textContent = '❌ Disconnected';
        backendStatus.className = 'status-value disconnected';
        logMessage(`Backend connection failed: ${error.message}`, 'error');
    }
}

/**
 * Utility functions
 */

// Set button loading state
function setButtonLoading(button, isLoading) {
    const text = button.querySelector('.button-text');
    const spinner = button.querySelector('.button-spinner');
    
    if (isLoading) {
        text.style.display = 'none';
        spinner.style.display = 'inline';
        button.disabled = true;
    } else {
        text.style.display = 'inline';
        spinner.style.display = 'none';
        button.disabled = false;
    }
}

// Hide result element
function hideResult(resultElement) {
    resultElement.style.display = 'none';
}

// Log message to activity log
function logMessage(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);

    const activityLog = document.getElementById('activityLog');
    if (!activityLog) {
        console.log('Activity log element not found, message:', message);
        return;
    }

    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;

    logEntry.innerHTML = `
        <span class="log-time">${timestamp}</span>
        <span class="log-message">${message}</span>
    `;

    activityLog.appendChild(logEntry);
    activityLog.scrollTop = activityLog.scrollHeight;
}

// Clear activity log
function clearLog() {
    const activityLog = document.getElementById('activityLog');
    if (activityLog) {
        activityLog.innerHTML = '';
        logMessage('Activity log cleared', 'info');
    }
}

// Export global functions
window.switchTab = switchTab;
window.logMessage = logMessage;
window.clearLog = clearLog;
window.setButtonLoading = setButtonLoading;
window.hideResult = hideResult;
window.updateModeIndicator = updateModeIndicator;
window.checkBackendStatus = checkBackendStatus;
window.API_BASE_URL = API_BASE_URL;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', init);
