# SpigaMonde Production Configuration
# Use this configuration for production releases

# Production mode
DEBUG=false

# Database Configuration
DATABASE_URL=sqlite:///spigamonde.db
DATABASE_ECHO=false

# Spider Configuration - PRODUCTION SETTINGS
SPIDER_CONCURRENT_REQUESTS=16
SPIDER_DOWNLOAD_DELAY=1.0
SPIDER_RANDOMIZE_DELAY=true

# OFFICIAL SPIGAMONDE USER-AGENT
SPIDER_USER_AGENT=SpigaMonde/0.1.0 (+https://github.com/spigamonde/spigamonde)

SPIDER_MAX_DEPTH=3
SPIDER_MAX_PAGES=
SPIDER_ROBOTSTXT_OBEY=true

# Full file type support for production
SPIDER_ALLOWED_FILE_TYPES=pdf,doc,docx,txt,rtf,odt,xls,xlsx,csv,ods,ppt,pptx,odp,jpg,jpeg,png,gif,bmp,svg,webp,mp3,wav,flac,ogg,m4a,mp4,avi,mkv,mov,wmv,flv,webm,zip,rar,7z,tar,gz,bz2,html,htm,xml,json,yaml,yml

# File size limits (production)
SPIDER_MAX_FILE_SIZE=104857600  # 100MB
SPIDER_MIN_FILE_SIZE=1024       # 1KB

# Storage Configuration
STORAGE_BASE_PATH=./downloads
STORAGE_ORGANIZE_BY_DATE=true
STORAGE_ORGANIZE_BY_TYPE=true
STORAGE_ORGANIZE_BY_DOMAIN=false

# Logging Configuration
# Note: Logging config has known issues, using defaults for now

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_DASHBOARD_ENABLED=true
MONITORING_ALERTS_ENABLED=true
MONITORING_METRICS_RETENTION_HOURS=24
MONITORING_ALERT_CHECK_INTERVAL=30
MONITORING_PERFORMANCE_THRESHOLD=30.0
MONITORING_ERROR_RATE_THRESHOLD=0.2
