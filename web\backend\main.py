#!/usr/bin/env python3
"""
SpigaMonde Web Interface Backend
Minimal FastAPI implementation with SpigaMonde integration and logging.

Usage:
    uvicorn main:app --reload --port 8000
"""

import sys
import os
import subprocess
import json
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# SpigaMonde imports
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.logger import get_logger

# Initialize SpigaMonde logger
logger = get_logger()

# Create FastAPI app
app = FastAPI(
    title="SpigaMonde Web Interface",
    description="Web interface for SpigaMonde web crawler",
    version="1.0.0"
)

# Add CORS middleware for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize SpigaMonde components on startup."""
    try:
        logger.info("Starting SpigaMonde Web Interface backend")

        # Ensure we're using the correct database path (from project root)
        project_root = Path(__file__).parent.parent.parent
        original_cwd = os.getcwd()
        os.chdir(str(project_root))
        logger.info(f"Changed working directory from {original_cwd} to {project_root}")

        # Initialize database
        init_database()
        logger.info("Database initialized successfully")

        # Log startup success
        logger.info("SpigaMonde Web Interface backend started successfully")

    except Exception as e:
        logger.error(f"Failed to start backend: {e}")
        raise


@app.get("/")
async def root():
    """Root endpoint - basic health check."""
    logger.info("Root endpoint accessed")
    return {
        "message": "SpigaMonde Web Interface API",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/test")
async def test_connection():
    """Test endpoint to verify connectivity and logging."""
    logger.info("Test connection endpoint called")
    
    try:
        # Test database connection
        with session_scope() as session:
            content_count = session.query(Content).count()
            analysis_count = session.query(ContentAnalysis).count()
        
        logger.info(f"Database test successful - Content: {content_count}, Analysis: {analysis_count}")
        
        response_data = {
            "status": "success",
            "message": "Connection test successful",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": True,
                "content_count": content_count,
                "analysis_count": analysis_count
            },
            "logging": {
                "enabled": True,
                "message": "This request was logged successfully"
            }
        }
        
        logger.info("Test connection completed successfully")
        return response_data
        
    except Exception as e:
        logger.error(f"Test connection failed: {e}")
        
        error_response = {
            "status": "error",
            "message": f"Connection test failed: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "database": {
                "connected": False,
                "error": str(e)
            },
            "logging": {
                "enabled": True,
                "message": "This error was logged successfully"
            }
        }
        
        raise HTTPException(status_code=500, detail=error_response)


@app.get("/api/spiga/stats")
async def get_spigamonde_stats():
    """Get SpigaMonde statistics - real data from database."""
    logger.info("SpigaMonde stats endpoint called")
    
    try:
        with session_scope() as session:
            # Get basic statistics
            total_content = session.query(Content).count()
            analyzed_content = session.query(ContentAnalysis).count()
            
            # Get recent content (last 24 hours)
            from datetime import timedelta
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_content = session.query(Content).filter(
                Content.created_at >= recent_cutoff
            ).count()
            
            # Get content by status
            completed_content = session.query(Content).filter(
                Content.status == "completed"
            ).count()
            
            # Get latest crawl session info
            latest_content = session.query(Content).order_by(
                Content.created_at.desc()
            ).first()
            
            stats_data = {
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "statistics": {
                    "total_content": total_content,
                    "analyzed_content": analyzed_content,
                    "recent_content_24h": recent_content,
                    "completed_content": completed_content,
                    "analysis_coverage": f"{(analyzed_content/total_content*100):.1f}%" if total_content > 0 else "0%"
                },
                "latest_activity": {
                    "last_crawl": latest_content.created_at.isoformat() if latest_content else None,
                    "last_url": latest_content.url if latest_content else None
                },
                "system": {
                    "database_connected": True,
                    "logging_active": True
                }
            }
            
            logger.info(f"Stats retrieved successfully - Total content: {total_content}, Analyzed: {analyzed_content}")
            return stats_data
            
    except Exception as e:
        logger.error(f"Failed to get SpigaMonde stats: {e}")
        
        error_response = {
            "status": "error",
            "message": f"Failed to retrieve statistics: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "system": {
                "database_connected": False,
                "error": str(e)
            }
        }
        
        raise HTTPException(status_code=500, detail=error_response)


@app.post("/api/run-script")
async def run_test_script():
    """Execute the web test script and return results."""
    logger.info("Test script execution requested from web interface")

    try:
        # Path to the test script
        script_path = Path(__file__).parent.parent.parent / "scripts" / "web_test_script.py"

        if not script_path.exists():
            logger.error(f"Test script not found: {script_path}")
            raise HTTPException(
                status_code=404,
                detail={
                    "status": "error",
                    "message": "Test script not found",
                    "script_path": str(script_path),
                    "timestamp": datetime.now().isoformat()
                }
            )

        logger.info(f"Executing test script: {script_path}")

        # Execute the script
        result = subprocess.run(
            [sys.executable, str(script_path)],
            capture_output=True,
            text=True,
            timeout=30,  # 30 second timeout
            cwd=str(script_path.parent.parent)  # Run from SpigaMonde root
        )

        # Parse the JSON output
        if result.returncode == 0:
            try:
                script_output = json.loads(result.stdout)
                logger.info("Test script executed successfully")

                # Add execution metadata
                response_data = {
                    "status": "success",
                    "execution": {
                        "return_code": result.returncode,
                        "execution_time": "< 30s",
                        "script_path": str(script_path),
                        "executed_at": datetime.now().isoformat()
                    },
                    "script_results": script_output
                }

                return response_data

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse script output as JSON: {e}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "status": "error",
                        "message": "Script output is not valid JSON",
                        "raw_output": result.stdout[:500],  # First 500 chars
                        "stderr": result.stderr[:500] if result.stderr else None,
                        "timestamp": datetime.now().isoformat()
                    }
                )
        else:
            logger.error(f"Test script failed with return code: {result.returncode}")
            raise HTTPException(
                status_code=500,
                detail={
                    "status": "error",
                    "message": f"Script execution failed (exit code: {result.returncode})",
                    "stdout": result.stdout[:500] if result.stdout else None,
                    "stderr": result.stderr[:500] if result.stderr else None,
                    "return_code": result.returncode,
                    "timestamp": datetime.now().isoformat()
                }
            )

    except subprocess.TimeoutExpired:
        logger.error("Test script execution timed out")
        raise HTTPException(
            status_code=408,
            detail={
                "status": "error",
                "message": "Script execution timed out (30 seconds)",
                "timestamp": datetime.now().isoformat()
            }
        )

    except Exception as e:
        logger.error(f"Failed to execute test script: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Script execution error: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/api/mode")
async def get_current_mode():
    """Get current configuration mode (testing vs production)."""
    logger.info("Configuration mode check requested")

    try:
        # Get current settings
        settings = get_settings()
        user_agent = settings.spider.user_agent

        # Determine mode based on user agent
        if "TestCrawler" in user_agent:
            mode = "testing"
            mode_icon = "🧪"
            mode_description = "Testing Mode - Anonymized crawling"
        elif "SpigaMonde" in user_agent:
            mode = "production"
            mode_icon = "🚀"
            mode_description = "Production Mode - Official crawling"
        else:
            mode = "unknown"
            mode_icon = "❓"
            mode_description = "Unknown Mode - Custom configuration"

        response_data = {
            "status": "success",
            "mode": {
                "type": mode,
                "icon": mode_icon,
                "description": mode_description,
                "user_agent": user_agent,
                "storage_path": str(settings.storage.base_path),
                "max_pages": settings.spider.max_pages,
                "download_delay": settings.spider.download_delay
            },
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"Current mode: {mode} ({user_agent})")
        return response_data

    except Exception as e:
        logger.error(f"Failed to get configuration mode: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Failed to determine configuration mode: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.post("/api/mode/toggle")
async def toggle_mode():
    """Toggle between testing and production mode."""
    logger.info("Mode toggle requested")

    try:
        # Get current settings
        settings = get_settings()
        current_user_agent = settings.spider.user_agent

        # Determine current mode and new mode
        if "TestCrawler" in current_user_agent:
            # Currently in testing mode, switch to production
            new_mode = "production"
            new_user_agent = "SpigaMonde/0.1.0"
            new_icon = "🚀"
            new_description = "Production Mode - Official crawling"
        else:
            # Currently in production mode, switch to testing
            new_mode = "testing"
            new_user_agent = "TestCrawler/1.0"
            new_icon = "🧪"
            new_description = "Testing Mode - Anonymized crawling"

        # Update the configuration
        # Note: This is a simplified approach - in a real application,
        # you'd want to update the actual config file or environment variables
        settings.spider.user_agent = new_user_agent

        # For now, we'll just return the new mode info
        # In a full implementation, you'd persist this change

        response_data = {
            "status": "success",
            "message": f"Mode switched to {new_mode}",
            "previous_mode": "testing" if new_mode == "production" else "production",
            "new_mode": {
                "type": new_mode,
                "icon": new_icon,
                "description": new_description,
                "user_agent": new_user_agent
            },
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"Mode switched from {current_user_agent} to {new_user_agent}")
        return response_data

    except Exception as e:
        logger.error(f"Failed to toggle mode: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Failed to toggle mode: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/api/available-scripts")
async def get_available_scripts():
    """Get list of available SpigaMonde scripts."""
    logger.info("Available scripts requested")

    try:
        import os
        from pathlib import Path

        # Get the project root directory
        project_root = Path(__file__).parent.parent.parent
        scripts_dir = project_root / "scripts"

        available_scripts = {
            "cli_commands": {
                "basic-crawl": {
                    "name": "Basic Crawl",
                    "description": "Direct SpigaMonde CLI command: spiga crawl <url>",
                    "command": "spiga crawl",
                    "type": "cli_command",
                    "parameters": ["url", "depth", "max-pages", "delay"]
                }
            },
            "web_scripts": {},
            "example_scripts": {},
            "template_scripts": {}
        }

        # Scan web scripts directory (UI-specific scripts)
        web_scripts_dir = project_root / "web" / "scripts"
        if web_scripts_dir.exists():
            for script_file in web_scripts_dir.glob("*.py"):
                if script_file.name != "__init__.py":
                    script_name = script_file.stem
                    available_scripts["web_scripts"][script_name] = {
                        "name": script_name.replace("_", " ").title(),
                        "description": f"Web UI script: {script_name}",
                        "path": str(script_file),
                        "type": "web_script"
                    }

        # Scan examples directory (SpigaMonde examples)
        examples_dir = scripts_dir / "examples"
        if examples_dir.exists():
            for script_file in examples_dir.glob("*.py"):
                if script_file.name != "__init__.py":
                    script_name = script_file.stem
                    available_scripts["example_scripts"][script_name] = {
                        "name": script_name.replace("_", " ").title(),
                        "description": f"SpigaMonde example: {script_name}",
                        "path": str(script_file),
                        "type": "example_script"
                    }

        # Scan templates directory (SpigaMonde templates)
        templates_dir = scripts_dir / "templates"
        if templates_dir.exists():
            for script_file in templates_dir.glob("*.py"):
                if script_file.name != "__init__.py":
                    script_name = script_file.stem
                    available_scripts["template_scripts"][script_name] = {
                        "name": script_name.replace("_", " ").title(),
                        "description": f"SpigaMonde template: {script_name}",
                        "path": str(script_file),
                        "type": "template_script"
                    }

        return {
            "status": "success",
            "scripts": available_scripts,
            "total_count": (
                len(available_scripts["cli_commands"]) +
                len(available_scripts["web_scripts"]) +
                len(available_scripts["example_scripts"]) +
                len(available_scripts["template_scripts"])
            ),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get available scripts: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Failed to get available scripts: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.post("/api/execute-script")
async def execute_script(script_config: dict):
    """Execute a real SpigaMonde script or CLI command."""
    logger.info("Script execution requested")

    try:
        # Check if this is standalone mode (no URL required)
        standalone_mode = script_config.get('standalone_mode', False)

        # Validate required parameters for non-standalone scripts
        if not standalone_mode and not script_config.get('target_url'):
            raise HTTPException(
                status_code=400,
                detail="target_url is required for non-standalone scripts"
            )

        script_type = script_config.get('script_type', 'basic-crawl')
        target_url = script_config.get('target_url', '')
        max_depth = script_config.get('max_depth', 3)
        max_pages = script_config.get('max_pages', 50)
        crawl_delay = script_config.get('crawl_delay', 1.0)

        if standalone_mode:
            logger.info(f"Executing standalone script: {script_type}")
        else:
            logger.info(f"Executing {script_type} for {target_url}")

        import subprocess
        import threading
        import time
        from pathlib import Path

        execution_id = f"exec_{int(time.time())}"

        # Determine execution method based on script type
        if script_type == 'basic-crawl' and not standalone_mode:
            # Use SpigaMonde CLI command (requires URL)
            cmd = [
                'uv', 'run', 'python', '-m', 'spigamonde.cli', 'crawl', target_url,
                '--depth', str(max_depth),
                '--max-pages', str(max_pages),
                '--delay', str(crawl_delay)
            ]
            execution_method = "SpigaMonde CLI command"

        elif script_type.startswith('web_'):
            # Execute web UI script
            script_name = script_type.replace('web_', '')
            project_root = Path(__file__).parent.parent.parent
            script_path = project_root / "web" / "scripts" / f"{script_name}.py"

            if not script_path.exists():
                raise HTTPException(
                    status_code=404,
                    detail=f"Web script not found: {script_path}"
                )

            cmd = ['uv', 'run', 'python', str(script_path)]
            execution_method = f"Web UI script: {script_name}"

        elif script_type.startswith('example_'):
            # Execute SpigaMonde example script
            script_name = script_type.replace('example_', '')
            project_root = Path(__file__).parent.parent.parent
            script_path = project_root / "scripts" / "examples" / f"{script_name}.py"

            if not script_path.exists():
                raise HTTPException(
                    status_code=404,
                    detail=f"Example script not found: {script_path}"
                )

            cmd = ['uv', 'run', 'python', str(script_path)]
            execution_method = f"SpigaMonde example: {script_name}"

        elif script_type.startswith('template_'):
            # Execute SpigaMonde template script
            script_name = script_type.replace('template_', '')
            project_root = Path(__file__).parent.parent.parent
            script_path = project_root / "scripts" / "templates" / f"{script_name}.py"

            if not script_path.exists():
                raise HTTPException(
                    status_code=404,
                    detail=f"Template script not found: {script_path}"
                )

            cmd = ['python', str(script_path)]
            execution_method = f"SpigaMonde template: {script_name}"

        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown script type: {script_type}"
            )

        # Execute command and wait for completion
        logger.info(f"Starting {execution_method}: {' '.join(cmd)}")

        try:
            # Set environment variables to handle Unicode properly
            import os
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONLEGACYWINDOWSSTDIO'] = '0'

            # Run the command and wait for completion
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                cwd=str(Path(__file__).parent.parent.parent),
                env=env  # Use modified environment
            )

            if result.returncode == 0:
                logger.info(f"Script execution completed successfully: {execution_id}")
                if result.stdout:
                    logger.info(f"Script output: {result.stdout}")
                if result.stderr:
                    logger.warning(f"Script warnings: {result.stderr}")

                # Return successful execution results
                response_data = {
                    "status": "success",
                    "message": f"Script executed successfully: {execution_method}",
                    "script_type": script_type,
                    "execution_method": execution_method,
                    "execution_id": execution_id,
                    "output": result.stdout,
                    "warnings": result.stderr if result.stderr else None,
                    "return_code": result.returncode,
                    "timestamp": datetime.now().isoformat()
                }

                logger.info(f"Script execution completed: {execution_id} - {execution_method}")
                return response_data

            else:
                logger.error(f"Script execution failed: {execution_id}")
                logger.error(f"Return code: {result.returncode}")
                if result.stdout:
                    logger.error(f"Stdout: {result.stdout}")
                if result.stderr:
                    logger.error(f"Stderr: {result.stderr}")

                # Return error results
                raise HTTPException(
                    status_code=500,
                    detail={
                        "status": "error",
                        "message": f"Script execution failed: {execution_method}",
                        "execution_id": execution_id,
                        "return_code": result.returncode,
                        "output": result.stdout,
                        "error": result.stderr,
                        "timestamp": datetime.now().isoformat()
                    }
                )

        except subprocess.TimeoutExpired:
            logger.error(f"Script execution timed out: {execution_id}")
            raise HTTPException(
                status_code=408,
                detail={
                    "status": "timeout",
                    "message": f"Script execution timed out after 5 minutes: {execution_method}",
                    "execution_id": execution_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
        except Exception as e:
            logger.error(f"Script execution error: {execution_id} - {e}")
            raise HTTPException(
                status_code=500,
                detail={
                    "status": "error",
                    "message": f"Script execution error: {str(e)}",
                    "execution_id": execution_id,
                    "timestamp": datetime.now().isoformat()
                }
            )



    except Exception as e:
        logger.error(f"Failed to execute script: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Failed to execute script: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.post("/api/reset-system")
async def reset_system(reset_options: dict):
    """Reset system by clearing data based on options."""
    clear_database = reset_options.get('clear_database', True)
    clear_downloads = reset_options.get('clear_downloads', True)
    clear_logs = reset_options.get('clear_logs', False)

    logger.info(f"System reset requested - DB: {clear_database}, Downloads: {clear_downloads}, Logs: {clear_logs}")

    reset_results = {
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "operations": {}
    }

    try:
        # Clear database content
        if clear_database:
            try:
                with session_scope() as session:
                    # Get counts before deletion
                    content_count = session.query(Content).count()
                    analysis_count = session.query(ContentAnalysis).count()

                    # Delete all content analysis first (foreign key constraint)
                    session.query(ContentAnalysis).delete()

                    # Delete all content
                    session.query(Content).delete()

                    session.commit()

                reset_results["operations"]["database"] = {
                    "status": "success",
                    "content_deleted": content_count,
                    "analysis_deleted": analysis_count,
                    "message": f"Cleared {content_count} content items and {analysis_count} analyses"
                }
                logger.info(f"Database cleared - {content_count} content, {analysis_count} analyses")

            except Exception as e:
                reset_results["operations"]["database"] = {
                    "status": "error",
                    "error": str(e),
                    "message": "Failed to clear database"
                }
                logger.error(f"Database clear failed: {e}")

        # Clear download directories
        if clear_downloads:
            try:
                settings = get_settings()
                download_paths = [
                    Path("./downloads"),
                    Path("./test_downloads"),
                    settings.storage.base_path
                ]

                cleared_dirs = []
                total_size = 0

                for path in download_paths:
                    if path.exists() and path.is_dir():
                        # Calculate size before deletion
                        dir_size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                        total_size += dir_size

                        # Remove directory contents
                        shutil.rmtree(path)
                        path.mkdir(exist_ok=True)

                        cleared_dirs.append(str(path))

                reset_results["operations"]["downloads"] = {
                    "status": "success",
                    "directories_cleared": cleared_dirs,
                    "total_size_mb": round(total_size / (1024 * 1024), 2),
                    "message": f"Cleared {len(cleared_dirs)} download directories ({total_size / (1024 * 1024):.1f} MB)"
                }
                logger.info(f"Downloads cleared - {len(cleared_dirs)} directories, {total_size / (1024 * 1024):.1f} MB")

            except Exception as e:
                reset_results["operations"]["downloads"] = {
                    "status": "error",
                    "error": str(e),
                    "message": "Failed to clear download directories"
                }
                logger.error(f"Downloads clear failed: {e}")

        # Clear log files
        if clear_logs:
            try:
                log_paths = [
                    Path("./logs"),
                ]

                cleared_files = []
                total_size = 0

                for log_dir in log_paths:
                    if log_dir.exists() and log_dir.is_dir():
                        for log_file in log_dir.glob("*.log"):
                            if log_file.is_file():
                                file_size = log_file.stat().st_size
                                total_size += file_size
                                log_file.unlink()
                                cleared_files.append(str(log_file))

                reset_results["operations"]["logs"] = {
                    "status": "success",
                    "files_cleared": cleared_files,
                    "total_size_mb": round(total_size / (1024 * 1024), 2),
                    "message": f"Cleared {len(cleared_files)} log files ({total_size / (1024 * 1024):.1f} MB)"
                }
                logger.info(f"Logs cleared - {len(cleared_files)} files, {total_size / (1024 * 1024):.1f} MB")

            except Exception as e:
                reset_results["operations"]["logs"] = {
                    "status": "error",
                    "error": str(e),
                    "message": "Failed to clear log files"
                }
                logger.error(f"Logs clear failed: {e}")

        # Check if any operations failed
        failed_operations = [
            op for op in reset_results["operations"].values()
            if op.get("status") == "error"
        ]

        if failed_operations:
            reset_results["status"] = "partial_success"
            reset_results["message"] = f"{len(failed_operations)} operations failed"
        else:
            reset_results["message"] = "System reset completed successfully"

        logger.info(f"System reset completed - Status: {reset_results['status']}")
        return reset_results

    except Exception as e:
        logger.error(f"System reset failed: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"System reset failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/api/stats/detailed")
async def get_detailed_stats():
    """Get comprehensive statistics for the stats widget."""
    logger.info("Detailed stats requested for stats widget")

    try:
        with session_scope() as session:
            # Basic counts
            total_content = session.query(Content).count()
            analyzed_content = session.query(ContentAnalysis).count()

            # Time-based analysis
            from datetime import timedelta
            now = datetime.now()

            # Recent activity (multiple time periods)
            time_periods = {
                "last_hour": now - timedelta(hours=1),
                "last_24h": now - timedelta(hours=24),
                "last_week": now - timedelta(days=7),
                "last_month": now - timedelta(days=30)
            }

            recent_activity = {}
            for period, cutoff in time_periods.items():
                count = session.query(Content).filter(Content.created_at >= cutoff).count()
                recent_activity[period] = count

            # Content by status
            status_breakdown = {}
            for status in ['pending', 'completed', 'failed', 'skipped']:
                count = session.query(Content).filter(Content.status == status).count()
                status_breakdown[status] = count

            # Content by type
            type_breakdown = {}
            for content_type in ['document', 'image', 'video', 'audio', 'archive', 'other']:
                count = session.query(Content).filter(Content.content_type == content_type).count()
                if count > 0:  # Only include types that exist
                    type_breakdown[content_type] = count

            # File size analytics
            from sqlalchemy import func
            size_stats = session.query(
                func.sum(Content.file_size).label('total_size'),
                func.avg(Content.file_size).label('avg_size'),
                func.max(Content.file_size).label('max_size'),
                func.min(Content.file_size).label('min_size')
            ).filter(Content.file_size.isnot(None)).first()

            total_size = size_stats.total_size or 0
            avg_size = size_stats.avg_size or 0
            max_size = size_stats.max_size or 0
            min_size = size_stats.min_size or 0

            # Top domains/sources
            domain_stats = session.query(
                func.substr(Content.url, 1, func.instr(Content.url, '/') + 1).label('domain'),
                func.count().label('count')
            ).group_by('domain').order_by(func.count().desc()).limit(10).all()

            top_domains = [{"domain": domain, "count": count} for domain, count in domain_stats]

            # Recent content samples
            recent_samples = []
            recent_items = session.query(Content).order_by(
                Content.created_at.desc()
            ).limit(10).all()

            for item in recent_items:
                recent_samples.append({
                    "id": item.id,
                    "url": item.url[:100] + "..." if len(item.url) > 100 else item.url,
                    "filename": item.filename,
                    "content_type": item.content_type,
                    "status": item.status,
                    "created_at": item.created_at.isoformat() if item.created_at else None,
                    "file_size": item.file_size,
                    "file_size_mb": round(item.file_size / (1024 * 1024), 2) if item.file_size else 0
                })

            # Analysis coverage by type
            analysis_coverage = {}
            for content_type in type_breakdown.keys():
                total_type = session.query(Content).filter(Content.content_type == content_type).count()
                analyzed_type = session.query(Content).join(ContentAnalysis).filter(
                    Content.content_type == content_type
                ).count()

                if total_type > 0:
                    coverage_pct = (analyzed_type / total_type) * 100
                    analysis_coverage[content_type] = {
                        "total": total_type,
                        "analyzed": analyzed_type,
                        "percentage": round(coverage_pct, 1)
                    }

            # Performance metrics
            performance_metrics = {
                "total_storage_mb": round(total_size / (1024 * 1024), 2),
                "avg_file_size_kb": round(avg_size / 1024, 2) if avg_size else 0,
                "largest_file_mb": round(max_size / (1024 * 1024), 2) if max_size else 0,
                "smallest_file_kb": round(min_size / 1024, 2) if min_size else 0,
                "analysis_coverage_pct": round((analyzed_content / total_content * 100), 1) if total_content > 0 else 0
            }

            response_data = {
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "overview": {
                    "total_content": total_content,
                    "analyzed_content": analyzed_content,
                    "unanalyzed_content": total_content - analyzed_content,
                    "analysis_coverage_pct": performance_metrics["analysis_coverage_pct"]
                },
                "recent_activity": recent_activity,
                "content_breakdown": {
                    "by_status": status_breakdown,
                    "by_type": type_breakdown
                },
                "analysis_coverage": analysis_coverage,
                "storage_metrics": performance_metrics,
                "top_domains": top_domains,
                "recent_samples": recent_samples
            }

            logger.info(f"Detailed stats generated - {total_content} total content, {analyzed_content} analyzed")
            return response_data

    except Exception as e:
        logger.error(f"Failed to get detailed stats: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Failed to retrieve detailed statistics: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


@app.get("/api/health")
async def health_check():
    """Health check endpoint for monitoring."""
    logger.debug("Health check endpoint called")
    
    try:
        # Quick database ping
        from sqlalchemy import text
        with session_scope() as session:
            session.execute(text("SELECT 1"))
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "database": "connected",
                "logging": "active",
                "api": "running"
            }
        }
        
    except Exception as e:
        logger.warning(f"Health check failed: {e}")
        
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )


@app.get("/api/logs/recent")
async def get_recent_logs(lines: int = 50):
    """Get recent log entries."""
    logger.info(f"Recent logs requested - last {lines} lines")

    try:
        import os
        from pathlib import Path

        # Look for log files in common locations
        log_locations = [
            "spigamonde.log",
            "logs/spigamonde.log",
            "logs/app.log"
        ]

        log_content = []
        log_file_found = None

        for log_path in log_locations:
            if os.path.exists(log_path):
                log_file_found = log_path
                try:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        lines_list = f.readlines()
                        # Get last N lines
                        recent_lines = lines_list[-lines:] if len(lines_list) > lines else lines_list
                        log_content = [line.strip() for line in recent_lines if line.strip()]
                    break
                except Exception as e:
                    logger.warning(f"Could not read log file {log_path}: {e}")
                    continue

        if not log_file_found:
            # Return backend logs if no file found
            log_content = [
                "No log file found in standard locations",
                "Showing recent backend activity:",
                f"Backend started at: {datetime.now().isoformat()}",
                "Check console output for real-time logs"
            ]

        return {
            "status": "success",
            "log_file": log_file_found or "backend_memory",
            "lines_requested": lines,
            "lines_returned": len(log_content),
            "logs": log_content,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get recent logs: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "status": "error",
                "message": f"Failed to get logs: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        )


if __name__ == "__main__":
    # For development - run with: python main.py
    logger.info("Starting development server")
    uvicorn.run(
        "backend.main:app",  # Use import string for reload
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
