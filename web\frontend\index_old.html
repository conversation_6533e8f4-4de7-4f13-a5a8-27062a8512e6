<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpigaMonde Web Interface - Test</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="header-main">
                    <h1>🕷️ SpigaMonde Web Interface</h1>
                    <p>Connectivity and Logging Test</p>
                </div>
                <div class="header-status">
                    <div id="testModeIndicator" class="mode-indicator">
                        <span class="mode-icon">🔧</span>
                        <span class="mode-text">Checking Mode...</span>
                    </div>
                </div>
            </div>
        </header>

        <main class="main">
            <div class="test-section">
                <h2>🔌 Connection Test</h2>
                <p>Test the connection between frontend and backend API.</p>
                
                <button id="testConnectionBtn" class="test-button">
                    <span class="button-text">Test Connection</span>
                    <span class="button-spinner" style="display: none;">🔄</span>
                </button>
                
                <div id="connectionResult" class="result-box" style="display: none;">
                    <h3>Result:</h3>
                    <pre id="connectionData"></pre>
                </div>
            </div>

            <div class="test-section">
                <h2>📊 SpigaMonde Stats</h2>
                <p>Fetch real statistics from SpigaMonde database.</p>
                
                <button id="getStatsBtn" class="test-button">
                    <span class="button-text">Get SpigaMonde Stats</span>
                    <span class="button-spinner" style="display: none;">🔄</span>
                </button>
                
                <div id="statsResult" class="result-box" style="display: none;">
                    <h3>Statistics:</h3>
                    <pre id="statsData"></pre>
                </div>
            </div>

            <div class="test-section">
                <h2>🔄 System Reset</h2>
                <p>Clear old crawl data and reset system for fresh testing.</p>

                <div class="reset-controls">
                    <button id="resetSystemBtn" class="test-button reset-button">
                        <span class="button-text">Reset System</span>
                        <span class="button-spinner" style="display: none;">🔄</span>
                    </button>

                    <div class="reset-options">
                        <label class="checkbox-label">
                            <input type="checkbox" id="clearDatabase" checked>
                            <span class="checkmark"></span>
                            Clear database content
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="clearDownloads" checked>
                            <span class="checkmark"></span>
                            Clear download directories
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="clearLogs">
                            <span class="checkmark"></span>
                            Clear log files
                        </label>
                    </div>
                </div>

                <div id="resetResult" class="result-box reset-result" style="display: none;">
                    <h3>Reset Results:</h3>
                    <div id="resetSummary" class="reset-summary"></div>
                </div>
            </div>

            <div class="test-section">
                <h2>🚀 Run Test Script</h2>
                <p>Execute comprehensive SpigaMonde system test script.</p>

                <button id="runScriptBtn" class="test-button script-button">
                    <span class="button-text">Run Test Script</span>
                    <span class="button-spinner" style="display: none;">🔄</span>
                </button>

                <div id="scriptResult" class="result-box script-result" style="display: none;">
                    <h3>Script Results:</h3>
                    <div id="scriptSummary" class="script-summary"></div>
                    <details class="script-details">
                        <summary>View Detailed Results</summary>
                        <pre id="scriptData"></pre>
                    </details>
                </div>
            </div>

            <div class="test-section stats-widget">
                <h2>📊 Statistics Dashboard</h2>
                <p>Real-time SpigaMonde database and system statistics.</p>

                <div class="stats-controls">
                    <button id="refreshStatsBtn" class="test-button stats-button">
                        <span class="button-text">Refresh Stats</span>
                        <span class="button-spinner" style="display: none;">🔄</span>
                    </button>

                    <div class="auto-refresh-control">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoRefreshStats">
                            <span class="checkmark"></span>
                            Auto-refresh every 30s
                        </label>
                    </div>
                </div>

                <div id="statsWidget" class="stats-display" style="display: none;">
                    <!-- Overview Cards -->
                    <div class="stats-overview">
                        <div class="stat-card">
                            <div class="stat-icon">📄</div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalContent">0</div>
                                <div class="stat-label">Total Content</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">🔍</div>
                            <div class="stat-content">
                                <div class="stat-number" id="analyzedContent">0</div>
                                <div class="stat-label">Analyzed</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">💾</div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalStorage">0 MB</div>
                                <div class="stat-label">Storage Used</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-content">
                                <div class="stat-number" id="analysisProgress">0%</div>
                                <div class="stat-label">Analysis Coverage</div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Stats -->
                    <div class="stats-details">
                        <div class="stats-section">
                            <h3>📅 Recent Activity</h3>
                            <div id="recentActivity" class="activity-stats"></div>
                        </div>

                        <div class="stats-section">
                            <h3>📊 Content Breakdown</h3>
                            <div id="contentBreakdown" class="breakdown-stats"></div>
                        </div>

                        <div class="stats-section">
                            <h3>🌐 Top Sources</h3>
                            <div id="topSources" class="sources-stats"></div>
                        </div>

                        <div class="stats-section">
                            <h3>📋 Recent Content</h3>
                            <div id="recentContent" class="recent-content-list"></div>
                        </div>
                    </div>
                </div>

                <div id="statsError" class="result-box error" style="display: none;">
                    <h3>Error Loading Stats:</h3>
                    <pre id="statsErrorMessage"></pre>
                </div>
            </div>

            <div class="test-section">
                <h2>📝 Activity Log</h2>
                <p>All frontend actions and API calls are logged here.</p>
                
                <div id="activityLog" class="log-box">
                    <div class="log-entry">
                        <span class="log-time">Page loaded</span>
                        <span class="log-message">SpigaMonde Web Interface initialized</span>
                    </div>
                </div>
                
                <button id="clearLogBtn" class="clear-button">Clear Log</button>
            </div>
        </main>

        <footer class="footer">
            <p>SpigaMonde Web Interface v1.0 | Backend: <span id="backendStatus">Checking...</span></p>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html>
