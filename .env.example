# SpigaMonde Configuration Example
# Copy this file to .env and modify as needed

# Debug mode
DEBUG=false

# Database Configuration
DATABASE_URL=sqlite:///spigamonde.db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10

# Spider Configuration
SPIDER_CONCURRENT_REQUESTS=16
SPIDER_DOWNLOAD_DELAY=1.0
SPIDER_RANDOMIZE_DELAY=true
SPIDER_USER_AGENT=SpigaMonde/0.1.0 (+https://github.com/spigamonde/spigamonde)
SPIDER_MAX_DEPTH=3
SPIDER_MAX_PAGES=
SPIDER_ROBOTSTXT_OBEY=true

# File type filtering (comma-separated)
SPIDER_ALLOWED_FILE_TYPES=pdf,doc,docx,txt,rtf,odt,xls,xlsx,csv,ods,ppt,pptx,odp,jpg,jpeg,png,gif,bmp,svg,webp,mp3,wav,flac,ogg,m4a,mp4,avi,mkv,mov,wmv,flv,webm,zip,rar,7z,tar,gz,bz2,html,htm,xml,json,yaml,yml

# File size limits (in bytes)
SPIDER_MAX_FILE_SIZE=104857600  # 100MB
SPIDER_MIN_FILE_SIZE=1024       # 1KB

# Storage Configuration
STORAGE_BASE_PATH=./downloads
STORAGE_ORGANIZE_BY_DATE=true
STORAGE_ORGANIZE_BY_TYPE=true
STORAGE_ORGANIZE_BY_DOMAIN=false

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT={time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}
LOG_FILE_PATH=
LOG_MAX_FILE_SIZE=10 MB
LOG_RETENTION=30 days
LOG_STRUCTURED=true
LOG_PERFORMANCE=true

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_DASHBOARD_ENABLED=true
MONITORING_ALERTS_ENABLED=true
MONITORING_METRICS_RETENTION_HOURS=24
MONITORING_ALERT_CHECK_INTERVAL=30
MONITORING_PERFORMANCE_THRESHOLD=30.0
MONITORING_ERROR_RATE_THRESHOLD=0.2
