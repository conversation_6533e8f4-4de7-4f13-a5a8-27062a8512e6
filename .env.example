# SpigaUI Environment Configuration
# Copy this file to .env and update the values

# Application Settings
DEBUG=true
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=INFO
SERVE_STATIC=true

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# Redis Settings (for Celery)
REDIS_URL=redis://localhost:6379/0

# SpigaMonde Integration
SPIGAMONDE_DATABASE_URL=sqlite:///spigamonde.db

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security Settings
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
