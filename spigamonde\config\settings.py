"""Configuration settings for SpigaMonde."""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    url: str = Field(default="sqlite:///spigamonde.db", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    pool_size: int = Field(default=5, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=10, env="DATABASE_MAX_OVERFLOW")


class SpiderSettings(BaseSettings):
    """Spider configuration settings."""
    
    # Basic spider settings
    concurrent_requests: int = Field(default=16, env="SPIDER_CONCURRENT_REQUESTS")
    download_delay: float = Field(default=1.0, env="SPIDER_DOWNLOAD_DELAY")
    randomize_download_delay: bool = Field(default=True, env="SPIDER_RANDOMIZE_DELAY")
    
    # User agent and headers
    user_agent: str = Field(
        default="TestCrawler/1.0",
        env="SPIDER_USER_AGENT"
    )
    
    # File type filtering
    allowed_file_types: List[str] = Field(
        default=[
            "pdf", "doc", "docx", "txt", "rtf", "odt",  # Documents
            "xls", "xlsx", "csv", "ods",  # Spreadsheets
            "ppt", "pptx", "odp",  # Presentations
            "jpg", "jpeg", "png", "gif", "bmp", "svg", "webp",  # Images
            "mp3", "wav", "flac", "ogg", "m4a",  # Audio
            "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm",  # Video
            "zip", "rar", "7z", "tar", "gz", "bz2",  # Archives
            "html", "htm", "xml", "json", "yaml", "yml",  # Web/Data
        ],
        env="SPIDER_ALLOWED_FILE_TYPES"
    )
    
    # Size limits (in bytes)
    max_file_size: int = Field(default=100 * 1024 * 1024, env="SPIDER_MAX_FILE_SIZE")  # 100MB
    min_file_size: int = Field(default=1024, env="SPIDER_MIN_FILE_SIZE")  # 1KB
    
    # Depth and crawling limits
    max_depth: int = Field(default=3, env="SPIDER_MAX_DEPTH")
    max_pages: Optional[int] = Field(default=None, env="SPIDER_MAX_PAGES")
    
    # Respect robots.txt
    robotstxt_obey: bool = Field(default=True, env="SPIDER_ROBOTSTXT_OBEY")
    
    @field_validator("allowed_file_types", mode="before")
    @classmethod
    def parse_file_types(cls, v):
        """Parse file types from environment variable if it's a string."""
        if isinstance(v, str):
            return [ft.strip().lower() for ft in v.split(",")]
        return [ft.lower() for ft in v]


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""

    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        env="LOG_FORMAT"
    )
    file_path: Optional[str] = Field(default="logs/spigamonde.log", env="LOG_FILE_PATH")  # Relative to project root
    max_file_size: str = Field(default="10 MB", env="LOG_MAX_FILE_SIZE")
    retention: str = Field(default="30 days", env="LOG_RETENTION")
    structured_logging: bool = Field(default=True, env="LOG_STRUCTURED")
    performance_logging: bool = Field(default=True, env="LOG_PERFORMANCE")


class MonitoringSettings(BaseSettings):
    """Monitoring and alerting configuration settings."""

    enabled: bool = Field(default=True, env="MONITORING_ENABLED")
    dashboard_enabled: bool = Field(default=True, env="MONITORING_DASHBOARD_ENABLED")
    alerts_enabled: bool = Field(default=True, env="MONITORING_ALERTS_ENABLED")
    metrics_retention_hours: int = Field(default=24, env="MONITORING_METRICS_RETENTION_HOURS")
    alert_check_interval_seconds: int = Field(default=30, env="MONITORING_ALERT_CHECK_INTERVAL")
    performance_threshold_seconds: float = Field(default=30.0, env="MONITORING_PERFORMANCE_THRESHOLD")
    error_rate_threshold: float = Field(default=0.2, env="MONITORING_ERROR_RATE_THRESHOLD")


class StorageSettings(BaseSettings):
    """File storage configuration settings."""
    
    base_path: Path = Field(default=Path("./downloads"), env="STORAGE_BASE_PATH")
    organize_by_date: bool = Field(default=True, env="STORAGE_ORGANIZE_BY_DATE")
    organize_by_type: bool = Field(default=True, env="STORAGE_ORGANIZE_BY_TYPE")
    organize_by_domain: bool = Field(default=False, env="STORAGE_ORGANIZE_BY_DOMAIN")
    
    @field_validator("base_path", mode="before")
    @classmethod
    def parse_path(cls, v):
        """Convert string path to Path object."""
        return Path(v)


class Settings(BaseSettings):
    """Main application settings."""
    
    # Application info
    app_name: str = "SpigaMonde"
    version: str = "0.1.0"
    debug: bool = Field(default=False, env="DEBUG")
    
    # Component settings
    database: DatabaseSettings = DatabaseSettings()
    spider: SpiderSettings = SpiderSettings()
    logging: LoggingSettings = LoggingSettings()
    storage: StorageSettings = StorageSettings()
    monitoring: MonitoringSettings = MonitoringSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment and config files."""
    global settings
    settings = Settings()
    return settings
