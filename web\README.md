# SpigaMonde Web Interface

**Modern web interface for SpigaMonde crawling and monitoring**

## 🚀 Quick Start

### Prerequisites
- Python 3.8+ with SpigaMonde installed
- Node.js not required (uses vanilla HTML/CSS/JS)

### 1. Start the Backend Server
```bash
# From the SpigaMonde root directory
cd web
uvicorn backend.main:app --reload --host 127.0.0.1 --port 8000
```

### 2. Start the Frontend Server
```bash
# In a new terminal, from the SpigaMonde root directory
cd web/frontend
python -m http.server 3000
```

### 3. Access the Interface
Open your browser and navigate to:
- **Frontend**: http://localhost:3000
- **Backend API**: http://127.0.0.1:8000/docs (FastAPI docs)

## 🎯 Interface Features

### **Dashboard Tab**
- Quick statistics overview
- Navigation shortcuts
- System status indicators

### **Analytics Tab** 
- Detailed crawl statistics
- Performance metrics
- Data visualization

### **Scripts Tab**
- **🔧 CLI Commands**: Direct `spiga` commands (e.g., `spiga crawl <url>`)
- **🌐 Web UI Scripts**: Python scripts optimized for web interface
- **📚 SpigaMonde Examples**: Example scripts from main project
- **📝 SpigaMonde Templates**: Customizable script templates
- Configure crawl parameters and execution options
- Real-time progress tracking and error handling

### **System Tab**
- Connection testing
- System diagnostics
- Database reset functionality
- Mode switching (Testing/Production)

### **Logs Tab**
- Real-time activity monitoring
- Error tracking
- System event logging

## ⚙️ Configuration

### Mode Switching
The interface supports two modes:

**🧪 Testing Mode** (Default):
- User-Agent: `TestCrawler/1.0`
- Safe for development and testing
- Limited crawl scope

**🚀 Production Mode**:
- User-Agent: `SpigaMonde/0.1.0`
- Full production settings
- Official crawling identification

Switch modes using the toggle button in the header.

## 📜 Script Types

The Scripts tab organizes different types of executable scripts:

### **🔧 CLI Commands**
Direct SpigaMonde command-line interface commands:
- **Example**: `spiga crawl <url> --depth 2 --max-pages 50`
- **Use Case**: Standard crawling operations
- **Parameters**: URL, depth, pages, delay configurable via UI

### **🌐 Web UI Scripts**
Python scripts optimized for web interface execution:
- **Location**: `web/scripts/`
- **Purpose**: Web-friendly scripts with progress reporting
- **Features**: Limited scope, clear error handling, reasonable timeouts
- **Example**: `test_crawl_simple.py` - Simple test crawl

### **📚 SpigaMonde Examples**
Example scripts from the main SpigaMonde project:
- **Location**: `scripts/examples/`
- **Purpose**: Demonstrate SpigaMonde capabilities
- **Examples**: Academic papers, news aggregation, content extraction
- **Note**: May require modification for web interface use

### **📝 SpigaMonde Templates**
Customizable script templates:
- **Location**: `scripts/templates/`
- **Purpose**: Starting points for custom scripts
- **Usage**: Copy, modify, and customize for specific needs

## 🔧 Development

### Backend Development
```bash
# Auto-reload on changes
cd web
uvicorn backend.main:app --reload --host 127.0.0.1 --port 8000

# View logs
tail -f ../spigamonde.log
```

### Frontend Development
```bash
# Serve with auto-refresh
cd web/frontend
python -m http.server 3000

# For cache-busting during development, add version parameters:
# http://localhost:3000?v=1
```

### API Endpoints
- `GET /api/test` - Connection testing
- `GET /api/spiga/stats` - SpigaMonde statistics
- `POST /api/run-script` - System test execution
- `GET /api/mode` - Current mode detection
- `POST /api/mode/toggle` - Mode switching
- `GET /api/available-scripts` - List available scripts
- `POST /api/execute-script` - Execute crawl scripts

## 🐛 Troubleshooting

### Backend Issues
```bash
# Check if backend is running
curl http://127.0.0.1:8000/api/health

# View backend logs
cd web
uvicorn backend.main:app --log-level debug

# Check SpigaMonde installation
python -c "import spigamonde; print('SpigaMonde installed')"
```

### Frontend Issues
```bash
# Clear browser cache
# Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)

# Check console for JavaScript errors
# Open browser dev tools (F12) and check Console tab

# Verify files are being served
curl http://localhost:3000
```

### Script Execution Issues
```bash
# Test SpigaMonde CLI directly
spiga crawl https://httpbin.org/html --depth 1 --max-pages 5

# Check available scripts
ls scripts/examples/
ls scripts/templates/

# Verify script permissions
python scripts/examples/simple_working_economic_news.py
```

## 📁 File Structure

```
web/
├── README.md                 # This file
├── backend/
│   ├── main.py              # FastAPI backend server
│   └── requirements.txt     # Python dependencies
├── frontend/
│   ├── index.html           # Main interface
│   ├── style.css            # Styling
│   └── app-simple.js        # JavaScript functionality
└── scripts/
    ├── README.md            # Web scripts documentation
    └── *.py                 # Web UI optimized Python scripts
```

## 🔒 Security Notes

- Backend runs on localhost only by default
- No authentication required for local development
- Production deployment should add proper security measures
- Scripts execute with current user permissions

## 📚 Additional Resources

- **Main Documentation**: `../docs/web-interface-implementation.md`
- **Crawl Guide**: `../docs/crawl-guide.md`
- **SpigaMonde CLI**: `spiga --help`
- **API Documentation**: http://127.0.0.1:8000/docs (when backend is running)

---

**Need help?** Check the main SpigaMonde documentation or open an issue on GitHub.
