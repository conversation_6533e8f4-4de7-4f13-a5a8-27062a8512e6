{"mcpServers": {"sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAHznBOM5M44yY781-D0Lrvhu7_fBq"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "alwaysAllow": ["read_graph", "create_entities", "delete_entities", "delete_observations", "delete_relations", "search_nodes", "open_nodes", "create_relations", "add_observations", "search"], "env": {"MEMORY_FILE_PATH": "I:/SpigaMonde/memory/memory.jsonl"}}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "qdrant": {"command": "uvx", "args": ["mcp-server-qdrant"], "env": {"QDRANT_URL": "http://localhost:6333", "QDRANT_API_KEY": "test123", "COLLECTION_NAME": "spiga_collection", "EMBEDDING_MODEL": "text-embedding-004"}}}}