"""Alerting system for SpigaMonde monitoring."""

import time
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from threading import Thread, Event
from collections import defaultdict

from .logger import get_logger
from .metrics import get_metrics_collector


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class Alert:
    """Represents an alert."""
    id: str
    name: str
    severity: AlertSeverity
    message: str
    timestamp: datetime
    status: AlertStatus = AlertStatus.ACTIVE
    metadata: Dict[str, Any] = field(default_factory=dict)
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None


@dataclass
class AlertRule:
    """Defines an alert rule."""
    name: str
    condition: Callable[[], bool]
    severity: AlertSeverity
    message_template: str
    cooldown_minutes: int = 5
    auto_resolve: bool = True
    resolve_condition: Optional[Callable[[], bool]] = None
    metadata_extractor: Optional[Callable[[], Dict[str, Any]]] = None


class AlertManager:
    """Manages alerts and alert rules."""
    
    def __init__(self):
        self.logger = get_logger()
        self.metrics = get_metrics_collector()
        self._rules: Dict[str, AlertRule] = {}
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_history: List[Alert] = []
        self._last_check: Dict[str, datetime] = {}
        self._running = False
        self._check_thread: Optional[Thread] = None
        self._stop_event = Event()
        self._check_interval = 30  # seconds
    
    def add_rule(self, rule: AlertRule):
        """Add an alert rule."""
        self._rules[rule.name] = rule
        self.logger.info(f"Added alert rule: {rule.name}", rule_name=rule.name, severity=rule.severity.value)
    
    def remove_rule(self, rule_name: str):
        """Remove an alert rule."""
        if rule_name in self._rules:
            del self._rules[rule_name]
            self.logger.info(f"Removed alert rule: {rule_name}", rule_name=rule_name)
    
    def start_monitoring(self):
        """Start the alert monitoring thread."""
        if self._running:
            return
        
        self._running = True
        self._stop_event.clear()
        self._check_thread = Thread(target=self._monitoring_loop, daemon=True)
        self._check_thread.start()
        self.logger.info("Alert monitoring started")
    
    def stop_monitoring(self):
        """Stop the alert monitoring thread."""
        if not self._running:
            return
        
        self._running = False
        self._stop_event.set()
        if self._check_thread:
            self._check_thread.join(timeout=5)
        self.logger.info("Alert monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self._running and not self._stop_event.is_set():
            try:
                self._check_all_rules()
                self._stop_event.wait(self._check_interval)
            except Exception as e:
                self.logger.error(f"Error in alert monitoring loop: {e}")
                self._stop_event.wait(5)  # Wait before retrying
    
    def _check_all_rules(self):
        """Check all alert rules."""
        current_time = datetime.utcnow()
        
        for rule_name, rule in self._rules.items():
            try:
                # Check cooldown
                last_check = self._last_check.get(rule_name)
                if last_check and (current_time - last_check).total_seconds() < rule.cooldown_minutes * 60:
                    continue
                
                # Check condition
                if rule.condition():
                    self._trigger_alert(rule, current_time)
                elif rule.auto_resolve and rule.resolve_condition and rule.resolve_condition():
                    self._resolve_alert(rule_name, current_time)
                
                self._last_check[rule_name] = current_time
                
            except Exception as e:
                self.logger.error(f"Error checking rule {rule_name}: {e}", rule_name=rule_name)
    
    def _trigger_alert(self, rule: AlertRule, timestamp: datetime):
        """Trigger an alert."""
        alert_id = f"{rule.name}_{int(timestamp.timestamp())}"
        
        # Check if alert is already active
        if rule.name in self._active_alerts:
            return
        
        # Extract metadata
        metadata = {}
        if rule.metadata_extractor:
            try:
                metadata = rule.metadata_extractor()
            except Exception as e:
                self.logger.error(f"Error extracting metadata for rule {rule.name}: {e}")
        
        # Create alert
        alert = Alert(
            id=alert_id,
            name=rule.name,
            severity=rule.severity,
            message=rule.message_template.format(**metadata) if metadata else rule.message_template,
            timestamp=timestamp,
            metadata=metadata
        )
        
        # Store alert
        self._active_alerts[rule.name] = alert
        self._alert_history.append(alert)
        
        # Log alert
        self.logger.error(
            f"ALERT TRIGGERED: {alert.message}",
            alert_id=alert.id,
            alert_name=alert.name,
            severity=alert.severity.value,
            **metadata
        )
        
        # Send notifications (if configured)
        self._send_notifications(alert)
    
    def _resolve_alert(self, rule_name: str, timestamp: datetime):
        """Resolve an active alert."""
        if rule_name not in self._active_alerts:
            return
        
        alert = self._active_alerts[rule_name]
        alert.status = AlertStatus.RESOLVED
        alert.resolved_at = timestamp
        
        # Remove from active alerts
        del self._active_alerts[rule_name]
        
        self.logger.info(
            f"ALERT RESOLVED: {alert.name}",
            alert_id=alert.id,
            alert_name=alert.name,
            duration_minutes=(timestamp - alert.timestamp).total_seconds() / 60
        )
    
    def _send_notifications(self, alert: Alert):
        """Send alert notifications."""
        # This is a placeholder for notification integrations
        # In a real implementation, you might send emails, Slack messages, etc.
        pass
    
    def acknowledge_alert(self, rule_name: str):
        """Acknowledge an active alert."""
        if rule_name in self._active_alerts:
            alert = self._active_alerts[rule_name]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            
            self.logger.info(f"Alert acknowledged: {rule_name}", alert_name=rule_name)
    
    def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts."""
        return list(self._active_alerts.values())
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """Get alert history for the specified number of hours."""
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        return [alert for alert in self._alert_history if alert.timestamp >= cutoff]
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get a summary of alerts."""
        active_by_severity = defaultdict(int)
        for alert in self._active_alerts.values():
            active_by_severity[alert.severity.value] += 1
        
        recent_history = self.get_alert_history(24)
        history_by_severity = defaultdict(int)
        for alert in recent_history:
            history_by_severity[alert.severity.value] += 1
        
        return {
            "active_alerts": len(self._active_alerts),
            "active_by_severity": dict(active_by_severity),
            "total_rules": len(self._rules),
            "recent_alerts_24h": len(recent_history),
            "recent_by_severity": dict(history_by_severity),
            "monitoring_active": self._running
        }


# Global alert manager
alert_manager = AlertManager()


def get_alert_manager() -> AlertManager:
    """Get the global alert manager."""
    return alert_manager


# Predefined alert rules
def create_default_alert_rules() -> List[AlertRule]:
    """Create default alert rules for common issues."""
    rules = []
    
    # High error rate
    def high_error_rate():
        stats = alert_manager.metrics.get_performance_stats()
        for operation, perf_stats in stats.items():
            if perf_stats and perf_stats.count >= 10:
                error_rate = perf_stats.error_count / perf_stats.count
                if error_rate > 0.2:  # 20% error rate
                    return True
        return False
    
    def error_rate_metadata():
        stats = alert_manager.metrics.get_performance_stats()
        worst_operation = None
        worst_rate = 0
        
        for operation, perf_stats in stats.items():
            if perf_stats and perf_stats.count >= 10:
                error_rate = perf_stats.error_count / perf_stats.count
                if error_rate > worst_rate:
                    worst_rate = error_rate
                    worst_operation = operation
        
        return {
            "operation": worst_operation,
            "error_rate": f"{worst_rate:.1%}",
            "error_count": stats[worst_operation].error_count if worst_operation else 0
        }
    
    rules.append(AlertRule(
        name="high_error_rate",
        condition=high_error_rate,
        severity=AlertSeverity.WARNING,
        message_template="High error rate detected: {error_rate} in operation {operation}",
        cooldown_minutes=10,
        metadata_extractor=error_rate_metadata
    ))
    
    # Slow performance
    def slow_performance():
        stats = alert_manager.metrics.get_performance_stats()
        for operation, perf_stats in stats.items():
            if perf_stats and perf_stats.avg_duration > 30:  # 30 seconds
                return True
        return False
    
    rules.append(AlertRule(
        name="slow_performance",
        condition=slow_performance,
        severity=AlertSeverity.WARNING,
        message_template="Slow performance detected in operations",
        cooldown_minutes=15
    ))
    
    # No recent activity
    def no_recent_activity():
        stats = alert_manager.metrics.get_performance_stats()
        cutoff = datetime.utcnow() - timedelta(minutes=30)
        
        for operation, perf_stats in stats.items():
            if perf_stats and perf_stats.last_execution and perf_stats.last_execution > cutoff:
                return False
        return len(stats) > 0  # Only alert if we have operations but none are recent
    
    rules.append(AlertRule(
        name="no_recent_activity",
        condition=no_recent_activity,
        severity=AlertSeverity.INFO,
        message_template="No recent crawling activity detected",
        cooldown_minutes=60
    ))
    
    return rules


def setup_default_alerts():
    """Setup default alert rules."""
    rules = create_default_alert_rules()
    for rule in rules:
        alert_manager.add_rule(rule)
    
    alert_manager.start_monitoring()
    alert_manager.logger.info(f"Setup {len(rules)} default alert rules")


def start_alerting():
    """Start the alerting system with default rules."""
    setup_default_alerts()


def stop_alerting():
    """Stop the alerting system."""
    alert_manager.stop_monitoring()
