"""File utility functions for content detection and processing."""

import hashlib
import mimetypes
import os
import re
from pathlib import Path
from typing import Dict, Optional, Tuple
from urllib.parse import urlparse

from loguru import logger

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    logger.warning("python-magic not available, file type detection will be limited")

from ..config.settings import get_settings
from ..models.content import ContentType


class FileTypeDetector:
    """Utility class for detecting file types and content classification."""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Initialize python-magic if available
        if MAGIC_AVAILABLE:
            try:
                self.magic_mime = magic.Magic(mime=True)
                self.magic_desc = magic.Magic()
            except Exception as e:
                logger.warning(f"Failed to initialize python-magic: {e}")
                self.magic_mime = None
                self.magic_desc = None
        else:
            self.magic_mime = None
            self.magic_desc = None
        
        # File extension to content type mapping
        self.extension_to_content_type = {
            # Documents
            'pdf': ContentType.DOCUMENT,
            'doc': ContentType.DOCUMENT,
            'docx': ContentType.DOCUMENT,
            'txt': ContentType.DOCUMENT,
            'rtf': ContentType.DOCUMENT,
            'odt': ContentType.DOCUMENT,
            
            # Spreadsheets
            'xls': ContentType.DOCUMENT,
            'xlsx': ContentType.DOCUMENT,
            'csv': ContentType.DATA_FILE,
            'ods': ContentType.DOCUMENT,
            
            # Presentations
            'ppt': ContentType.DOCUMENT,
            'pptx': ContentType.DOCUMENT,
            'odp': ContentType.DOCUMENT,
            
            # Images
            'jpg': ContentType.IMAGE,
            'jpeg': ContentType.IMAGE,
            'png': ContentType.IMAGE,
            'gif': ContentType.IMAGE,
            'bmp': ContentType.IMAGE,
            'svg': ContentType.IMAGE,
            'webp': ContentType.IMAGE,
            'tiff': ContentType.IMAGE,
            'ico': ContentType.IMAGE,
            
            # Audio
            'mp3': ContentType.AUDIO,
            'wav': ContentType.AUDIO,
            'flac': ContentType.AUDIO,
            'ogg': ContentType.AUDIO,
            'm4a': ContentType.AUDIO,
            'aac': ContentType.AUDIO,
            'wma': ContentType.AUDIO,
            
            # Video
            'mp4': ContentType.VIDEO,
            'avi': ContentType.VIDEO,
            'mkv': ContentType.VIDEO,
            'mov': ContentType.VIDEO,
            'wmv': ContentType.VIDEO,
            'flv': ContentType.VIDEO,
            'webm': ContentType.VIDEO,
            'm4v': ContentType.VIDEO,
            
            # Archives
            'zip': ContentType.ARCHIVE,
            'rar': ContentType.ARCHIVE,
            '7z': ContentType.ARCHIVE,
            'tar': ContentType.ARCHIVE,
            'gz': ContentType.ARCHIVE,
            'bz2': ContentType.ARCHIVE,
            'xz': ContentType.ARCHIVE,
            
            # Web and data files
            'html': ContentType.WEB_PAGE,
            'htm': ContentType.WEB_PAGE,
            'xml': ContentType.DATA_FILE,
            'json': ContentType.DATA_FILE,
            'yaml': ContentType.DATA_FILE,
            'yml': ContentType.DATA_FILE,
            'css': ContentType.WEB_PAGE,
            'js': ContentType.WEB_PAGE,
        }
    
    def get_file_info_from_url(self, url: str) -> Dict[str, Optional[str]]:
        """Extract file information from URL."""
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        # Extract filename and extension
        filename = os.path.basename(path) if path else None
        extension = None
        
        if filename and '.' in filename:
            extension = filename.split('.')[-1].lower()
        
        # Get MIME type from URL
        mime_type, _ = mimetypes.guess_type(url)
        
        return {
            'filename': filename,
            'extension': extension,
            'mime_type': mime_type,
            'path': path
        }
    
    def get_content_type_from_extension(self, extension: str) -> ContentType:
        """Get content type based on file extension."""
        if not extension:
            return ContentType.OTHER
        
        return self.extension_to_content_type.get(
            extension.lower(), 
            ContentType.OTHER
        )
    
    def get_content_type_from_mime(self, mime_type: str) -> ContentType:
        """Get content type based on MIME type."""
        if not mime_type:
            return ContentType.OTHER
        
        mime_lower = mime_type.lower()
        
        if mime_lower.startswith('text/'):
            if 'html' in mime_lower:
                return ContentType.WEB_PAGE
            return ContentType.DOCUMENT
        elif mime_lower.startswith('image/'):
            return ContentType.IMAGE
        elif mime_lower.startswith('video/'):
            return ContentType.VIDEO
        elif mime_lower.startswith('audio/'):
            return ContentType.AUDIO
        elif mime_lower.startswith('application/'):
            if any(doc_type in mime_lower for doc_type in ['pdf', 'word', 'document']):
                return ContentType.DOCUMENT
            elif any(arch_type in mime_lower for arch_type in ['zip', 'rar', 'tar', 'gzip']):
                return ContentType.ARCHIVE
            elif any(data_type in mime_lower for data_type in ['json', 'xml', 'csv']):
                return ContentType.DATA_FILE
        
        return ContentType.OTHER
    
    def detect_content_type(self, url: str, content: Optional[bytes] = None) -> Tuple[ContentType, Dict[str, Optional[str]]]:
        """Detect content type from URL and optionally from content bytes."""
        file_info = self.get_file_info_from_url(url)
        
        # Try to determine content type from extension first
        content_type = ContentType.OTHER
        if file_info['extension']:
            content_type = self.get_content_type_from_extension(file_info['extension'])
        
        # If we have content bytes and magic is available, use it for better detection
        if content and self.magic_mime:
            try:
                detected_mime = self.magic_mime.from_buffer(content)
                file_info['detected_mime'] = detected_mime
                
                # Override content type if magic detection provides better info
                magic_content_type = self.get_content_type_from_mime(detected_mime)
                if magic_content_type != ContentType.OTHER:
                    content_type = magic_content_type
                    
            except Exception as e:
                logger.debug(f"Magic detection failed for URL {url}: {e}")
        
        # Fallback to MIME type from URL if still unknown
        if content_type == ContentType.OTHER and file_info['mime_type']:
            content_type = self.get_content_type_from_mime(file_info['mime_type'])
        
        return content_type, file_info
    
    def is_allowed_file_type(self, extension: str) -> bool:
        """Check if file extension is in allowed types."""
        if not extension:
            return False
        
        return extension.lower() in [ft.lower() for ft in self.settings.spider.allowed_file_types]
    
    def should_download_content(self, url: str, content_length: Optional[int] = None) -> Tuple[bool, str]:
        """Determine if content should be downloaded based on size and type."""
        file_info = self.get_file_info_from_url(url)
        
        # Check file type
        if file_info['extension'] and not self.is_allowed_file_type(file_info['extension']):
            return False, f"File type '{file_info['extension']}' not in allowed types"
        
        # Check file size
        if content_length:
            if content_length > self.settings.spider.max_file_size:
                return False, f"File size {content_length} exceeds maximum {self.settings.spider.max_file_size}"
            
            if content_length < self.settings.spider.min_file_size:
                return False, f"File size {content_length} below minimum {self.settings.spider.min_file_size}"
        
        return True, "Content approved for download"


def calculate_content_hash(content: bytes) -> str:
    """Calculate SHA-256 hash of content for deduplication."""
    return hashlib.sha256(content).hexdigest()


def calculate_url_hash(url: str) -> str:
    """Calculate SHA-256 hash of URL for indexing."""
    return hashlib.sha256(url.encode('utf-8')).hexdigest()


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage."""
    if not filename:
        return "unknown_file"
    
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    sanitized = sanitized.strip('. ')
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = os.path.splitext(sanitized)
        max_name_len = 255 - len(ext)
        sanitized = name[:max_name_len] + ext
    
    return sanitized or "unknown_file"


def get_storage_path(url: str, filename: str, content_type: ContentType) -> Path:
    """Generate storage path for downloaded content."""
    settings = get_settings()
    base_path = settings.storage.base_path
    
    # Start with base path
    path_parts = [base_path]
    
    # Organize by date if enabled
    if settings.storage.organize_by_date:
        from datetime import datetime
        today = datetime.now()
        path_parts.extend([str(today.year), f"{today.month:02d}", f"{today.day:02d}"])
    
    # Organize by domain if enabled
    if settings.storage.organize_by_domain:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc or "unknown_domain"
        path_parts.append(domain)
    
    # Organize by content type if enabled
    if settings.storage.organize_by_type:
        path_parts.append(content_type.value)
    
    # Create directory path
    dir_path = Path(*path_parts)
    dir_path.mkdir(parents=True, exist_ok=True)
    
    # Add filename
    safe_filename = sanitize_filename(filename)
    return dir_path / safe_filename


# Global file type detector instance
file_detector = FileTypeDetector()


def get_file_detector() -> FileTypeDetector:
    """Get the global file type detector instance."""
    return file_detector
