#!/usr/bin/env python3
"""
Web Interface Test Script
Demonstrates SpigaMonde operations that can be triggered from the web interface.

This script performs various operations and returns structured results
that can be displayed in the web interface.
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime, timedelta

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.config.settings import get_settings
from spigamonde.monitoring.logger import get_logger

logger = get_logger()

def get_system_info():
    """Get current system and configuration information."""
    try:
        settings = get_settings()
        
        return {
            "status": "success",
            "system": {
                "user_agent": settings.spider.user_agent,
                "max_depth": settings.spider.max_depth,
                "download_delay": settings.spider.download_delay,
                "concurrent_requests": settings.spider.concurrent_requests,
                "storage_path": str(settings.storage.base_path),
                "max_file_size_mb": settings.spider.max_file_size / (1024 * 1024),
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def get_database_stats():
    """Get detailed database statistics."""
    try:
        with session_scope() as session:
            # Basic counts
            total_content = session.query(Content).count()
            analyzed_content = session.query(ContentAnalysis).count()
            
            # Recent activity (last 24 hours)
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_content = session.query(Content).filter(
                Content.created_at >= recent_cutoff
            ).count()
            
            # Content by status
            status_counts = {}
            for status in ['pending', 'completed', 'failed', 'skipped']:
                count = session.query(Content).filter(Content.status == status).count()
                status_counts[status] = count
            
            # Content by type
            type_counts = {}
            for content_type in ['document', 'image', 'video', 'audio', 'archive', 'other']:
                count = session.query(Content).filter(Content.content_type == content_type).count()
                type_counts[content_type] = count
            
            # Recent content samples
            recent_samples = []
            recent_items = session.query(Content).order_by(
                Content.created_at.desc()
            ).limit(5).all()
            
            for item in recent_items:
                recent_samples.append({
                    "id": item.id,
                    "url": item.url,
                    "filename": item.filename,
                    "content_type": item.content_type,
                    "status": item.status,
                    "created_at": item.created_at.isoformat() if item.created_at else None,
                    "file_size": item.file_size
                })
            
            return {
                "status": "success",
                "database": {
                    "total_content": total_content,
                    "analyzed_content": analyzed_content,
                    "recent_content_24h": recent_content,
                    "analysis_coverage": f"{(analyzed_content/total_content*100):.1f}%" if total_content > 0 else "0%",
                    "status_breakdown": status_counts,
                    "type_breakdown": type_counts,
                    "recent_samples": recent_samples
                },
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def test_database_connection():
    """Test database connectivity and operations."""
    try:
        # Initialize database
        init_database()
        
        # Test basic query
        with session_scope() as session:
            # Simple test query using SQLAlchemy
            from sqlalchemy import text
            result = session.execute(text("SELECT COUNT(*) as count FROM content")).fetchone()
            content_count = result[0] if result else 0

            # Test write operation (safe)
            test_query = text("SELECT 1 as test")
            test_result = session.execute(test_query).fetchone()
            
        return {
            "status": "success",
            "database": {
                "connection": "successful",
                "content_count": content_count,
                "read_test": "passed",
                "write_test": "passed" if test_result else "failed"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def run_comprehensive_test():
    """Run a comprehensive test of SpigaMonde systems."""
    logger.info("Starting comprehensive test script from web interface")
    
    results = {
        "test_name": "SpigaMonde Comprehensive Test",
        "started_at": datetime.now().isoformat(),
        "tests": {}
    }
    
    # Test 1: System Information
    logger.info("Testing system information retrieval")
    results["tests"]["system_info"] = get_system_info()
    
    # Test 2: Database Connection
    logger.info("Testing database connection")
    results["tests"]["database_connection"] = test_database_connection()
    
    # Test 3: Database Statistics
    logger.info("Testing database statistics")
    results["tests"]["database_stats"] = get_database_stats()
    
    # Test 4: Configuration Check
    logger.info("Testing configuration")
    try:
        settings = get_settings()
        config_test = {
            "status": "success",
            "config": {
                "mode": "testing" if "TestCrawler" in settings.spider.user_agent else "production",
                "user_agent": settings.spider.user_agent,
                "storage_organized": settings.storage.organize_by_date,
                "robots_obey": settings.spider.robotstxt_obey
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        config_test = {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
    
    results["tests"]["configuration"] = config_test
    
    # Calculate overall status
    all_successful = all(
        test.get("status") == "success" 
        for test in results["tests"].values()
    )
    
    results["completed_at"] = datetime.now().isoformat()
    results["overall_status"] = "success" if all_successful else "partial_failure"
    results["summary"] = {
        "total_tests": len(results["tests"]),
        "successful_tests": sum(1 for test in results["tests"].values() if test.get("status") == "success"),
        "failed_tests": sum(1 for test in results["tests"].values() if test.get("status") == "error")
    }
    
    logger.info(f"Comprehensive test completed - Status: {results['overall_status']}")
    
    return results

def main():
    """Main function for command line execution."""
    result = run_comprehensive_test()
    print(json.dumps(result, indent=2))
    return 0 if result["overall_status"] == "success" else 1

if __name__ == "__main__":
    sys.exit(main())
