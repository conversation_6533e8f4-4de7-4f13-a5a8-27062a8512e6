"""Enhanced structured logging system for SpigaMonde."""

import json
import sys
import time
import uuid
from contextlib import contextmanager
from datetime import datetime
from functools import wraps
from pathlib import Path
from typing import Any, Dict, Optional, Union
from contextvars import ContextVar

from loguru import logger

from ..config.settings import get_settings


# Context variables for correlation tracking
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
session_id: ContextVar[Optional[str]] = ContextVar('session_id', default=None)
spider_name: ContextVar[Optional[str]] = ContextVar('spider_name', default=None)


class StructuredLogger:
    """Enhanced logger with structured logging capabilities."""
    
    def __init__(self):
        self.settings = get_settings()
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup loguru with enhanced configuration."""
        # Remove default logger
        logger.remove()
        
        # Console logger with rich formatting
        console_format = self._get_console_format()
        logger.add(
            sys.stderr,
            level=self.settings.logging.level,
            format=console_format,
            colorize=True,
            backtrace=True,
            diagnose=True,
            filter=self._add_correlation_id_filter
        )
        
        # File logger with JSON formatting for structured logs
        if self.settings.logging.file_path:
            # Resolve log path relative to project root (not current working directory)
            log_path = self._resolve_log_path(self.settings.logging.file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)

            json_format = self._get_json_format()
            logger.add(
                str(log_path),
                level=self.settings.logging.level,
                format=json_format,
                rotation=self.settings.logging.max_file_size,
                retention=self.settings.logging.retention,
                serialize=False  # We'll handle JSON serialization ourselves
            )
        
        # Performance logs (separate file)
        perf_log_path = self._get_performance_log_path()
        if perf_log_path:
            # Ensure performance log directory exists
            perf_log_path.parent.mkdir(parents=True, exist_ok=True)
            logger.add(
                perf_log_path,
                level="INFO",
                format=json_format,
                rotation="100 MB",
                retention="7 days",
                filter=lambda record: record["extra"].get("log_type") == "performance"
            )

        # Error logs (separate file)
        error_log_path = self._get_error_log_path()
        if error_log_path:
            # Ensure error log directory exists
            error_log_path.parent.mkdir(parents=True, exist_ok=True)
            logger.add(
                error_log_path,
                level="ERROR",
                format=json_format,
                rotation="50 MB",
                retention="30 days",
                filter=lambda record: record["level"].name in ["ERROR", "CRITICAL"]
            )
    
    def _get_console_format(self) -> str:
        """Get console log format with context information."""
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{extra[correlation_id]}</cyan> | "
            "<blue>{name}:{function}:{line}</blue> - "
            "<level>{message}</level>"
        )
    
    def _get_json_format(self) -> str:
        """Get JSON log format for structured logging."""
        return "{message}"
    
    def _resolve_log_path(self, log_path: str) -> Path:
        """Resolve log path relative to project root."""
        path = Path(log_path)
        if path.is_absolute():
            return path

        # Find project root by looking for key files
        current = Path.cwd()
        project_root = current

        # Look for project indicators
        for parent in [current] + list(current.parents):
            if any((parent / indicator).exists() for indicator in [
                'pyproject.toml', 'setup.py', '.git', 'spigamonde.db', 'spigamonde'
            ]):
                project_root = parent
                break

        return project_root / path

    def _get_performance_log_path(self) -> Optional[Path]:
        """Get performance log file path."""
        if self.settings.logging.file_path:
            base_path = self._resolve_log_path(self.settings.logging.file_path)
            return base_path.parent / f"{base_path.stem}_performance.log"
        return None

    def _get_error_log_path(self) -> Optional[Path]:
        """Get error log file path."""
        if self.settings.logging.file_path:
            base_path = self._resolve_log_path(self.settings.logging.file_path)
            return base_path.parent / f"{base_path.stem}_errors.log"
        return None

    def _add_correlation_id_filter(self, record):
        """Add correlation_id to record if missing."""
        if "correlation_id" not in record["extra"]:
            record["extra"]["correlation_id"] = correlation_id.get() or "N/A"
        return True
    
    def _format_structured_message(self, level: str, message: str, **kwargs) -> str:
        """Format message as structured JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "message": message,
            "correlation_id": correlation_id.get(),
            "session_id": session_id.get(),
            "spider_name": spider_name.get(),
            **kwargs
        }
        
        # Remove None values
        log_entry = {k: v for k, v in log_entry.items() if v is not None}
        
        return json.dumps(log_entry, default=str)
    
    def info(self, message: str, **kwargs):
        """Log info message with structured data."""
        if self.settings.logging.file_path:
            structured_msg = self._format_structured_message("INFO", message, **kwargs)
            logger.bind(correlation_id=correlation_id.get() or "N/A").info(structured_msg)
        else:
            logger.bind(correlation_id=correlation_id.get() or "N/A").info(message)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with structured data."""
        if self.settings.logging.file_path:
            structured_msg = self._format_structured_message("DEBUG", message, **kwargs)
            logger.bind(correlation_id=correlation_id.get() or "N/A").debug(structured_msg)
        else:
            logger.bind(correlation_id=correlation_id.get() or "N/A").debug(message)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with structured data."""
        if self.settings.logging.file_path:
            structured_msg = self._format_structured_message("WARNING", message, **kwargs)
            logger.bind(correlation_id=correlation_id.get() or "N/A").warning(structured_msg)
        else:
            logger.bind(correlation_id=correlation_id.get() or "N/A").warning(message)
    
    def error(self, message: str, **kwargs):
        """Log error message with structured data."""
        if self.settings.logging.file_path:
            structured_msg = self._format_structured_message("ERROR", message, **kwargs)
            logger.bind(correlation_id=correlation_id.get() or "N/A").error(structured_msg)
        else:
            logger.bind(correlation_id=correlation_id.get() or "N/A").error(message)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with structured data."""
        if self.settings.logging.file_path:
            structured_msg = self._format_structured_message("CRITICAL", message, **kwargs)
            logger.bind(correlation_id=correlation_id.get() or "N/A").critical(structured_msg)
        else:
            logger.bind(correlation_id=correlation_id.get() or "N/A").critical(message)


# Global structured logger instance
structured_logger = StructuredLogger()


@contextmanager
def log_context(correlation_id_val: str = None, session_id_val: str = None, spider_name_val: str = None):
    """Context manager for setting logging context variables."""
    # Generate correlation ID if not provided
    if correlation_id_val is None:
        correlation_id_val = str(uuid.uuid4())[:8]
    
    # Set context variables
    correlation_token = correlation_id.set(correlation_id_val)
    session_token = session_id.set(session_id_val) if session_id_val else None
    spider_token = spider_name.set(spider_name_val) if spider_name_val else None
    
    try:
        yield correlation_id_val
    finally:
        # Reset context variables
        correlation_id.reset(correlation_token)
        if session_token:
            session_id.reset(session_token)
        if spider_token:
            spider_name.reset(spider_token)


def log_performance(operation: str):
    """Decorator for logging performance metrics."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log performance metrics
                logger.bind(
                    log_type="performance",
                    operation=operation,
                    duration_seconds=duration,
                    status="success",
                    correlation_id=correlation_id.get() or "N/A"
                ).info(f"Performance: {operation} completed in {duration:.3f}s")
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Log performance metrics for failed operations
                logger.bind(
                    log_type="performance",
                    operation=operation,
                    duration_seconds=duration,
                    status="error",
                    error=str(e),
                    correlation_id=correlation_id.get() or "N/A"
                ).error(f"Performance: {operation} failed after {duration:.3f}s: {e}")
                
                raise
        
        return wrapper
    return decorator


def log_spider_activity(activity_type: str, **metadata):
    """Log spider activity with metadata."""
    structured_logger.info(
        f"Spider activity: {activity_type}",
        activity_type=activity_type,
        spider_name=spider_name.get(),
        **metadata
    )


def log_database_operation(operation: str, table: str, **metadata):
    """Log database operations with metadata."""
    structured_logger.debug(
        f"Database operation: {operation} on {table}",
        operation_type="database",
        operation=operation,
        table=table,
        **metadata
    )


def log_file_operation(operation: str, file_path: str, **metadata):
    """Log file operations with metadata."""
    structured_logger.debug(
        f"File operation: {operation} on {file_path}",
        operation_type="file",
        operation=operation,
        file_path=file_path,
        **metadata
    )


def get_logger() -> StructuredLogger:
    """Get the global structured logger instance."""
    return structured_logger


def setup_logging():
    """Setup enhanced logging system."""
    global structured_logger
    structured_logger = StructuredLogger()
    return structured_logger
