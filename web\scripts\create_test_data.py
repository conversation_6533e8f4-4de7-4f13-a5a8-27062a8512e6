#!/usr/bin/env python3
"""
Create Test Data Script
Description: Create sample content and analysis data for testing the web interface
Execution Time: ~5 seconds
Resource Usage: Minimal (database operations only)
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

def main():
    """Create test data for the web interface."""
    print("Creating test data for SpigaMonde...")
    
    try:
        # Add SpigaMonde to path
        project_root = Path(__file__).parent.parent.parent
        sys.path.insert(0, str(project_root))
        
        from spigamonde.database.connection import init_database, session_scope
        from spigamonde.models.content import (
            Content, ContentAnalysis, URL, CrawlSession,
            ContentCategory, QualityScore, ContentType, ContentStatus
        )
        import hashlib
        
        # Initialize database
        print("Initializing database...")
        init_database()
        
        with session_scope() as session:
            # Create a test crawl session
            crawl_session = CrawlSession(
                session_name="Test Web Interface Session",
                start_time=datetime.now() - timedelta(hours=1),
                end_time=datetime.now(),
                seed_urls='["https://example.com"]',
                max_depth=2,
                max_pages=10,
                urls_discovered=10,
                urls_crawled=8,
                content_found=5,
                content_downloaded=5,
                is_active=False,
                is_completed=True
            )
            session.add(crawl_session)
            session.flush()  # Get the ID
            
            # Create test URLs
            test_urls = [
                "https://example.com/document1.pdf",
                "https://example.com/article1.html",
                "https://example.com/image1.jpg",
                "https://example.com/document2.pdf",
                "https://example.com/article2.html"
            ]

            url_objects = []
            for url in test_urls:
                url_obj = URL(
                    url=url,
                    domain="example.com",
                    path=url.split("example.com")[1] if "example.com" in url else "/",
                    is_crawled=True,
                    last_crawled=datetime.now() - timedelta(minutes=30)
                )
                session.add(url_obj)
                url_objects.append(url_obj)
            
            session.flush()  # Get the URL IDs
            
            # Create test content items
            test_content = [
                {
                    "url": "https://example.com/document1.pdf",
                    "filename": "document1.pdf",
                    "content_type": ContentType.DOCUMENT,
                    "file_size": 1024000,  # 1MB
                    "status": ContentStatus.COMPLETED
                },
                {
                    "url": "https://example.com/article1.html",
                    "filename": "article1.html",
                    "content_type": ContentType.DOCUMENT,
                    "file_size": 50000,  # 50KB
                    "status": ContentStatus.COMPLETED
                },
                {
                    "url": "https://example.com/image1.jpg",
                    "filename": "image1.jpg",
                    "content_type": ContentType.IMAGE,
                    "file_size": 200000,  # 200KB
                    "status": ContentStatus.COMPLETED
                },
                {
                    "url": "https://example.com/document2.pdf",
                    "filename": "document2.pdf",
                    "content_type": ContentType.DOCUMENT,
                    "file_size": 2048000,  # 2MB
                    "status": ContentStatus.COMPLETED
                },
                {
                    "url": "https://example.com/article2.html",
                    "filename": "article2.html",
                    "content_type": ContentType.DOCUMENT,
                    "file_size": 75000,  # 75KB
                    "status": ContentStatus.FAILED
                }
            ]

            content_objects = []
            for i, content_data in enumerate(test_content):
                # Create URL hash for the content
                url_hash = hashlib.sha256(content_data["url"].encode()).hexdigest()

                content = Content(
                    url=content_data["url"],
                    filename=content_data["filename"],
                    content_type=content_data["content_type"],
                    file_size=content_data["file_size"],
                    status=content_data["status"],
                    source_url_id=url_objects[i].id,
                    url_hash=url_hash,
                    is_downloaded=(content_data["status"] == ContentStatus.COMPLETED),
                    created_at=datetime.now() - timedelta(minutes=20),
                    updated_at=datetime.now() - timedelta(minutes=10)
                )
                session.add(content)
                content_objects.append(content)
            
            session.flush()  # Get the content IDs
            
            # Create test analysis results for some content
            test_analyses = [
                {
                    "content_id": 0,  # Will be replaced with actual ID
                    "category": ContentCategory.TECHNICAL_DOCUMENTATION,
                    "confidence": 0.85,
                    "quality_score": QualityScore.EXCELLENT,
                    "word_count": 1500,
                    "language": "en"
                },
                {
                    "content_id": 1,
                    "category": ContentCategory.BLOG_POST, 
                    "confidence": 0.75,
                    "quality_score": QualityScore.GOOD,
                    "word_count": 800,
                    "language": "en"
                },
                {
                    "content_id": 3,
                    "category": ContentCategory.ACADEMIC_PAPER,
                    "confidence": 0.90,
                    "quality_score": QualityScore.EXCELLENT, 
                    "word_count": 3500,
                    "language": "en"
                }
            ]
            
            for analysis_data in test_analyses:
                content_obj = content_objects[analysis_data["content_id"]]
                analysis = ContentAnalysis(
                    content_id=content_obj.id,
                    category=analysis_data["category"],
                    confidence=analysis_data["confidence"],
                    quality_score=analysis_data["quality_score"],
                    word_count=analysis_data["word_count"],
                    language=analysis_data["language"],
                    title=f"Test {analysis_data['category'].value}",
                    keywords='["test", "sample", "data"]',  # JSON string
                    emails='[]',  # JSON string
                    urls='[]',    # JSON string
                    phone_numbers='[]',  # JSON string
                    dates='[]',   # JSON string
                    analysis_time=0.05,
                    created_at=datetime.now() - timedelta(minutes=5)
                )
                session.add(analysis)
            
            # Commit all changes
            session.commit()
            
            print("SUCCESS Test data created successfully!")
            print(f"Created:")
            print(f"  - 1 crawl session")
            print(f"  - {len(url_objects)} URLs")
            print(f"  - {len(content_objects)} content items")
            print(f"  - {len(test_analyses)} analysis results")
            
            return 0
            
    except Exception as e:
        print(f"ERROR Failed to create test data: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
