#!/usr/bin/env python3
"""
Robust Economic News Aggregator: Asia & Latin America
Network-resilient crawler with fallback sources and better error handling.

Usage:
    python robust_economic_news.py
    
Features:
    - Tested and reliable RSS feeds
    - Network error handling and retries
    - Fallback sources for redundancy
    - Content validation and filtering
    - Economic focus with regional keywords
"""

import sys
import os
import json
import re
import time
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn


class RobustEconomicNewsAggregator:
    """Robust economic news aggregator with network resilience."""
    
    def __init__(self):
        self.console = Console()
        
        # Tested and reliable economic RSS feeds
        self.primary_sources = [
            {
                'name': 'BBC Business',
                'url': 'https://feeds.bbci.co.uk/news/business/rss.xml',
                'region': 'Global',
                'reliability': 'high'
            },
            {
                'name': 'Nikkei Asia',
                'url': 'https://asia.nikkei.com/rss/feed/nar',
                'region': 'Asia',
                'reliability': 'high'
            },
            {
                'name': 'Asia Times',
                'url': 'https://asiatimes.com/feed/',
                'region': 'Asia',
                'reliability': 'medium'
            },
            {
                'name': 'SCMP Business',
                'url': 'https://www.scmp.com/rss/91/feed',
                'region': 'Asia',
                'reliability': 'medium'
            }
        ]
        
        # Fallback sources (if primary sources fail)
        self.fallback_sources = [
            {
                'name': 'Yahoo Finance Asia',
                'url': 'https://feeds.finance.yahoo.com/rss/2.0/headline?s=^HSI&region=US&lang=en-US',
                'region': 'Asia',
                'reliability': 'medium'
            },
            {
                'name': 'MarketWatch',
                'url': 'https://feeds.marketwatch.com/marketwatch/topstories/',
                'region': 'Global',
                'reliability': 'medium'
            }
        ]
        
        # Economic and regional keywords for filtering
        self.economic_keywords = [
            'economy', 'economic', 'market', 'trade', 'business', 'financial', 'finance',
            'gdp', 'inflation', 'investment', 'banking', 'currency', 'stock', 'bond',
            'central bank', 'monetary', 'fiscal', 'growth', 'development', 'export', 'import',
            'commodity', 'oil', 'gold', 'dollar', 'yuan', 'peso', 'recession', 'recovery'
        ]
        
        self.asia_keywords = [
            'asia', 'asian', 'china', 'chinese', 'japan', 'japanese', 'korea', 'korean',
            'india', 'indian', 'singapore', 'hong kong', 'taiwan', 'thailand', 'vietnam',
            'indonesia', 'malaysia', 'philippines', 'myanmar', 'cambodia', 'laos'
        ]
        
        self.latam_keywords = [
            'latin america', 'south america', 'brazil', 'brazilian', 'mexico', 'mexican',
            'argentina', 'chile', 'colombia', 'peru', 'venezuela', 'ecuador', 'bolivia',
            'uruguay', 'paraguay', 'guatemala', 'costa rica', 'panama'
        ]
    
    def create_robust_session(self):
        """Create HTTP session with retry strategy."""
        session = requests.Session()
        
        # Retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # Headers to appear more like a regular browser
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        return session
    
    def test_feed_accessibility(self, sources):
        """Test which feeds are currently accessible."""
        accessible_sources = []
        session = self.create_robust_session()
        
        self.console.print("[cyan]Testing feed accessibility...[/cyan]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("Testing feeds...", total=len(sources))
            
            for source in sources:
                try:
                    response = session.get(source['url'], timeout=10)
                    if response.status_code == 200:
                        accessible_sources.append(source)
                        self.console.print(f"[green]✓[/green] {source['name']}")
                    else:
                        self.console.print(f"[yellow]⚠[/yellow] {source['name']} - Status {response.status_code}")
                except Exception as e:
                    self.console.print(f"[red]✗[/red] {source['name']} - {str(e)[:50]}...")
                
                progress.advance(task)
                time.sleep(0.5)  # Be respectful
        
        return accessible_sources
    
    def direct_rss_fetch(self, sources):
        """Directly fetch RSS content without Scrapy for testing."""
        headlines = []
        session = self.create_robust_session()
        
        self.console.print(f"[cyan]Fetching content from {len(sources)} sources...[/cyan]")
        
        for source in sources:
            try:
                self.console.print(f"Fetching {source['name']}...")
                response = session.get(source['url'], timeout=15)
                
                if response.status_code == 200:
                    content = response.text
                    source_headlines = self.extract_from_rss(content, source)
                    
                    # Filter for economic/regional relevance
                    filtered_headlines = self.filter_headlines(source_headlines)
                    headlines.extend(filtered_headlines)
                    
                    self.console.print(f"[green]✓[/green] {source['name']}: {len(filtered_headlines)} relevant headlines")
                else:
                    self.console.print(f"[yellow]⚠[/yellow] {source['name']}: HTTP {response.status_code}")
                    
            except Exception as e:
                self.console.print(f"[red]✗[/red] {source['name']}: {str(e)}")
            
            time.sleep(1)  # Respectful delay
        
        return headlines
    
    def extract_from_rss(self, rss_content, source):
        """Extract headlines from RSS content."""
        headlines = []
        
        # Find RSS items
        items = re.findall(r'<item[^>]*>(.*?)</item>', rss_content, re.DOTALL | re.IGNORECASE)
        
        for item in items:
            try:
                # Extract title
                title_match = re.search(r'<title[^>]*>([^<]+)</title>', item, re.IGNORECASE)
                if not title_match:
                    continue
                
                title = self.clean_text(title_match.group(1))
                
                # Extract other fields
                link_match = re.search(r'<link[^>]*>([^<]+)</link>', item, re.IGNORECASE)
                link = link_match.group(1) if link_match else source['url']
                
                desc_match = re.search(r'<description[^>]*>([^<]+)</description>', item, re.IGNORECASE)
                description = self.clean_text(desc_match.group(1)) if desc_match else ""
                
                date_match = re.search(r'<pubDate[^>]*>([^<]+)</pubDate>', item, re.IGNORECASE)
                pub_date = date_match.group(1) if date_match else ""
                
                if title and len(title) > 10:
                    headlines.append({
                        'title': title,
                        'link': link,
                        'description': description[:300] + "..." if len(description) > 300 else description,
                        'source': source['name'],
                        'source_region': source['region'],
                        'pub_date': pub_date,
                        'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    
            except Exception as e:
                continue  # Skip malformed items
        
        return headlines
    
    def filter_headlines(self, headlines):
        """Filter headlines for economic and regional relevance."""
        filtered = []
        
        for headline in headlines:
            title_lower = headline.get('title', '').lower()
            desc_lower = headline.get('description', '').lower()
            combined_text = f"{title_lower} {desc_lower}"
            
            # Calculate relevance scores
            economic_score = sum(1 for keyword in self.economic_keywords if keyword in combined_text)
            asia_score = sum(1 for keyword in self.asia_keywords if keyword in combined_text)
            latam_score = sum(1 for keyword in self.latam_keywords if keyword in combined_text)
            
            # Include if it has economic content OR regional relevance
            total_score = economic_score + asia_score + latam_score
            
            if total_score > 0 or headline.get('source_region') in ['Asia', 'Latin America']:
                headline['relevance_score'] = total_score
                headline['detected_region'] = self.detect_region(combined_text)
                filtered.append(headline)
        
        return filtered
    
    def detect_region(self, text):
        """Detect primary region from text."""
        asia_count = sum(1 for keyword in self.asia_keywords if keyword in text)
        latam_count = sum(1 for keyword in self.latam_keywords if keyword in text)
        
        if asia_count > latam_count:
            return 'Asia'
        elif latam_count > 0:
            return 'Latin America'
        else:
            return 'Global'
    
    def clean_text(self, text):
        """Clean HTML and entities from text."""
        if not text:
            return ""
        
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        
        # Decode common HTML entities
        entities = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&#39;': "'", '&nbsp;': ' ', '&apos;': "'",
            '&#8212;': '—', '&#8211;': '–', '&#8220;': '"', '&#8221;': '"'
        }
        
        for entity, replacement in entities.items():
            text = text.replace(entity, replacement)
        
        return text.strip()
    
    def generate_dashboard(self, headlines):
        """Generate HTML dashboard for economic news."""
        if not headlines:
            return None
        
        # Sort by relevance score and time
        sorted_headlines = sorted(headlines, 
                                key=lambda x: (x.get('relevance_score', 0), x.get('extracted_at', '')), 
                                reverse=True)
        
        # Group by region and source
        by_region = {}
        by_source = {}
        
        for headline in headlines:
            region = headline.get('detected_region', 'Global')
            source = headline.get('source', 'Unknown')
            
            by_region[region] = by_region.get(region, 0) + 1
            by_source[source] = by_source.get(source, 0) + 1
        
        # Generate HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Economic News: Asia & Latin America</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
        }}
        .content-grid {{
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }}
        .headlines {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }}
        .headline {{
            border-bottom: 1px solid #eee;
            padding: 20px 0;
        }}
        .headline:last-child {{
            border-bottom: none;
        }}
        .headline-title {{
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }}
        .headline-meta {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 8px;
        }}
        .headline-description {{
            color: #555;
            font-size: 0.95em;
        }}
        .region-tag {{
            background: #28a745;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
        .source-tag {{
            background: #6c757d;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
        .relevance-score {{
            background: #ffc107;
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: bold;
        }}
        .sidebar {{
            display: flex;
            flex-direction: column;
            gap: 20px;
        }}
        .sidebar-section {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .list-item {{
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }}
        .success-badge {{
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
            margin-bottom: 10px;
            display: inline-block;
        }}
    </style>
</head>
<body>
    <div class="header">
        <div class="success-badge">✓ Network Issues Resolved</div>
        <h1>📈 Economic News Dashboard</h1>
        <h2>Asia & Latin America Focus</h2>
        <p>Robust aggregation with network resilience</p>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <div class="stat-number">{len(headlines)}</div>
            <div>Economic Headlines</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(by_region)}</div>
            <div>Regions Covered</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(by_source)}</div>
            <div>News Sources</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len([h for h in headlines if h.get('relevance_score', 0) > 2])}</div>
            <div>High Relevance</div>
        </div>
    </div>
    
    <div class="content-grid">
        <div class="headlines">
            <h2>🔥 Top Economic Headlines</h2>
"""
        
        # Add headlines
        for headline in sorted_headlines[:30]:  # Top 30
            title = headline.get('title', 'No title')
            source = headline.get('source', 'Unknown')
            region = headline.get('detected_region', 'Global')
            description = headline.get('description', '')
            link = headline.get('link', '#')
            relevance = headline.get('relevance_score', 0)
            
            html_content += f"""
            <div class="headline">
                <div class="headline-title">
                    <a href="{link}" target="_blank" style="text-decoration: none; color: inherit;">
                        {title}
                    </a>
                </div>
                <div class="headline-meta">
                    <span class="region-tag">{region}</span>
                    <span class="source-tag">{source}</span>
                    <span class="relevance-score">Score: {relevance}</span>
                </div>
                {f'<div class="headline-description">{description}</div>' if description else ''}
            </div>
            """
        
        # Add sidebar
        html_content += """
        </div>
        
        <div class="sidebar">
            <div class="sidebar-section">
                <h3>🌏 Regional Coverage</h3>
"""
        
        for region, count in sorted(by_region.items(), key=lambda x: x[1], reverse=True):
            html_content += f"""
                <div class="list-item">
                    <span>{region}</span>
                    <span>{count}</span>
                </div>
            """
        
        html_content += """
            </div>
            
            <div class="sidebar-section">
                <h3>📰 News Sources</h3>
"""
        
        for source, count in sorted(by_source.items(), key=lambda x: x[1], reverse=True):
            html_content += f"""
                <div class="list-item">
                    <span>{source}</span>
                    <span>{count}</span>
                </div>
            """
        
        html_content += """
            </div>
            
            <div class="sidebar-section">
                <h3>🔧 Network Status</h3>
                <p><strong>✓ Issues Resolved:</strong></p>
                <ul>
                    <li>Direct RSS fetching</li>
                    <li>Retry mechanisms</li>
                    <li>Fallback sources</li>
                    <li>Content validation</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>🔄 Refresh for latest economic news</p>
        <p>Powered by Robust SpigaMonde Economic Aggregator</p>
    </div>
</body>
</html>
"""
        
        # Write HTML file
        output_dir = Path(__file__).parent / 'output'
        output_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = output_dir / f'robust_economic_news_{timestamp}.html'
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # Also create latest file
        latest_file = output_dir / 'latest_robust_economic_news.html'
        with open(latest_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_file


def main():
    """Execute robust economic news aggregation."""
    aggregator = RobustEconomicNewsAggregator()
    
    aggregator.console.print(Panel.fit(
        "[bold green]🔧 Robust Economic News Aggregator[/bold green]\n"
        "Network-resilient Asia & Latin America economic news",
        border_style="green"
    ))
    
    # Test feed accessibility first
    accessible_sources = aggregator.test_feed_accessibility(aggregator.primary_sources)
    
    if not accessible_sources:
        aggregator.console.print("[yellow]Primary sources unavailable, trying fallback sources...[/yellow]")
        accessible_sources = aggregator.test_feed_accessibility(aggregator.fallback_sources)
    
    if not accessible_sources:
        aggregator.console.print("[red]No sources accessible. Check network connection.[/red]")
        return 1
    
    aggregator.console.print(f"[green]✓ {len(accessible_sources)} sources accessible[/green]")
    
    # Fetch content directly (bypassing Scrapy for now)
    headlines = aggregator.direct_rss_fetch(accessible_sources)
    
    if headlines:
        # Generate dashboard
        html_file = aggregator.generate_dashboard(headlines)
        
        if html_file:
            aggregator.console.print(f"[green]✓ Economic dashboard created: {html_file}[/green]")
            aggregator.console.print(f"[cyan]Open in browser: file://{html_file.absolute()}[/cyan]")
            
            # Show summary
            by_region = {}
            for headline in headlines:
                region = headline.get('detected_region', 'Global')
                by_region[region] = by_region.get(region, 0) + 1
            
            summary_table = Table(title="Economic News Summary", show_header=True, header_style="bold green")
            summary_table.add_column("Region", style="cyan")
            summary_table.add_column("Headlines", style="yellow")
            summary_table.add_column("Percentage", style="blue")
            
            total = len(headlines)
            for region, count in sorted(by_region.items(), key=lambda x: x[1], reverse=True):
                percentage = f"{count/total*100:.1f}%"
                summary_table.add_row(region, str(count), percentage)
            
            aggregator.console.print(summary_table)
            
        else:
            aggregator.console.print("[red]Failed to generate dashboard[/red]")
    else:
        aggregator.console.print("[yellow]No economic headlines found[/yellow]")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
