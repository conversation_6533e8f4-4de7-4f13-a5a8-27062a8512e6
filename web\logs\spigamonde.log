{"timestamp": "2025-08-28T00:10:52.307530", "level": "INFO", "message": "Starting SpigaMonde Web Interface backend"}
{"timestamp": "2025-08-28T00:10:52.308525", "level": "INFO", "message": "Changed working directory from I:\\SpigaMonde\\web to I:\\SpigaMonde"}
Database initialized with URL: sqlite:///spigamonde.db
Database tables created successfully
{"timestamp": "2025-08-28T00:10:52.338122", "level": "INFO", "message": "Database initialized successfully"}
{"timestamp": "2025-08-28T00:10:52.339128", "level": "INFO", "message": "SpigaMonde Web Interface backend started successfully"}
{"timestamp": "2025-08-28T00:11:29.414994", "level": "INFO", "message": "SpigaMonde stats endpoint called"}
{"timestamp": "2025-08-28T00:11:29.462990", "level": "INFO", "message": "Stats retrieved successfully - Total content: 1419, Analyzed: 17"}
{"timestamp": "2025-08-28T00:18:06.978405", "level": "INFO", "message": "Starting SpigaMonde Web Interface backend"}
{"timestamp": "2025-08-28T00:18:06.979405", "level": "INFO", "message": "Changed working directory from I:\\SpigaMonde\\web to I:\\SpigaMonde"}
Database initialized with URL: sqlite:///spigamonde.db
Database tables created successfully
{"timestamp": "2025-08-28T00:18:07.009435", "level": "INFO", "message": "Database initialized successfully"}
{"timestamp": "2025-08-28T00:18:07.009435", "level": "INFO", "message": "SpigaMonde Web Interface backend started successfully"}
{"timestamp": "2025-08-28T00:22:36.401911", "level": "INFO", "message": "Detailed stats requested for stats widget"}
{"timestamp": "2025-08-28T00:22:36.479906", "level": "INFO", "message": "Detailed stats generated - 1419 total content, 17 analyzed"}
{"timestamp": "2025-08-28T00:22:55.363720", "level": "INFO", "message": "SpigaMonde stats endpoint called"}
{"timestamp": "2025-08-28T00:22:55.369697", "level": "INFO", "message": "Stats retrieved successfully - Total content: 1419, Analyzed: 17"}
{"timestamp": "2025-08-28T00:25:34.627024", "level": "INFO", "message": "Starting SpigaMonde Web Interface backend"}
{"timestamp": "2025-08-28T00:25:34.628059", "level": "INFO", "message": "Changed working directory from I:\\SpigaMonde\\web to I:\\SpigaMonde"}
Database initialized with URL: sqlite:///spigamonde.db
Database tables created successfully
{"timestamp": "2025-08-28T00:25:34.659003", "level": "INFO", "message": "Database initialized successfully"}
{"timestamp": "2025-08-28T00:25:34.659003", "level": "INFO", "message": "SpigaMonde Web Interface backend started successfully"}
{"timestamp": "2025-08-28T00:26:46.435008", "level": "INFO", "message": "SpigaMonde stats endpoint called"}
{"timestamp": "2025-08-28T00:26:46.481990", "level": "INFO", "message": "Stats retrieved successfully - Total content: 1419, Analyzed: 17"}
{"timestamp": "2025-08-28T00:30:41.587634", "level": "INFO", "message": "Detailed stats requested for stats widget"}
{"timestamp": "2025-08-28T00:30:41.623635", "level": "INFO", "message": "Detailed stats generated - 1419 total content, 17 analyzed"}
{"timestamp": "2025-08-28T00:34:20.600008", "level": "INFO", "message": "Configuration mode check requested"}
{"timestamp": "2025-08-28T00:34:20.601007", "level": "INFO", "message": "Current mode: testing (TestCrawler/1.0)"}
{"timestamp": "2025-08-28T00:34:20.604012", "level": "INFO", "message": "Available scripts requested"}
{"timestamp": "2025-08-28T00:34:20.608015", "level": "INFO", "message": "Available scripts requested"}
{"timestamp": "2025-08-28T00:35:40.416611", "level": "INFO", "message": "Script execution requested"}
{"timestamp": "2025-08-28T00:35:40.417612", "level": "INFO", "message": "Executing standalone script: web_simple_news_check"}
{"timestamp": "2025-08-28T00:35:40.418612", "level": "INFO", "message": "Starting Web UI script: simple_news_check: uv run python I:\\SpigaMonde\\web\\scripts\\simple_news_check.py"}
{"timestamp": "2025-08-28T00:35:47.188867", "level": "INFO", "message": "Script execution completed successfully: exec_1756341340"}
{"timestamp": "2025-08-28T00:35:47.189850", "level": "INFO", "message": "Script output: Starting simple news check...\nInitializing SpigaMonde...\nChecking 3 news sources...\n   [1/3] Checking BBC News...\n      SUCCESS - 329487 chars, expected content: True\n   [2/3] Checking Reuters...\n      ERROR - HTTP 401\n   [3/3] Checking AP News...\n      SUCCESS - 1839080 chars, expected content: True\n\nNews check completed!\n   Successful: 2/3\n   Failed: 1/3\n\nDetailed results:\n   SUCCESS BBC News: 329487 chars\n   ERROR Reuters: HTTP 401\n   SUCCESS AP News: 1839080 chars\n\nSimple news check completed successfully!\n"}
{"timestamp": "2025-08-28T00:35:47.190851", "level": "WARNING", "message": "Script warnings: 2025-08-27 19:35:41.712 | INFO     | spigamonde.database.connection:initialize:63 - Database initialized with URL: sqlite:///spigamonde.db\n2025-08-27 19:35:41.716 | INFO     | spigamonde.database.connection:create_tables:71 - Database tables created successfully\n"}
{"timestamp": "2025-08-28T00:35:47.190851", "level": "INFO", "message": "Script execution completed: exec_1756341340 - Web UI script: simple_news_check"}
{"timestamp": "2025-08-28T01:14:32.894183", "level": "INFO", "message": "Starting SpigaMonde Web Interface backend"}
{"timestamp": "2025-08-28T01:14:32.895245", "level": "INFO", "message": "Changed working directory from I:\\SpigaMonde\\web to I:\\SpigaMonde"}
Database initialized with URL: sqlite:///spigamonde.db
Database tables created successfully
{"timestamp": "2025-08-28T01:14:32.925174", "level": "INFO", "message": "Database initialized successfully"}
{"timestamp": "2025-08-28T01:14:32.925174", "level": "INFO", "message": "SpigaMonde Web Interface backend started successfully"}
{"timestamp": "2025-08-28T01:15:45.904106", "level": "INFO", "message": "Starting SpigaMonde Web Interface backend"}
{"timestamp": "2025-08-28T01:15:45.905086", "level": "INFO", "message": "Changed working directory from I:\\SpigaMonde\\web to I:\\SpigaMonde"}
Database initialized with URL: sqlite:///spigamonde.db
Database tables created successfully
{"timestamp": "2025-08-28T01:15:45.935065", "level": "INFO", "message": "Database initialized successfully"}
{"timestamp": "2025-08-28T01:15:45.935065", "level": "INFO", "message": "SpigaMonde Web Interface backend started successfully"}
{"timestamp": "2025-08-28T01:16:00.079815", "level": "INFO", "message": "Configuration mode check requested"}
{"timestamp": "2025-08-28T01:16:00.080780", "level": "INFO", "message": "Current mode: testing (TestCrawler/1.0)"}
{"timestamp": "2025-08-28T01:16:00.082781", "level": "INFO", "message": "Available scripts requested"}
{"timestamp": "2025-08-28T01:16:00.087782", "level": "INFO", "message": "Available scripts requested"}
{"timestamp": "2025-08-28T01:16:53.278310", "level": "INFO", "message": "Script execution requested"}
{"timestamp": "2025-08-28T01:16:53.279197", "level": "INFO", "message": "Executing standalone script: web_simple_news_check"}
{"timestamp": "2025-08-28T01:16:53.279197", "level": "INFO", "message": "Starting Web UI script: simple_news_check: uv run python I:\\SpigaMonde\\web\\scripts\\simple_news_check.py"}
{"timestamp": "2025-08-28T01:16:59.880738", "level": "INFO", "message": "Script execution completed successfully: exec_1756343813"}
{"timestamp": "2025-08-28T01:16:59.881740", "level": "INFO", "message": "Script output: Starting simple news check...\nInitializing SpigaMonde...\nChecking 3 news sources...\n   [1/3] Checking BBC News...\n      SUCCESS - 329562 chars, expected content: True\n   [2/3] Checking Reuters...\n      ERROR - HTTP 401\n   [3/3] Checking AP News...\n      SUCCESS - 1837799 chars, expected content: True\n\nNews check completed!\n   Successful: 2/3\n   Failed: 1/3\n\nDetailed results:\n   SUCCESS BBC News: 329562 chars\n   ERROR Reuters: HTTP 401\n   SUCCESS AP News: 1837799 chars\n\nSimple news check completed successfully!\n"}
{"timestamp": "2025-08-28T01:16:59.882753", "level": "WARNING", "message": "Script warnings: 2025-08-27 20:16:54.565 | INFO     | spigamonde.database.connection:initialize:63 - Database initialized with URL: sqlite:///spigamonde.db\n2025-08-27 20:16:54.568 | INFO     | spigamonde.database.connection:create_tables:71 - Database tables created successfully\n"}
{"timestamp": "2025-08-28T01:16:59.882753", "level": "INFO", "message": "Script execution completed: exec_1756343813 - Web UI script: simple_news_check"}
{"timestamp": "2025-08-28T01:17:27.640748", "level": "INFO", "message": "Detailed stats requested for stats widget"}
{"timestamp": "2025-08-28T01:17:27.715797", "level": "INFO", "message": "Detailed stats generated - 1419 total content, 17 analyzed"}
