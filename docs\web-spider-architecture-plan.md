# Web Spider Architecture Plan

## Executive Summary

This document outlines the recommended architecture for a web spider application using Python's Scrapy library. The plan addresses your key questions about CLI vs GUI, database backend, AI/LLM integration, and package recommendations.

## Architecture Recommendations

### 1. Development Approach: CLI-First Strategy ✅

**Recommendation**: Start with a robust command-line application, then add GUI later.

**Rationale**:
- Faster initial development and testing
- Better for automation and scripting
- Easier debugging and logging
- Can be containerized and deployed easily
- GUI can be built on top of solid CLI foundation

**Figure 1: Development Progression**

```mermaid
graph LR
    A[CLI Core] --> B[Web API Layer]
    B --> C[GUI Frontend]
    A --> D[Automation Scripts]
    
    style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style B fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style C fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style D fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
    
    linkStyle 0 stroke:#00BFFF,stroke-width:2px
    linkStyle 1 stroke:#39FF14,stroke-width:2px
    linkStyle 2 stroke:#FFA500,stroke-width:2px
```

### 2. GUI Technology: Web-Based Interface ✅

**Recommendation**: Use **FastAPI + React/Vue** or **Streamlit** for GUI.

**Options Comparison**:

| Technology | Pros | Cons | Best For |
|------------|------|------|----------|
| **Streamlit** | Rapid prototyping, Python-native | Limited customization | Quick dashboards |
| **FastAPI + React** | Full control, modern, scalable | More complex setup | Production apps |
| **Flask + Bootstrap** | Simple, lightweight | Less modern | Simple interfaces |
| **Tkinter** | Built-in, desktop native | Outdated look | Internal tools |

**Figure 2: GUI Architecture Options**

```mermaid
graph TD
    A[Spider Core] --> B{GUI Choice}
    B -->|Rapid Prototype| C[Streamlit]
    B -->|Production Ready| D[FastAPI + React]
    B -->|Simple Web| E[Flask + Bootstrap]
    B -->|Desktop| F[Tkinter/PyQt]
    
    style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style B fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style C fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style D fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style E fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
    style F fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
    
    linkStyle 0 stroke:#00BFFF,stroke-width:2px
    linkStyle 1 stroke:#39FF14,stroke-width:2px
    linkStyle 2 stroke:#39FF14,stroke-width:2px
    linkStyle 3 stroke:#FFA500,stroke-width:2px
    linkStyle 4 stroke:#FFA500,stroke-width:2px
```

### 3. Database Strategy: SQLite → PostgreSQL Migration Path ✅

**Recommendation**: Start with SQLite, design for easy PostgreSQL migration.

**Migration Strategy**:
1. **Phase 1**: SQLite for development and small-scale usage
2. **Phase 2**: PostgreSQL for production and larger datasets
3. **Phase 3**: Consider specialized databases (Elasticsearch for search, Redis for caching)

**Figure 3: Database Evolution**

```mermaid
graph LR
    A[SQLite Development] --> B[SQLite + Alembic Migrations]
    B --> C[PostgreSQL Production]
    C --> D[Elasticsearch Search Index]
    C --> E[Redis Cache Layer]
    
    style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style B fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style C fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style D fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style E fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    
    linkStyle 0,1,2 stroke:#00BFFF,stroke-width:2px
    linkStyle 3,4 stroke:#FFA500,stroke-width:2px
```

### 4. AI/LLM Integration: Modular Design ✅

**Recommendation**: Design with AI integration in mind from the start.

**AI Use Cases**:
- Content classification and tagging
- Duplicate detection
- Content summarization
- Quality scoring
- Language detection
- Entity extraction

**Figure 4: AI Integration Architecture**

```mermaid
graph TD
    A[Scraped Content] --> B[Content Processor]
    B --> C{AI Processing}
    C -->|Classification| D[Content Tagger]
    C -->|Analysis| E[Quality Scorer]
    C -->|Extraction| F[Entity Extractor]
    C -->|Summary| G[Content Summarizer]
    
    D --> H[(Database)]
    E --> H
    F --> H
    G --> H
    
    style A fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style B fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style C fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style D fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style E fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style F fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style G fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style H fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
    
    linkStyle 0,1 stroke:#00BFFF,stroke-width:2px
    linkStyle 2,3,4,5 stroke:#39FF14,stroke-width:2px
    linkStyle 6,7,8,9 stroke:#FFA500,stroke-width:2px
```

## Recommended Package Stack

### Core Spider Framework
```python
# Web scraping and crawling
scrapy = "^2.11.0"
scrapy-playwright = "^0.0.34"  # For JavaScript-heavy sites
scrapy-splash = "^0.8.0"       # Alternative JS rendering
requests = "^2.31.0"           # For simple HTTP requests
beautifulsoup4 = "^4.12.0"     # HTML parsing
lxml = "^4.9.0"                # Fast XML/HTML parser
```

### Database and ORM
```python
# Database
sqlalchemy = "^2.0.0"         # ORM
alembic = "^1.12.0"           # Database migrations
psycopg2-binary = "^2.9.0"    # PostgreSQL adapter
sqlite3 = "built-in"          # Development database

# Caching
redis = "^5.0.0"              # Caching and queuing
```

### CLI and Configuration
```python
# Command line interface
click = "^8.1.0"              # CLI framework
rich = "^13.7.0"              # Beautiful terminal output
typer = "^0.9.0"              # Alternative modern CLI
pydantic = "^2.5.0"           # Configuration validation
python-dotenv = "^1.0.0"      # Environment variables
```

### Web API and GUI
```python
# Web framework
fastapi = "^0.104.0"          # Modern async web framework
uvicorn = "^0.24.0"           # ASGI server
streamlit = "^1.28.0"         # Quick dashboard option

# Frontend (if using FastAPI)
jinja2 = "^3.1.0"            # Template engine
```

### AI/LLM Integration
```python
# AI and ML
openai = "^1.3.0"            # OpenAI API
anthropic = "^0.7.0"         # Claude API
transformers = "^4.35.0"     # Hugging Face models
sentence-transformers = "^2.2.0"  # Embeddings
langchain = "^0.0.350"       # LLM framework
```

### Utilities and Monitoring
```python
# Logging and monitoring
loguru = "^0.7.0"            # Better logging
prometheus-client = "^0.19.0" # Metrics
sentry-sdk = "^1.38.0"       # Error tracking

# File handling
python-magic = "^0.4.0"     # File type detection
pillow = "^10.1.0"          # Image processing
pypdf = "^3.17.0"           # PDF processing
```

## System Architecture Overview

**Figure 5: Complete System Architecture**

```mermaid
graph TB
    subgraph "User Interface Layer"
        CLI[CLI Interface]
        WEB[Web Dashboard]
        API[REST API]
    end
    
    subgraph "Application Layer"
        CORE[Spider Core Engine]
        SCHED[Task Scheduler]
        PROC[Content Processor]
    end
    
    subgraph "AI/ML Layer"
        CLASS[Content Classifier]
        SUMM[Summarizer]
        ENTITY[Entity Extractor]
    end
    
    subgraph "Data Layer"
        DB[(Primary Database)]
        CACHE[(Redis Cache)]
        FILES[File Storage]
    end
    
    subgraph "External Services"
        SITES[Target Websites]
        LLMAPI[LLM APIs]
    end
    
    CLI --> CORE
    WEB --> API
    API --> CORE
    CORE --> SCHED
    CORE --> PROC
    PROC --> CLASS
    PROC --> SUMM
    PROC --> ENTITY
    CLASS --> LLMAPI
    SUMM --> LLMAPI
    ENTITY --> LLMAPI
    CORE --> DB
    CORE --> CACHE
    PROC --> FILES
    SCHED --> SITES
    
    style CLI fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style WEB fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style API fill:#3498DB,stroke:#333,stroke-width:2px,color:#FFFFFF
    style CORE fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style SCHED fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style PROC fill:#2ECC71,stroke:#333,stroke-width:2px,color:#000000
    style CLASS fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style SUMM fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style ENTITY fill:#F39C12,stroke:#333,stroke-width:2px,color:#000000
    style DB fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
    style CACHE fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
    style FILES fill:#95A5A6,stroke:#333,stroke-width:2px,color:#000000
```

## Implementation Phases

### Phase 1: Core CLI Spider (Weeks 1-2)
- [ ] Basic Scrapy spider setup
- [ ] SQLite database with SQLAlchemy
- [ ] CLI interface with Click
- [ ] Basic file type detection
- [ ] Configuration management

### Phase 2: Enhanced Features (Weeks 3-4)
- [ ] JavaScript rendering (Playwright)
- [ ] Content deduplication
- [ ] Advanced filtering and search
- [ ] Export functionality (JSON, CSV, XML)
- [ ] Basic logging and monitoring

### Phase 3: AI Integration (Weeks 5-6) ✅ COMPLETE
- [x] Content classification (22 categories with confidence scoring)
- [x] Entity extraction (emails, URLs, phones, dates)
- [x] Quality scoring (5-level assessment with detailed metrics)
- [x] Advanced metadata extraction (title, author, language, keywords)
- [x] Performance monitoring integration
- [x] Database storage and relationships
- [ ] Duplicate detection using embeddings (deferred to Phase 4)

### Phase 4: Web Interface (Weeks 7-8)

#### **Tech Stack**
- **Backend**: FastAPI (async, auto-docs, WebSocket support)
- **Frontend**: React + TypeScript + Vite
- **Styling**: Tailwind CSS + Headless UI
- **Real-time**: WebSockets for live updates
- **State**: React Query + Zustand
- **Testing**: Jest + React Testing Library + Playwright

#### **Week 7: Backend API Foundation**

**Day 1-2: FastAPI Setup & Basic Endpoints**
- [ ] FastAPI project structure (`web/backend/`)
- [ ] Database integration with existing SpigaMonde models
- [ ] Basic CRUD endpoints for content and analysis
- [ ] Auto-generated API documentation
- [ ] CORS configuration for development
- **Test**: API endpoints with Swagger UI

**Day 3-4: Core API Endpoints**
- [ ] `/api/content` - List, filter, search content
- [ ] `/api/analysis` - Content analysis results
- [ ] `/api/crawl-sessions` - Crawl session management
- [ ] `/api/stats` - Dashboard statistics
- [ ] `/api/sources` - News sources and RSS feeds
- **Test**: All endpoints with sample data

**Day 5-7: Real-time Features**
- [ ] WebSocket endpoint for live crawl updates
- [ ] Server-Sent Events for notifications
- [ ] Background task integration with existing crawlers
- [ ] Real-time metrics streaming
- **Test**: WebSocket connections and live data flow

#### **Week 8: Frontend Development**

**Day 1-2: React Setup & Basic Layout**
- [ ] Vite + React + TypeScript project (`web/frontend/`)
- [ ] Tailwind CSS configuration
- [ ] Basic layout components (Header, Sidebar, Main)
- [ ] Routing setup (React Router)
- [ ] API client setup (Axios + React Query)
- **Test**: Basic navigation and layout responsiveness

**Day 3-4: Core Dashboard Components**
- [ ] Content list view with pagination
- [ ] Search and filter components
- [ ] Content detail modal/page
- [ ] Analysis results display
- [ ] Statistics cards and charts
- **Test**: Data fetching and display with mock API

**Day 5-6: Real-time Features**
- [ ] WebSocket integration for live updates
- [ ] Real-time crawl progress indicators
- [ ] Live notification system
- [ ] Auto-refreshing data components
- **Test**: Real-time updates during actual crawls

**Day 7: Integration & Polish**
- [ ] Connect frontend to FastAPI backend
- [ ] Error handling and loading states
- [ ] Responsive design refinements
- [ ] Basic authentication (if needed)
- **Test**: End-to-end functionality with real data

#### **Detailed Component Architecture**

**Backend Structure (`web/backend/`)**
```
web/backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI app setup
│   ├── config.py            # Configuration settings
│   ├── database.py          # Database connection
│   └── api/
│       ├── __init__.py
│       ├── content.py       # Content endpoints
│       ├── analysis.py      # Analysis endpoints
│       ├── crawl.py         # Crawl management
│       ├── stats.py         # Statistics endpoints
│       └── websocket.py     # Real-time endpoints
├── models/                  # Pydantic models
├── services/                # Business logic
├── tests/                   # API tests
└── requirements.txt
```

**Frontend Structure (`web/frontend/`)**
```
web/frontend/
├── src/
│   ├── components/
│   │   ├── layout/          # Header, Sidebar, Layout
│   │   ├── content/         # Content list, detail, search
│   │   ├── dashboard/       # Stats, charts, metrics
│   │   ├── crawl/           # Crawl management UI
│   │   └── common/          # Shared components
│   ├── pages/               # Route components
│   ├── hooks/               # Custom React hooks
│   ├── services/            # API client
│   ├── store/               # State management
│   ├── types/               # TypeScript types
│   └── utils/               # Helper functions
├── public/
├── tests/
└── package.json
```

#### **Incremental Testing Strategy**

**Backend Testing (Each Day)**
1. **Unit Tests**: Test individual endpoints with pytest
2. **Integration Tests**: Test database operations
3. **API Tests**: Test with real SpigaMonde data
4. **Performance Tests**: Load testing with multiple requests

**Frontend Testing (Each Day)**
1. **Component Tests**: Test individual components with Jest
2. **Integration Tests**: Test component interactions
3. **E2E Tests**: Test user workflows with Playwright
4. **Visual Tests**: Test responsive design

**Daily Testing Checkpoints**
- [ ] All new endpoints return expected data
- [ ] Frontend components render without errors
- [ ] Real-time features work with live data
- [ ] No performance regressions
- [ ] Mobile responsiveness maintained

#### **Key Features to Implement**

**Dashboard Features**
- [ ] Content overview with statistics
- [ ] Recent crawl activity timeline
- [ ] Top sources and categories
- [ ] Search and filter interface
- [ ] Content quality metrics

**Real-time Features**
- [ ] Live crawl progress bars
- [ ] Real-time content analysis updates
- [ ] Notification system for completed crawls
- [ ] Live statistics updates
- [ ] WebSocket connection status indicator

**Content Management**
- [ ] Browse all crawled content
- [ ] Advanced search with filters
- [ ] Content detail views with analysis
- [ ] Export functionality (CSV, JSON)
- [ ] Content tagging and categorization

**Crawl Management**
- [ ] Start new crawl jobs from UI
- [ ] Monitor active crawls
- [ ] Crawl history and logs
- [ ] Schedule recurring crawls
- [ ] Crawl configuration interface

#### **Development & Deployment Setup**

**Development Environment**
```bash
# Backend development
cd web/backend
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
uvicorn app.main:app --reload --port 8000

# Frontend development
cd web/frontend
npm install
npm run dev  # Runs on port 3000
```

**Production Deployment**
- [ ] Docker containers for backend and frontend
- [ ] Nginx reverse proxy configuration
- [ ] Environment-based configuration
- [ ] Health check endpoints
- [ ] Logging and monitoring integration

#### **Success Criteria**

**Week 7 (Backend) Success Metrics**
- [ ] All API endpoints return data in <200ms
- [ ] WebSocket connections handle 100+ concurrent users
- [ ] API documentation is complete and accurate
- [ ] 95%+ test coverage for critical endpoints
- [ ] Integration with existing SpigaMonde data works flawlessly

**Week 8 (Frontend) Success Metrics**
- [ ] Dashboard loads in <2 seconds
- [ ] Real-time updates appear within 1 second
- [ ] Mobile responsive on all major devices
- [ ] Accessible (WCAG 2.1 AA compliance)
- [ ] No console errors in production build

**Overall Phase 4 Success**
- [ ] Complete web interface for SpigaMonde
- [ ] Real-time monitoring of crawl operations
- [ ] User-friendly content browsing and search
- [ ] Ability to start and monitor crawls from web UI
- [ ] Production-ready deployment configuration

#### **Risk Mitigation**

**Technical Risks**
- **WebSocket complexity**: Start with simple polling, upgrade to WebSockets
- **Performance issues**: Implement pagination and lazy loading early
- **State management**: Keep state simple initially, add complexity as needed

**Timeline Risks**
- **Scope creep**: Focus on core features first, add enhancements later
- **Integration issues**: Test with real data frequently
- **Learning curve**: Use familiar technologies where possible

#### **Next Steps After Phase 4**
- [ ] User authentication and authorization
- [ ] Advanced analytics and reporting
- [ ] API rate limiting and security
- [ ] Advanced crawl scheduling
- [ ] Multi-user support and permissions

### Phase 5: Production Ready (Weeks 9-10)
- [ ] PostgreSQL migration
- [ ] Redis caching
- [ ] Docker containerization
- [ ] Performance optimization
- [ ] Comprehensive testing

## Next Steps

1. **Initialize Project Structure**: Set up the basic Python project with recommended packages
2. **Create Core Spider**: Implement basic Scrapy spider with file type detection
3. **Database Schema**: Design and implement the initial database schema
4. **CLI Interface**: Build the command-line interface for spider control
5. **Testing Framework**: Set up unit and integration tests

Would you like me to help you implement any specific phase or component of this architecture?
