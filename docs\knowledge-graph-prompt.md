# SpigaMonde Knowledge Graph Generation Prompt

**Purpose**: Generate a comprehensive knowledge graph for SpigaMonde using MCP memory server  
**Target**: LLM with access to MCP memory server capabilities  
**Output**: Structured knowledge graph representing all SpigaMonde components, relationships, and concepts

## LLM Prompt for Knowledge Graph Generation

```
You are tasked with creating a comprehensive knowledge graph for SpigaMonde, a production-ready web crawling platform with intelligent content analysis capabilities. Use the MCP memory server to store and organize this knowledge graph.

### CONTEXT
SpigaMonde is a sophisticated web crawling platform that includes:
- Core web crawling and content discovery
- Real-time monitoring and metrics dashboard
- Intelligent content analysis and classification
- Database persistence and management
- CLI interface and configuration system
- Production-ready deployment capabilities

### TASK
Create a structured knowledge graph by storing entities, relationships, and concepts in the MCP memory server. Focus on:

1. **CORE COMPONENTS** - Main system modules and their purposes
2. **RELATIONSHIPS** - How components interact and depend on each other
3. **FEATURES** - Specific capabilities and functionalities
4. **CONCEPTS** - Important ideas, patterns, and architectural decisions
5. **WORKFLOWS** - Key processes and data flows
6. **CONFIGURATION** - Settings, parameters, and customization options

### KNOWLEDGE GRAPH STRUCTURE

#### 1. ENTITIES TO STORE
Store each entity with relevant attributes and metadata:

**System Components:**
- Spider System (content_spider.py, link extraction, robots.txt compliance)
- Content Analysis Engine (classification, quality assessment, metadata extraction)
- Database Layer (SQLite, content storage, analysis results)
- Monitoring System (real-time dashboard, metrics collection, performance tracking)
- CLI Interface (commands, parameters, user interaction)
- Configuration System (settings management, environment variables)

**Content Types:**
- Web Pages (HTML content, text extraction)
- Documents (PDFs, academic papers, technical docs)
- Images (PNG, JPG, SVG files)
- Archives (compressed files, data archives)
- Data Files (structured data, configuration files)

**Analysis Categories:**
- Academic Papers (research content, citations)
- Technical Documentation (tutorials, guides, API docs)
- Blog Posts (informal content, opinions)
- Legal Documents (terms, policies, contracts)
- News Articles (journalism, current events)
- Low Quality Content (spam, irrelevant content)

**Quality Levels:**
- Excellent (high-value, well-structured content)
- Good (useful content with minor issues)
- Fair (acceptable content with some problems)
- Poor (low-value content with significant issues)

#### 2. RELATIONSHIPS TO MAP
Store relationships between entities:

**Component Dependencies:**
- Spider → Database (stores crawled content)
- Spider → Content Analysis (triggers analysis pipeline)
- Content Analysis → Database (stores analysis results)
- Monitoring → Database (reads metrics and statistics)
- CLI → All Components (orchestrates system operations)

**Data Flow Relationships:**
- URL Discovery → Content Download → Content Analysis → Database Storage
- Real-time Metrics → Dashboard Display → User Monitoring
- Configuration → System Behavior → Performance Outcomes

**Feature Relationships:**
- Custom Download Directories → File Organization → Content Storage
- Depth Limiting → Crawl Scope → Resource Management
- Delay Settings → Respectful Crawling → Server Load Management

#### 3. CONCEPTS TO CAPTURE
Store important architectural and design concepts:

**Design Patterns:**
- Modular Architecture (separation of concerns, extensibility)
- Observer Pattern (monitoring and metrics collection)
- Pipeline Pattern (content processing workflow)
- Configuration Pattern (flexible system behavior)

**Performance Concepts:**
- Concurrent Requests (parallel processing)
- Rate Limiting (respectful crawling)
- Memory Efficiency (resource optimization)
- Analysis Speed (2-5ms per item target)

**Production Concepts:**
- Error Handling (graceful degradation)
- Database Persistence (reliable storage)
- Real-time Monitoring (operational visibility)
- Configuration Flexibility (deployment adaptability)

#### 4. WORKFLOWS TO DOCUMENT
Store key process flows:

**Crawling Workflow:**
1. URL Input → 2. Robots.txt Check → 3. Content Download → 4. Link Extraction → 5. Content Analysis → 6. Database Storage

**Analysis Workflow:**
1. Content Reception → 2. Text Extraction → 3. Classification → 4. Quality Assessment → 5. Metadata Extraction → 6. Result Storage

**Monitoring Workflow:**
1. Metrics Collection → 2. Real-time Aggregation → 3. Dashboard Display → 4. Performance Tracking

### MEMORY STORAGE FORMAT
For each entity, store using this structure:

```
Entity: [COMPONENT_NAME]
Type: [system_component|content_type|analysis_category|workflow|concept]
Description: [Brief description of purpose and functionality]
Attributes: [Key properties, capabilities, or characteristics]
Relationships: [Connected entities and relationship types]
Location: [File paths, modules, or system locations]
Status: [production_ready|in_development|planned]
Dependencies: [Required components or prerequisites]
Examples: [Concrete examples or use cases]
```

### SPECIFIC INSTRUCTIONS

#### CHUNKING STRATEGY (IMPORTANT)
**Use modest-sized chunks to avoid tool use errors:**

- **Store 3-5 entities per memory operation** - Don't try to store too many nodes at once
- **Process one component category at a time** - e.g., first all system components, then content types, then workflows
- **Use incremental building** - Start with core entities, then add relationships in separate operations
- **Verify each chunk** - Confirm successful storage before proceeding to next chunk
- **If errors occur** - Reduce chunk size and retry with smaller batches

#### PROCESSING ORDER

1. **START WITH CORE COMPONENTS** (3-5 entities per batch):
   - Batch 1: Spider System, Content Analysis Engine
   - Batch 2: Database Layer, Monitoring System
   - Batch 3: CLI Interface, Configuration System

2. **ADD CONTENT TYPES** (3-4 entities per batch):
   - Batch 1: Web Pages, Documents, Images
   - Batch 2: Archives, Data Files

3. **STORE ANALYSIS CATEGORIES** (3-4 entities per batch):
   - Batch 1: Academic Papers, Technical Documentation, Blog Posts
   - Batch 2: Legal Documents, News Articles, Low Quality Content

4. **MAP RELATIONSHIPS** (focus on one component's relationships per batch):
   - Batch 1: Spider dependencies and connections
   - Batch 2: Analysis engine relationships
   - Batch 3: Database connections
   - Batch 4: Monitoring system relationships

5. **CAPTURE WORKFLOWS** (one workflow per batch):
   - Batch 1: Crawling workflow
   - Batch 2: Analysis workflow
   - Batch 3: Monitoring workflow

6. **DOCUMENT CONCEPTS** (2-3 concepts per batch):
   - Batch 1: Design patterns
   - Batch 2: Performance concepts
   - Batch 3: Production concepts

#### ADDITIONAL GUIDELINES

- **INCLUDE CONFIGURATION**: Document all configurable aspects and their impacts
- **ADD EXAMPLES**: Include concrete examples from our test runs (Wikipedia crawl, Python docs crawl)
- **MARK STATUS**: Indicate which features are production-ready vs. planned enhancements
- **CROSS-REFERENCE**: Ensure entities reference related entities for graph connectivity
- **VALIDATE INCREMENTALLY**: Test queries after each major batch to ensure connectivity

### EXPECTED OUTCOME
A comprehensive knowledge graph stored in MCP memory that:
- Maps all SpigaMonde components and their relationships
- Documents key workflows and data flows
- Captures architectural decisions and design patterns
- Provides a foundation for documentation generation
- Enables intelligent querying about system capabilities
- Supports future development planning

### VALIDATION
After creating the knowledge graph, verify by querying:
- "What components are involved in content analysis?"
- "How does the monitoring system work?"
- "What are the dependencies of the spider system?"
- "What content types can SpigaMonde handle?"
- "What configuration options are available?"

The knowledge graph should provide comprehensive, accurate answers to these and similar questions about SpigaMonde's architecture and capabilities.
```

## Usage Instructions

1. **Provide this prompt** to an LLM with MCP memory server access
2. **Include SpigaMonde documentation** as context (project files, dev log, test results)
3. **Review the generated knowledge graph** for completeness and accuracy
4. **Query the knowledge graph** to validate relationships and information
5. **Use for documentation generation** and architectural planning

## Expected Benefits

- **Comprehensive System Map**: Visual representation of all components and relationships
- **Documentation Foundation**: Structured knowledge for generating user guides and technical docs
- **Development Planning**: Clear understanding of dependencies for future enhancements
- **Onboarding Tool**: Help new developers understand the system architecture
- **Query Interface**: Intelligent answers about system capabilities and design decisions

This knowledge graph will serve as the foundation for enhanced documentation and system understanding!
