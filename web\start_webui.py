#!/usr/bin/env python3
"""
SpigaMonde Web UI Startup Script
Starts the FastAPI backend server for the web interface
Location: web/start_webui.py
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Start the SpigaMonde Web UI backend server"""
    
    # Get the web directory (current script's parent directory)
    web_dir = Path(__file__).parent.absolute()
    project_root = web_dir.parent

    # Ensure we're in the project root for proper UV environment
    os.chdir(project_root)
    
    print("Starting SpigaMonde Web UI...")
    print(f"Project root: {project_root}")
    print(f"Web directory: {web_dir}")
    
    # Backend script path
    backend_script = web_dir / "backend" / "main.py"
    
    if not backend_script.exists():
        print(f"Error: Backend script not found at {backend_script}")
        sys.exit(1)
    
    try:
        # Use UV to run uvicorn with proper environment isolation
        # Following the "Critical Learnings" requirement
        cmd = [
            "uv", "run", "uvicorn",
            "backend.main:app",
            "--reload",
            "--host", "127.0.0.1",
            "--port", "8000"
        ]

        print(f"Executing: {' '.join(cmd)}")
        print("Web UI backend will be available at http://localhost:8000")
        print("API documentation: http://localhost:8000/docs")
        print("Start frontend separately with: cd web/frontend && uv run python -m http.server 3000")
        print("Press Ctrl+C to stop the server")
        print("-" * 50)

        # Change to web directory for uvicorn to find backend.main
        # (but UV will use the main project environment)
        os.chdir(web_dir)

        # Run the backend server
        subprocess.run(cmd, check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"Error starting Web UI: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nWeb UI server stopped")
        sys.exit(0)
    except FileNotFoundError:
        print("Error: UV not found. Please ensure UV is installed.")
        print("Install with: pip install uv")
        sys.exit(1)

if __name__ == "__main__":
    main()