"""Scrapy settings for SpigaMonde project."""

from .config.settings import get_settings

# Get application settings
app_settings = get_settings()

# Scrapy settings
BOT_NAME = 'spigamonde'

SPIDER_MODULES = ['spigamonde.spiders']
NEWSPIDER_MODULE = 'spigamonde.spiders'

# Obey robots.txt rules
ROBOTSTXT_OBEY = app_settings.spider.robotstxt_obey

# Configure delays and concurrency
CONCURRENT_REQUESTS = app_settings.spider.concurrent_requests
DOWNLOAD_DELAY = app_settings.spider.download_delay
RANDOMIZE_DOWNLOAD_DELAY = app_settings.spider.randomize_download_delay

# User agent
USER_AGENT = app_settings.spider.user_agent

# Configure pipelines
ITEM_PIPELINES = {
    # Add custom pipelines here if needed
}

# Configure extensions
EXTENSIONS = {
    # Add custom extensions here if needed
}

# AutoThrottle extension
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 1
AUTOTHROTTLE_MAX_DELAY = 60
AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
AUTOTHROTTLE_DEBUG = False

# Memory usage monitoring
MEMUSAGE_ENABLED = True
MEMUSAGE_LIMIT_MB = 2048
MEMUSAGE_WARNING_MB = 1024

# Configure caching (optional)
HTTPCACHE_ENABLED = False
HTTPCACHE_EXPIRATION_SECS = 3600
HTTPCACHE_DIR = 'httpcache'

# Request fingerprinting
REQUEST_FINGERPRINTER_IMPLEMENTATION = '2.7'

# Twisted reactor
TWISTED_REACTOR = 'twisted.internet.asyncioreactor.AsyncioSelectorReactor'

# Feed exports (for data export)
FEEDS = {
    # Can be configured dynamically
}

# Logging
LOG_LEVEL = app_settings.logging.level
