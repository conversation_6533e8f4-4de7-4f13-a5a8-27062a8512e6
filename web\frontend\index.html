<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpigaMonde Web Interface</title>
    <link rel="stylesheet" href="style.css?v=17">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <div class="header-main">
                    <h1>🕷️ SpigaMonde Web Interface</h1>
                    <p>Web Management Dashboard</p>
                </div>
                <div class="header-status">
                    <button id="modeToggleBtn" class="mode-toggle-button testing">
                        <span class="mode-icon">🧪</span>
                        <span class="mode-text">Testing Mode</span>
                        <span class="toggle-hint">Click to switch</span>
                    </button>
                </div>
            </div>
        </header>

        <div class="status-bar">
            <div class="status-item">
                <span class="status-label">Backend Status:</span>
                <span id="backendStatus" class="status-value">Checking...</span>
            </div>
        </div>

        <!-- Tab Navigation -->
        <nav class="tab-navigation">
            <button class="tab-button active" data-tab="dashboard">
                <span class="tab-icon">🏠</span>
                <span class="tab-label">Dashboard</span>
            </button>
            <button class="tab-button" data-tab="analytics">
                <span class="tab-icon">📊</span>
                <span class="tab-label">Analytics</span>
            </button>
            <button class="tab-button" data-tab="scripts">
                <span class="tab-icon">📜</span>
                <span class="tab-label">Scripts</span>
            </button>
            <button class="tab-button" data-tab="system">
                <span class="tab-icon">🔧</span>
                <span class="tab-label">System</span>
            </button>
            <button class="tab-button" data-tab="logs">
                <span class="tab-icon">📝</span>
                <span class="tab-label">Logs</span>
            </button>
        </nav>

        <!-- Tab Content -->
        <main class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard-tab" class="tab-content active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h2>📈 Quick Stats</h2>
                        <p>Current system overview</p>
                        <button id="getStatsBtn" class="test-button">
                            <span class="button-text">Get Statistics</span>
                            <span class="button-spinner" style="display: none;">🔄</span>
                        </button>
                        <div id="statsResult" class="result-box" style="display: none;">
                            <h3>Statistics Result:</h3>
                            <pre id="statsData"></pre>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <h2>🏠 Dashboard Overview</h2>
                        <p>Welcome to the SpigaMonde Web Interface. Use the tabs above to navigate between different sections:</p>
                        <ul style="text-align: left; margin: 15px 0; padding-left: 20px;">
                            <li><strong>Analytics:</strong> View detailed statistics and metrics</li>
                            <li><strong>System:</strong> Run tests and manage system settings</li>
                            <li><strong>Logs:</strong> Monitor activity and system events</li>
                        </ul>
                    </div>

                    <div class="dashboard-card">
                        <h2>⚡ Quick Actions</h2>
                        <p>Common tasks and shortcuts for system management.</p>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 15px;">
                            <button onclick="switchTab('analytics')" class="test-button" style="flex: 1; min-width: 120px;">
                                <span class="button-text">📊 View Analytics</span>
                            </button>
                            <button onclick="switchTab('system')" class="test-button" style="flex: 1; min-width: 120px;">
                                <span class="button-text">🔧 System Tests</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div id="analytics-tab" class="tab-content">
                <div class="analytics-container">
                    <div class="analytics-header">
                        <h2>📊 Statistics Dashboard</h2>
                        <div class="analytics-controls">
                            <button id="refreshStatsBtn" class="test-button stats-button">
                                <span class="button-text">Refresh Stats</span>
                                <span class="button-spinner" style="display: none;">🔄</span>
                            </button>
                            <div class="auto-refresh-control">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="autoRefreshStats">
                                    <span class="checkmark"></span>
                                    Auto-refresh every 30s
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="statsWidget" class="stats-display" style="display: none;">
                        <!-- Overview Cards -->
                        <div class="stats-overview">
                            <div class="stat-card">
                                <div class="stat-icon">📄</div>
                                <div class="stat-content">
                                    <div class="stat-number" id="totalContent">0</div>
                                    <div class="stat-label">Total Content</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">🔍</div>
                                <div class="stat-content">
                                    <div class="stat-number" id="analyzedContent">0</div>
                                    <div class="stat-label">Analyzed</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">💾</div>
                                <div class="stat-content">
                                    <div class="stat-number" id="totalStorage">0 MB</div>
                                    <div class="stat-label">Storage Used</div>
                                </div>
                            </div>
                            
                            <div class="stat-card">
                                <div class="stat-icon">📈</div>
                                <div class="stat-content">
                                    <div class="stat-number" id="analysisProgress">0%</div>
                                    <div class="stat-label">Analysis Coverage</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Detailed Stats -->
                        <div class="stats-details">
                            <div class="stats-section">
                                <h3>📅 Recent Activity</h3>
                                <div id="recentActivity" class="activity-stats"></div>
                            </div>
                            
                            <div class="stats-section">
                                <h3>📊 Content Breakdown</h3>
                                <div id="contentBreakdown" class="breakdown-stats"></div>
                            </div>
                            
                            <div class="stats-section">
                                <h3>🌐 Top Sources</h3>
                                <div id="topSources" class="sources-stats"></div>
                            </div>
                            
                            <div class="stats-section">
                                <h3>📋 Recent Content</h3>
                                <div id="recentContent" class="recent-content-list"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="statsError" class="result-box error" style="display: none;">
                        <h3>Error Loading Stats:</h3>
                        <pre id="statsErrorMessage"></pre>
                    </div>
                </div>
            </div>

            <!-- Scripts Tab -->
            <div id="scripts-tab" class="tab-content">
                <div class="scripts-container">
                    <!-- Script Mode Toggle -->
                    <div class="script-mode-toggle">
                        <h2>📜 Script Execution</h2>
                        <div class="mode-selector">
                            <button id="commandModeBtn" class="mode-button active" data-mode="command">
                                <span class="mode-icon">🔧</span>
                                <span class="mode-label">Command Mode</span>
                                <small>URL-based crawl commands</small>
                            </button>
                            <button id="scriptModeBtn" class="mode-button" data-mode="script">
                                <span class="mode-icon">🐍</span>
                                <span class="mode-label">Script Mode</span>
                                <small>Standalone Python scripts</small>
                            </button>
                        </div>
                    </div>

                    <!-- Command Mode Section -->
                    <div id="commandModeSection" class="scripts-section mode-section">
                        <h3>🔧 Command Mode</h3>
                        <p>Execute SpigaMonde CLI commands and URL-based crawl scripts.</p>

                        <div class="script-controls">
                            <div class="script-selector">
                                <label for="scriptSelect">Select Script:</label>
                                <select id="scriptSelect" class="script-dropdown">
                                    <option value="">Choose a script...</option>
                                    <option value="basic-crawl">Basic Web Crawl</option>
                                    <option value="deep-crawl">Deep Content Analysis</option>
                                    <option value="site-map">Site Mapping</option>
                                    <option value="custom">Custom Script</option>
                                </select>
                            </div>

                            <div class="script-params" id="scriptParams" style="display: none;">
                                <h3>Script Parameters</h3>
                                <div class="param-grid">
                                    <div class="param-group">
                                        <label for="targetUrl">Target URL:</label>
                                        <input type="url" id="targetUrl" placeholder="https://example.com" class="param-input">
                                    </div>
                                    <div class="param-group">
                                        <label for="maxDepth">Max Depth:</label>
                                        <input type="number" id="maxDepth" value="3" min="1" max="10" class="param-input">
                                    </div>
                                    <div class="param-group">
                                        <label for="maxPages">Max Pages:</label>
                                        <input type="number" id="maxPages" value="50" min="1" max="1000" class="param-input">
                                    </div>
                                    <div class="param-group">
                                        <label for="crawlDelay">Delay (seconds):</label>
                                        <input type="number" id="crawlDelay" value="1" min="0.1" max="10" step="0.1" class="param-input">
                                    </div>
                                </div>

                                <div class="script-options">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="followExternal" checked>
                                        <span class="checkmark"></span>
                                        Follow external links
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="downloadMedia">
                                        <span class="checkmark"></span>
                                        Download media files
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="analyzeContent" checked>
                                        <span class="checkmark"></span>
                                        Analyze content
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="script-actions">
                            <button id="executeScriptBtn" class="test-button script-execute-button" disabled>
                                <span class="button-text">🚀 Execute Script</span>
                                <span class="button-spinner" style="display: none;">🔄</span>
                            </button>
                            <button id="stopScriptBtn" class="test-button script-stop-button" disabled>
                                <span class="button-text">⏹️ Stop Script</span>
                            </button>
                            <button id="saveConfigBtn" class="test-button script-save-button" disabled>
                                <span class="button-text">💾 Save Config</span>
                            </button>
                        </div>
                    </div>

                    <!-- Script Mode Section -->
                    <div id="scriptModeSection" class="scripts-section mode-section" style="display: none;">
                        <h3>🐍 Script Mode</h3>
                        <p>Execute standalone Python scripts that don't require URL parameters.</p>

                        <div class="script-controls">
                            <div class="script-selector">
                                <label for="standaloneScriptSelect">Select Python Script:</label>
                                <select id="standaloneScriptSelect" class="script-dropdown">
                                    <option value="">Choose a script...</option>
                                </select>
                            </div>

                            <div class="script-description" id="scriptDescription" style="display: none;">
                                <h4>Script Information</h4>
                                <div id="scriptInfo" class="script-info">
                                    <p><strong>Description:</strong> <span id="scriptDescText">-</span></p>
                                    <p><strong>Execution Time:</strong> <span id="scriptTimeText">-</span></p>
                                    <p><strong>Type:</strong> <span id="scriptTypeText">-</span></p>
                                </div>
                            </div>

                            <div class="script-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="verboseOutput" checked>
                                    <span class="checkmark"></span>
                                    Verbose output
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="saveResults" checked>
                                    <span class="checkmark"></span>
                                    Save results to database
                                </label>
                            </div>
                        </div>

                        <div class="script-actions">
                            <button id="executeStandaloneBtn" class="test-button script-execute-button" disabled>
                                <span class="button-text">▶️ Execute Script</span>
                                <span class="button-spinner" style="display: none;">🔄</span>
                            </button>
                            <button id="stopStandaloneBtn" class="test-button script-stop-button" disabled>
                                <span class="button-text">⏹️ Stop Script</span>
                            </button>
                        </div>
                    </div>

                    <!-- Script Execution Section -->
                    <div class="scripts-section">
                        <h2>📊 Execution Status</h2>
                        <div id="scriptStatus" class="script-status">
                            <div class="status-indicator">
                                <span class="status-icon">⏸️</span>
                                <span class="status-text">Ready to execute</span>
                            </div>
                        </div>

                        <div id="scriptProgress" class="script-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill"></div>
                                <span class="progress-text" id="progressText">0%</span>
                            </div>
                            <div class="progress-details">
                                <span id="progressDetails">Preparing to start...</span>
                            </div>
                        </div>

                        <div id="scriptResults" class="result-box script-results" style="display: none;">
                            <h3>Execution Results:</h3>
                            <div id="scriptSummaryWidget" class="script-summary-widget"></div>
                            <details class="script-details">
                                <summary>View Detailed Output</summary>
                                <pre id="scriptOutput"></pre>
                            </details>
                        </div>
                    </div>

                    <!-- Script History Section -->
                    <div class="scripts-section">
                        <h2>📋 Recent Executions</h2>
                        <div id="scriptHistory" class="script-history">
                            <div class="history-item">
                                <span class="history-time">No recent executions</span>
                                <span class="history-script">-</span>
                                <span class="history-status">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Tab -->
            <div id="system-tab" class="tab-content">
                <div class="system-container">
                    <!-- System Tests Section -->
                    <div class="system-section">
                        <h2>🧪 System Tests</h2>
                        <p>Run diagnostic tests to verify system functionality and connectivity.</p>

                        <div class="dashboard-grid" style="margin-bottom: 30px;">
                            <div class="dashboard-card">
                                <h3>🔗 Connection Test</h3>
                                <p>Test the connection between the web interface and SpigaMonde backend.</p>
                                <button id="testConnectionBtn" class="test-button">
                                    <span class="button-text">Test Connection</span>
                                    <span class="button-spinner" style="display: none;">🔄</span>
                                </button>
                                <div id="connectionResult" class="result-box" style="display: none;">
                                    <h4>Connection Result:</h4>
                                    <pre id="connectionData"></pre>
                                </div>
                            </div>

                            <div class="dashboard-card">
                                <h3>🚀 Comprehensive Test</h3>
                                <p>Run comprehensive system tests to verify SpigaMonde functionality.</p>
                                <button id="runScriptBtn" class="test-button script-button">
                                    <span class="button-text">Run Test Script</span>
                                    <span class="button-spinner" style="display: none;">🔄</span>
                                </button>
                                <div id="scriptResult" class="result-box script-result" style="display: none;">
                                    <h4>Test Results Summary:</h4>
                                    <div id="scriptSummary" class="script-summary"></div>
                                    <details class="script-details">
                                        <summary>View Detailed Results</summary>
                                        <pre id="scriptData"></pre>
                                    </details>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Reset Section -->
                    <div class="system-section">
                        <h2>🔄 System Reset</h2>
                        <p>Clear old crawl data and reset system for fresh testing.</p>
                        
                        <div class="reset-controls">
                            <button id="resetSystemBtn" class="test-button reset-button">
                                <span class="button-text">Reset System</span>
                                <span class="button-spinner" style="display: none;">🔄</span>
                            </button>
                            
                            <div class="reset-options">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="clearDatabase" checked>
                                    <span class="checkmark"></span>
                                    Clear database content
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="clearDownloads" checked>
                                    <span class="checkmark"></span>
                                    Clear download directories
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="clearLogs">
                                    <span class="checkmark"></span>
                                    Clear log files
                                </label>
                            </div>
                        </div>
                        
                        <div id="resetResult" class="result-box reset-result" style="display: none;">
                            <h3>Reset Results:</h3>
                            <div id="resetSummary" class="reset-summary"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs Tab -->
            <div id="logs-tab" class="tab-content">
                <div class="logs-container">
                    <div class="logs-header">
                        <h2>📝 Activity Log</h2>
                        <div class="log-controls">
                            <button id="clearLogBtn" class="test-button clear-button">
                                <span class="button-text">Clear Log</span>
                            </button>
                        </div>
                    </div>
                    
                    <div id="activityLog" class="activity-log">
                        <!-- Log entries will be populated here -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript - Simple Working Version -->
    <script src="app-simple.js?v=17"></script>
</body>
</html>
