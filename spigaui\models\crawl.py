"""
Pydantic models for crawl jobs and configurations.

Defines the data structures used for crawl job management.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import BaseModel, HttpUrl, Field


class JobStatus(str, Enum):
    """Crawl job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CrawlConfig(BaseModel):
    """Configuration for a crawl job."""
    max_depth: int = Field(default=3, ge=1, le=10, description="Maximum crawl depth")
    max_pages: int = Field(default=100, ge=1, le=10000, description="Maximum pages to crawl")
    delay: float = Field(default=1.0, ge=0.1, le=10.0, description="Delay between requests in seconds")
    follow_external: bool = Field(default=False, description="Follow external links")
    respect_robots: bool = Field(default=True, description="Respect robots.txt")
    user_agent: Optional[str] = Field(default=None, description="Custom user agent")
    headers: Dict[str, str] = Field(default_factory=dict, description="Custom headers")
    cookies: Dict[str, str] = Field(default_factory=dict, description="Custom cookies")
    timeout: int = Field(default=30, ge=5, le=300, description="Request timeout in seconds")
    retries: int = Field(default=3, ge=0, le=10, description="Number of retries for failed requests")


class CrawlJobCreate(BaseModel):
    """Model for creating a new crawl job."""
    id: str = Field(description="Unique job identifier")
    url: HttpUrl = Field(description="Starting URL for the crawl")
    config: CrawlConfig = Field(description="Crawl configuration")
    name: Optional[str] = Field(default=None, description="Optional job name")
    description: Optional[str] = Field(default=None, description="Optional job description")


class CrawlProgress(BaseModel):
    """Crawl job progress information."""
    pages_crawled: int = Field(default=0, description="Number of pages crawled")
    pages_queued: int = Field(default=0, description="Number of pages in queue")
    pages_failed: int = Field(default=0, description="Number of failed pages")
    bytes_downloaded: int = Field(default=0, description="Total bytes downloaded")
    duration_seconds: float = Field(default=0.0, description="Crawl duration in seconds")
    current_url: Optional[str] = Field(default=None, description="Currently processing URL")
    percentage: float = Field(default=0.0, ge=0.0, le=100.0, description="Completion percentage")


class CrawlResult(BaseModel):
    """Individual crawl result."""
    url: str = Field(description="Crawled URL")
    status_code: int = Field(description="HTTP status code")
    title: Optional[str] = Field(default=None, description="Page title")
    content_length: int = Field(default=0, description="Content length in bytes")
    content_type: Optional[str] = Field(default=None, description="Content type")
    crawled_at: datetime = Field(description="When the page was crawled")
    links_found: int = Field(default=0, description="Number of links found on page")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")


class CrawlJobResponse(BaseModel):
    """Complete crawl job response model."""
    id: str = Field(description="Job identifier")
    url: str = Field(description="Starting URL")
    name: Optional[str] = Field(default=None, description="Job name")
    description: Optional[str] = Field(default=None, description="Job description")
    status: JobStatus = Field(description="Current job status")
    config: CrawlConfig = Field(description="Job configuration")
    progress: CrawlProgress = Field(description="Current progress")
    results: List[CrawlResult] = Field(default_factory=list, description="Crawl results")
    created_at: datetime = Field(description="When the job was created")
    started_at: Optional[datetime] = Field(default=None, description="When the job started")
    completed_at: Optional[datetime] = Field(default=None, description="When the job completed")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class JobEvent(BaseModel):
    """Real-time job event model."""
    type: str = Field(description="Event type")
    job_id: str = Field(description="Job identifier")
    timestamp: datetime = Field(description="Event timestamp")
    data: Dict[str, Any] = Field(description="Event data")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
