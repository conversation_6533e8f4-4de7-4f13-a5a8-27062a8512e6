#!/usr/bin/env python3
"""Demo script showcasing the enhanced content analysis capabilities."""

import asyncio
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from spigamonde.analysis.analyzer import get_content_analyzer
from spigamonde.analysis.classifier import ContentCategory, QualityScore


def main():
    """Demonstrate content analysis capabilities."""
    console = Console()
    analyzer = get_content_analyzer()
    
    console.print(Panel.fit(
        "[bold blue]SpigaMonde Phase 3: Enhanced Content Analysis Demo[/bold blue]",
        border_style="blue"
    ))
    
    # Sample content for analysis
    test_cases = [
        {
            "name": "Academic Paper",
            "url": "https://arxiv.org/pdf/2023.12345.pdf",
            "content": """
            Abstract: This paper presents a comprehensive study of neural networks in artificial intelligence.
            
            1. Introduction
            Machine learning has revolutionized the field of artificial intelligence through deep learning algorithms.
            The methodology involves statistical analysis and pattern recognition techniques.
            
            2. Results
            Our experiments demonstrate significant improvements in accuracy and performance metrics.
            The findings contribute to the advancement of computer science research.
            
            3. Conclusion
            This work advances the state of the art in AI research and provides a foundation for future studies.
            
            References:
            [1] <PERSON>, <PERSON><PERSON> (2023). Journal of Machine Learning Research.
            [2] <PERSON><PERSON>, <PERSON><PERSON> (2022). Proceedings of Neural Information Processing Systems.
            """,
            "file_path": "/papers/neural_networks.pdf",
            "mime_type": "application/pdf"
        },
        {
            "name": "News Article",
            "url": "https://www.bbc.com/news/technology-12345",
            "content": """
            <html>
            <head>
                <title>AI Breakthrough: New Neural Network Achieves Human-Level Performance</title>
                <meta name="description" content="Scientists announce major breakthrough in artificial intelligence">
                <meta name="author" content="Tech Reporter">
            </head>
            <body>
                <h1>Major AI Breakthrough Announced</h1>
                <p>Scientists at leading research institutions have announced a significant breakthrough 
                in artificial intelligence technology. The new neural network system demonstrates 
                human-level performance across multiple cognitive tasks.</p>
                
                <p>According to sources familiar with the research, the system represents a major 
                advancement in machine learning capabilities. The findings were published in a 
                peer-reviewed journal this week.</p>
                
                <p>Contact the <NAME_EMAIL> for more information.</p>
            </body>
            </html>
            """,
            "file_path": "/articles/ai_breakthrough.html",
            "mime_type": "text/html"
        },
        {
            "name": "Technical Documentation",
            "url": "https://docs.python.org/3/library/asyncio.html",
            "content": """
            # asyncio — Asynchronous I/O
            
            ## Overview
            asyncio is a library to write concurrent code using the async/await syntax.
            
            ## API Reference
            
            ### Event Loop
            The event loop is the core of every asyncio application. Event loops run asynchronous tasks.
            
            ### Coroutines
            Coroutines are functions that can be paused and resumed. They are declared with async def.
            
            ### Example Usage
            ```python
            import asyncio
            
            async def main():
                print('Hello')
                await asyncio.sleep(1)
                print('World')
            
            asyncio.run(main())
            ```
            
            For more information, see the official documentation at https://docs.python.org
            """,
            "file_path": "/docs/asyncio.md",
            "mime_type": "text/markdown"
        },
        {
            "name": "Low Quality Content",
            "url": "https://spam-site.com/offers",
            "content": """
            AMAZING DEALS!!! Click here now for limited time offers!
            
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Buy now and save 90%! Don't miss out on this incredible opportunity!
            
            This page is under construction. Please check back later.
            
            Click here! Buy now! Limited time! Amazing deals!
            """,
            "file_path": "/spam/offers.html",
            "mime_type": "text/html"
        }
    ]
    
    # Analyze each test case
    for i, test_case in enumerate(test_cases, 1):
        console.print(f"\n[bold cyan]Test Case {i}: {test_case['name']}[/bold cyan]")
        console.print(f"[dim]URL: {test_case['url']}[/dim]")
        
        # Perform analysis
        result = analyzer.analyze_content(
            url=test_case['url'],
            content=test_case['content'],
            file_path=test_case['file_path'],
            mime_type=test_case['mime_type']
        )
        
        # Create results table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        # Classification results
        table.add_row("Category", result.classification.category.value.replace('_', ' ').title())
        table.add_row("Confidence", f"{result.classification.confidence:.2f}")
        table.add_row("Quality Score", f"{result.classification.quality_score.value}/5 ({result.classification.quality_score.name})")
        
        # Content metadata
        table.add_row("Word Count", str(result.metadata.word_count))
        table.add_row("Character Count", str(result.metadata.character_count))
        table.add_row("Language", result.metadata.language or "Unknown")
        
        if result.metadata.title:
            table.add_row("Title", result.metadata.title[:50] + "..." if len(result.metadata.title) > 50 else result.metadata.title)
        
        if result.metadata.author:
            table.add_row("Author", result.metadata.author)
        
        # Quality metrics
        if result.metadata.readability_score:
            table.add_row("Readability", f"{result.metadata.readability_score:.1f}/100")
        
        if result.metadata.complexity_score:
            table.add_row("Complexity", f"{result.metadata.complexity_score:.1f}/100")
        
        # Extracted entities
        if result.metadata.emails:
            table.add_row("Emails Found", str(len(result.metadata.emails)))
        
        if result.metadata.urls:
            table.add_row("URLs Found", str(len(result.metadata.urls)))
        
        if result.metadata.keywords:
            top_keywords = result.metadata.keywords[:5]
            table.add_row("Top Keywords", ", ".join(top_keywords))
        
        # Performance
        table.add_row("Analysis Time", f"{result.analysis_time:.3f}s")
        table.add_row("Success", "✅" if result.success else "❌")
        
        console.print(table)
        
        # Show reasoning
        if result.classification.reasoning:
            reasoning_text = Text("Classification Reasoning: ", style="bold yellow")
            reasoning_text.append("; ".join(result.classification.reasoning), style="italic")
            console.print(reasoning_text)
    
    # Summary
    console.print(Panel.fit(
        "[bold green]Content Analysis Demo Complete![/bold green]\n\n"
        "✨ Features Demonstrated:\n"
        "• Smart content classification (22 categories)\n"
        "• Quality scoring (1-5 scale)\n"
        "• Metadata extraction (title, author, language)\n"
        "• Entity extraction (emails, URLs, dates)\n"
        "• Readability and complexity analysis\n"
        "• Keyword extraction\n"
        "• Performance monitoring\n"
        "• Multi-format support (PDF, HTML, Markdown)",
        border_style="green"
    ))


if __name__ == "__main__":
    main()
