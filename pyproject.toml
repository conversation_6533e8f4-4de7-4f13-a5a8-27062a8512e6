[project]
name = "spigamonde"
version = "0.1.0"
description = "A powerful web spider for searching and cataloging various file types and content"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # Core spider framework
    "scrapy>=2.11.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    # Database and ORM
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    # CLI and configuration
    "click>=8.1.0",
    "rich>=13.7.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    # Logging and utilities
    "loguru>=0.7.0",
    "python-magic>=0.4.0",
    # File handling
    "pillow>=10.1.0",
    "uvicorn>=0.35.0",
    "fastapi>=0.116.1",
    "python-multipart>=0.0.20",
    "httpx>=0.28.1",
]

[project.scripts]
spiga = "spigamonde.cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[dependency-groups]
dev = [
    "black>=25.1.0",
    "isort>=6.0.1",
    "mypy>=1.17.1",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "pytest-cov>=6.2.1",
    "pytest-mock>=3.14.1",
]
