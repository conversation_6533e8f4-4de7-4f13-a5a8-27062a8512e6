#!/usr/bin/env python3
"""
Manual Logging Crawl Script
Demonstrates how to capture SpigaMonde logs to files manually.

Usage:
    python manual_logging_crawl.py
"""

import sys
import os
import subprocess
from pathlib import Path
from datetime import datetime

def setup_log_directory():
    """Create logs directory structure."""
    log_dir = Path('./logs')
    log_dir.mkdir(exist_ok=True)
    return log_dir

def run_crawl_with_logging(url, log_dir):
    """Run crawl with manual log capture."""
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Log file paths
    main_log = log_dir / f'crawl_{timestamp}.log'
    error_log = log_dir / f'crawl_errors_{timestamp}.log'
    
    print(f"Starting crawl with logging...")
    print(f"Main log: {main_log}")
    print(f"Error log: {error_log}")
    
    # Run crawl command with log capture
    cmd = [
        sys.executable, '-m', 'spigamonde.cli',
        'crawl', url,
        '--max-pages', '5',
        '--delay', '1.0'
    ]
    
    try:
        # Capture both stdout and stderr
        with open(main_log, 'w') as main_f, open(error_log, 'w') as error_f:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Combine stderr with stdout
                universal_newlines=True,
                bufsize=1
            )
            
            # Real-time output and logging
            for line in process.stdout:
                print(line.strip())  # Show in console
                main_f.write(line)   # Write to log file
                main_f.flush()       # Ensure immediate write
                
                # Also write errors to separate file
                if 'ERROR' in line or 'CRITICAL' in line:
                    error_f.write(line)
                    error_f.flush()
            
            process.wait()
            return_code = process.returncode
        
        print(f"\nCrawl completed with return code: {return_code}")
        
        # Show log file info
        if main_log.exists():
            size = main_log.stat().st_size
            print(f"Main log file: {main_log} ({size} bytes)")
            
            # Show last few lines
            with open(main_log, 'r') as f:
                lines = f.readlines()
                print(f"Last 3 lines of log:")
                for line in lines[-3:]:
                    print(f"  {line.strip()}")
        
        if error_log.exists() and error_log.stat().st_size > 0:
            print(f"Error log file: {error_log}")
        else:
            print("No errors logged")
            
        return main_log, error_log
        
    except Exception as e:
        print(f"Error running crawl: {e}")
        return None, None

def main():
    """Execute manual logging crawl."""
    
    # Setup
    log_dir = setup_log_directory()
    
    # Test URL
    test_url = "https://httpbin.org/html"  # Simple test page
    
    print("SpigaMonde Manual Logging Demo")
    print("=" * 40)
    
    # Run crawl with logging
    main_log, error_log = run_crawl_with_logging(test_url, log_dir)
    
    if main_log:
        print(f"\n✅ Logs captured successfully!")
        print(f"📄 Main log: {main_log}")
        print(f"🚨 Error log: {error_log}")
        print(f"\nTo view logs:")
        print(f"  cat {main_log}")
        print(f"  tail -f {main_log}")
    else:
        print("❌ Failed to capture logs")

if __name__ == "__main__":
    main()
