"""Performance metrics and monitoring system."""

import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from threading import Lock
from typing import Dict, List, Optional, Any
from statistics import mean, median

from .logger import get_logger, log_performance


@dataclass
class MetricPoint:
    """A single metric data point."""
    timestamp: datetime
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class PerformanceStats:
    """Performance statistics for an operation."""
    operation: str
    count: int = 0
    total_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    avg_duration: float = 0.0
    median_duration: float = 0.0
    success_count: int = 0
    error_count: int = 0
    last_execution: Optional[datetime] = None
    recent_durations: deque = field(default_factory=lambda: deque(maxlen=100))


class MetricsCollector:
    """Collects and manages performance metrics."""
    
    def __init__(self):
        self.logger = get_logger()
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._performance_stats: Dict[str, PerformanceStats] = {}
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = {}
        self._lock = Lock()
    
    def record_metric(self, name: str, value: float, labels: Dict[str, str] = None):
        """Record a metric value."""
        with self._lock:
            metric_point = MetricPoint(
                timestamp=datetime.utcnow(),
                value=value,
                labels=labels or {}
            )
            self._metrics[name].append(metric_point)
    
    def increment_counter(self, name: str, value: int = 1, labels: Dict[str, str] = None):
        """Increment a counter metric."""
        with self._lock:
            key = self._make_metric_key(name, labels)
            self._counters[key] += value

            # Also record as a metric point (avoid deadlock by creating metric point directly)
            metric_point = MetricPoint(
                timestamp=datetime.utcnow(),
                value=float(self._counters[key]),
                labels=labels or {}
            )
            self._metrics[name].append(metric_point)
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """Set a gauge metric value."""
        with self._lock:
            key = self._make_metric_key(name, labels)
            self._gauges[key] = value

            # Also record as a metric point (avoid deadlock by creating metric point directly)
            metric_point = MetricPoint(
                timestamp=datetime.utcnow(),
                value=value,
                labels=labels or {}
            )
            self._metrics[name].append(metric_point)
    
    def record_performance(self, operation: str, duration: float, success: bool = True):
        """Record performance metrics for an operation."""
        with self._lock:
            if operation not in self._performance_stats:
                self._performance_stats[operation] = PerformanceStats(operation=operation)
            
            stats = self._performance_stats[operation]
            stats.count += 1
            stats.total_duration += duration
            stats.min_duration = min(stats.min_duration, duration)
            stats.max_duration = max(stats.max_duration, duration)
            stats.avg_duration = stats.total_duration / stats.count
            stats.last_execution = datetime.utcnow()
            stats.recent_durations.append(duration)
            
            if success:
                stats.success_count += 1
            else:
                stats.error_count += 1
            
            # Calculate median from recent durations
            if stats.recent_durations:
                stats.median_duration = median(list(stats.recent_durations))
            
            # Record as metrics (avoid deadlock by creating metric points directly)
            duration_metric = MetricPoint(
                timestamp=datetime.utcnow(),
                value=duration,
                labels={"operation": operation}
            )
            self._metrics["operation_duration"].append(duration_metric)

            # Record counter metric
            counter_labels = {"operation": operation, "status": "success" if success else "error"}
            counter_key = self._make_metric_key("operation_count", counter_labels)
            self._counters[counter_key] += 1

            counter_metric = MetricPoint(
                timestamp=datetime.utcnow(),
                value=float(self._counters[counter_key]),
                labels=counter_labels
            )
            self._metrics["operation_count"].append(counter_metric)
    
    def get_performance_stats(self, operation: str = None) -> Dict[str, PerformanceStats]:
        """Get performance statistics."""
        with self._lock:
            if operation:
                return {operation: self._performance_stats.get(operation)}
            return dict(self._performance_stats)
    
    def get_metrics(self, name: str = None, since: datetime = None) -> Dict[str, List[MetricPoint]]:
        """Get metric data points."""
        with self._lock:
            result = {}
            
            metrics_to_get = [name] if name else self._metrics.keys()
            
            for metric_name in metrics_to_get:
                if metric_name in self._metrics:
                    points = list(self._metrics[metric_name])
                    
                    if since:
                        points = [p for p in points if p.timestamp >= since]
                    
                    result[metric_name] = points
            
            return result
    
    def get_counters(self) -> Dict[str, int]:
        """Get all counter values."""
        with self._lock:
            return dict(self._counters)
    
    def get_gauges(self) -> Dict[str, float]:
        """Get all gauge values."""
        with self._lock:
            return dict(self._gauges)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all metrics."""
        with self._lock:
            return {
                "performance_stats": {k: {
                    "operation": v.operation,
                    "count": v.count,
                    "avg_duration": v.avg_duration,
                    "median_duration": v.median_duration,
                    "min_duration": v.min_duration,
                    "max_duration": v.max_duration,
                    "success_rate": v.success_count / v.count if v.count > 0 else 0,
                    "last_execution": v.last_execution.isoformat() if v.last_execution else None
                } for k, v in self._performance_stats.items()},
                "counters": dict(self._counters),
                "gauges": dict(self._gauges),
                "metric_counts": {name: len(points) for name, points in self._metrics.items()}
            }
    
    def reset_metrics(self, metric_name: str = None):
        """Reset metrics data."""
        with self._lock:
            if metric_name:
                if metric_name in self._metrics:
                    self._metrics[metric_name].clear()
                if metric_name in self._performance_stats:
                    del self._performance_stats[metric_name]
            else:
                self._metrics.clear()
                self._performance_stats.clear()
                self._counters.clear()
                self._gauges.clear()
    
    def _make_metric_key(self, name: str, labels: Dict[str, str] = None) -> str:
        """Create a unique key for a metric with labels."""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}[{label_str}]"


# Global metrics collector
metrics_collector = MetricsCollector()


class PerformanceTimer:
    """Context manager for timing operations."""
    
    def __init__(self, operation: str, auto_log: bool = True):
        self.operation = operation
        self.auto_log = auto_log
        self.start_time = None
        self.end_time = None
        self.duration = None
        self.success = True
        self.logger = get_logger()
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = exc_type is None
        
        # Record performance metrics
        metrics_collector.record_performance(self.operation, self.duration, self.success)
        
        if self.auto_log:
            if self.success:
                self.logger.debug(
                    f"Operation completed: {self.operation}",
                    operation=self.operation,
                    duration=self.duration,
                    status="success"
                )
            else:
                self.logger.error(
                    f"Operation failed: {self.operation}",
                    operation=self.operation,
                    duration=self.duration,
                    status="error",
                    error=str(exc_val) if exc_val else "Unknown error"
                )


def timer(operation: str, auto_log: bool = True):
    """Decorator for timing function execution."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(operation, auto_log):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector."""
    return metrics_collector


# Convenience functions for common metrics
def record_crawl_metric(metric_name: str, value: float, **kwargs):
    """Record a crawling-related metric."""
    labels = {}

    # Handle common parameters
    if 'url' in kwargs:
        labels["url"] = kwargs['url']
    if 'domain' in kwargs:
        labels["domain"] = kwargs['domain']

    # Add any additional labels
    for key, val in kwargs.items():
        if key not in ['url', 'domain'] and val is not None:
            labels[key] = str(val)

    metrics_collector.record_metric(f"crawl_{metric_name}", value, labels)


def record_download_metric(metric_name: str, value: float, content_type: str = None, file_size: int = None):
    """Record a download-related metric."""
    labels = {}
    if content_type:
        labels["content_type"] = content_type
    if file_size:
        labels["file_size_category"] = _categorize_file_size(file_size)
    
    metrics_collector.record_metric(f"download_{metric_name}", value, labels)


def record_database_metric(metric_name: str, value: float, table: str = None, operation: str = None):
    """Record a database-related metric."""
    labels = {}
    if table:
        labels["table"] = table
    if operation:
        labels["operation"] = operation
    
    metrics_collector.record_metric(f"database_{metric_name}", value, labels)


def _categorize_file_size(size_bytes: int) -> str:
    """Categorize file size for metrics."""
    if size_bytes < 1024:
        return "tiny"
    elif size_bytes < 1024 * 1024:
        return "small"
    elif size_bytes < 10 * 1024 * 1024:
        return "medium"
    elif size_bytes < 100 * 1024 * 1024:
        return "large"
    else:
        return "huge"
