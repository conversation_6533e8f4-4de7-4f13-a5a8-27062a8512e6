# SpigaMonde Web Interface Complete Architecture Mapping

## OBJECTIVE:
You are tasked with Extending a knowledge graph that maps the complete flow from user interface elements to API endpoints to SpigaMonde core functionality, including all file locations, data flows, and component relationships.

Use the MCP memory server to store and organize this knowledge graph.
This is a complex task that requires a deep understanding of the SpigaMonde Web Interface architecture and functionality.  Breakdown the task into sub tasks and Use sequentialThinking mcp server to store and organize this knowledge graph.  Use other modes as needed. See SPECIFIC INSTRUCTIONS at the end of this prompt.

## ENTITIES TO MAP:

### Frontend Components & Locations
- **index.html** (`web/frontend/index.html`): Main UI structure with tabs and widgets
- **app-simple.js** (`web/frontend/app-simple.js`): JavaScript application logic and API calls
- **style.css** (`web/frontend/style.css`): UI styling and responsive design
- **Dashboard Tab**: Statistics display and health monitoring UI
- **Scripts Tab**: Dual-mode script execution interface (Command/Script modes)
- **Reset Tab**: System cleanup and database management UI
- **Logs Tab**: Activity logging and real-time monitoring display
- **Mode Toggle Buttons**: Command Mode vs Script Mode switching
- **Script Dropdowns**: Dynamic script selection from backend API
- **Execute Buttons**: Trigger script execution via API calls
- **Status Indicators**: Real-time feedback and progress display

### API Endpoints & Backend Logic
- **`/api/health`** (`web/backend/main.py:app.get`): System health and connectivity check
- **`/api/spiga/stats`** (`web/backend/main.py:get_spiga_stats`): Database statistics and content counts
- **`/api/available-scripts`** (`web/backend/main.py:get_available_scripts`): Script discovery and categorization
- **`/api/execute-script`** (`web/backend/main.py:execute_script`): Script execution with subprocess management
- **`/api/reset-system`** (`web/backend/main.py:reset_system`): Database and download cleanup
- **`/api/mode`** (`web/backend/main.py:get_mode`): Configuration mode detection
- **`/api/logs/recent`** (`web/backend/main.py:get_recent_logs`): Log file access and streaming
- **Backend Main** (`web/backend/main.py`): FastAPI application with CORS and middleware
- **Subprocess Execution Engine**: UV-based script execution with environment isolation

### SpigaMonde Core Integration Points
- **SpigaMonde CLI** (`spigamonde/cli.py`): Command-line interface executed by web backend
- **Database Connection** (`spigamonde/database/connection.py`): SQLite database access layer
- **Content Models** (`spigamonde/models/content.py`): Database schema and ORM models
- **Settings System** (`spigamonde/config/settings.py`): Configuration management
- **Logging System** (`spigamonde/monitoring/logger.py`): Enhanced logging with correlation IDs
- **Spider Engine** (`spigamonde/spiders/content_spider.py`): Core crawling functionality

### Script Locations & Categories
- **Web Scripts** (`web/scripts/`): Lightweight scripts for web interface
  - `test_crawl_simple.py`: Basic SpigaMonde integration test
  - `simple_news_check.py`: HTTP-based news source checker
- **Example Scripts** (`scripts/examples/`): Complex SpigaMonde examples
  - `news_headline_aggregator.py`: News aggregation with Scrapy
  - `robust_economic_news.py`: Economic news crawler
- **Template Scripts** (`scripts/templates/`): Customizable script templates
- **CLI Commands**: Direct SpigaMonde command execution

### Data Storage & Logs
- **Main Database** (`spigamonde.db`): SQLite database with content and analysis
- **Download Directories**: 
  - `downloads/`: Production downloads
  - `test_downloads/`: Testing downloads
- **Log Locations**:
  - Console Output: Real-time backend logs
  - Browser Console: Frontend JavaScript logs
  - Activity Log: Web interface action tracking
  - SpigaMonde Logs: Core system logging

### Configuration & Environment
- **UV Environment** (`.venv/`): Virtual environment with all dependencies
- **Project Root** (`I:\SpigaMonde`): Base directory for all operations
- **Working Directory Management**: Backend changes to project root on startup
- **Environment Variables**: Configuration through settings system

## RELATIONSHIPS TO MAP:

### UI to API Flow
- Dashboard Tab → CALLS → `/api/health`, `/api/spiga/stats`
- Scripts Tab → CALLS → `/api/available-scripts`, `/api/execute-script`
- Reset Tab → CALLS → `/api/reset-system`
- Logs Tab → CALLS → `/api/logs/recent`
- Mode Toggle → TRIGGERS → Script categorization and UI updates
- Execute Buttons → SEND_POST_TO → `/api/execute-script` with script configuration
- Status Displays → RECEIVE_FROM → API responses with real-time data

### API to SpigaMonde Core Flow
- `/api/spiga/stats` → QUERIES → SpigaMonde Database via session_scope
- `/api/execute-script` → EXECUTES → UV subprocess with SpigaMonde CLI/scripts
- `/api/available-scripts` → SCANS → Script directories and categorizes by type
- `/api/reset-system` → CALLS → Database cleanup and download directory clearing
- `/api/health` → TESTS → Database connectivity and system status
- Backend startup → INITIALIZES → SpigaMonde database and logging

### Script Execution Flow
- Web UI Script Selection → SENDS → Script type and parameters to API
- API Subprocess Engine → EXECUTES → `uv run python script.py` with proper environment
- Script Execution → ACCESSES → SpigaMonde database, settings, and logging
- Script Results → CAPTURED → By subprocess and returned to frontend
- Frontend → DISPLAYS → Execution status, results, and error messages

### Data Flow Mapping
- User Actions → LOGGED_TO → Browser console and activity log
- API Calls → LOGGED_TO → Backend console output
- Script Execution → LOGGED_TO → SpigaMonde logging system
- Database Operations → TRACKED_BY → Session scope and transaction management
- File Operations → MONITORED_BY → Download directory management

### Error Handling Flow
- Frontend Errors → DISPLAYED_IN → UI error messages and console
- API Errors → RETURNED_AS → HTTP status codes with error details
- Script Failures → CAPTURED_BY → Subprocess stderr and logged
- Database Errors → HANDLED_BY → Session rollback and error reporting
- Environment Issues → DIAGNOSED_BY → Virtual environment validation

## CRITICAL PATHS TO ENCODE:

### Script Execution Critical Path
1. User selects script in frontend dropdown
2. Frontend calls `/api/available-scripts` to populate options
3. User configures parameters and clicks execute
4. Frontend sends POST to `/api/execute-script` with configuration
5. Backend validates script type and parameters
6. Backend constructs `uv run python script.py` command
7. Subprocess executes in proper virtual environment
8. Script accesses SpigaMonde database and core functionality
9. Results captured and returned to frontend
10. Frontend displays execution status and results

### Statistics Display Critical Path
1. Frontend auto-refresh timer triggers
2. JavaScript calls `/api/spiga/stats`
3. Backend opens database session
4. Queries content and analysis tables
5. Returns JSON with counts and timestamps
6. Frontend updates dashboard displays
7. Process repeats every 30 seconds

### Virtual Environment Critical Path
1. Backend starts with `uv run uvicorn`
2. Working directory changed to project root
3. SpigaMonde modules become importable
4. Script execution uses `uv run` prefix
5. All operations share same environment
6. Prevents import failures and segmentation faults

## FILE LOCATION MAPPING:

### Frontend Architecture
- **Entry Point**: `web/frontend/index.html` (main UI structure)
- **Application Logic**: `web/frontend/app-simple.js` (API calls, event handling)
- **Styling**: `web/frontend/style.css` (responsive design, component styling)
- **Cache Busting**: Version parameters in script/CSS includes

### Backend Architecture
- **API Server**: `web/backend/main.py` (FastAPI application)
- **Startup Logic**: Working directory management and database initialization
- **Endpoint Handlers**: Individual functions for each API endpoint
- **Subprocess Management**: UV-based script execution engine

### Integration Points
- **Database Access**: Backend imports `spigamonde.database.connection`
- **Settings Access**: Backend imports `spigamonde.config.settings`
- **Logging Integration**: Backend uses `spigamonde.monitoring.logger`
- **CLI Integration**: Subprocess calls to `spigamonde.cli`

### Script Organization
- **Web Scripts**: `web/scripts/` (lightweight, web-optimized)
- **Core Examples**: `scripts/examples/` (full SpigaMonde functionality)
- **Templates**: `scripts/templates/` (customizable patterns)
- **CLI Commands**: Direct SpigaMonde command execution


### SPECIFIC INSTRUCTIONS

#### CHUNKING STRATEGY (IMPORTANT)
**Use modest-sized chunks to avoid tool use errors:**

- **Store 3-5 entities per memory operation** - Don't try to store too many nodes at once
- **Process one component category at a time** 
- **Use incremental building** - Start with core entities, then add relationships in separate operations
- **Verify each chunk** - Confirm successful storage before proceeding to next chunk
- **If errors occur** - Reduce chunk size and retry with smaller batches

This mapping creates a complete architectural view from user interaction to core SpigaMonde functionality, enabling comprehensive understanding of data flows, dependencies, and integration points.