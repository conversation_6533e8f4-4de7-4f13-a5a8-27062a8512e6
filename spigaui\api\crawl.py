"""
Crawl management endpoints for SpigaUI.

Handles starting, stopping, and monitoring crawl jobs.
"""

from typing import Dict, Any, List
from uuid import uuid4

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, HttpUrl
import structlog

from spigaui.models.crawl import CrawlJobCreate, CrawlJobResponse, CrawlConfig
from spigaui.services.crawl_service import CrawlService
from spigaui.core.logging import log_job_event

router = APIRouter()
logger = structlog.get_logger(__name__)


class StartCrawlRequest(BaseModel):
    """Request model for starting a crawl job."""
    url: HttpUrl
    config: CrawlConfig


@router.post("/crawl/start", response_model=CrawlJobResponse)
async def start_crawl(
    request: StartCrawlRequest,
    background_tasks: BackgroundTasks
) -> CrawlJobResponse:
    """
    Start a new crawl job.
    
    Creates and queues a new crawl job with the specified configuration.
    """
    job_id = str(uuid4())
    
    logger.info(
        "Starting crawl job",
        job_id=job_id,
        url=str(request.url),
        config=request.config.model_dump()
    )
    
    try:
        # Create crawl service instance
        crawl_service = CrawlService()
        
        # Create job data
        job_create = CrawlJobCreate(
            id=job_id,
            url=request.url,
            config=request.config
        )
        
        # Start the crawl job (async via Celery)
        job = await crawl_service.start_crawl(job_create)
        
        log_job_event(job_id, "started", url=str(request.url))
        
        logger.info("Crawl job started successfully", job_id=job_id)
        return job
        
    except Exception as e:
        logger.error("Failed to start crawl job", job_id=job_id, error=str(e))
        log_job_event(job_id, "start_failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start crawl job: {str(e)}"
        )


@router.get("/crawl/{job_id}", response_model=CrawlJobResponse)
async def get_crawl_job(job_id: str) -> CrawlJobResponse:
    """
    Get crawl job status and details.
    
    Returns the current status, progress, and results of a crawl job.
    """
    logger.debug("Getting crawl job status", job_id=job_id)
    
    try:
        crawl_service = CrawlService()
        job = await crawl_service.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Crawl job {job_id} not found"
            )
        
        return job
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get crawl job", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get crawl job: {str(e)}"
        )


@router.post("/crawl/{job_id}/cancel")
async def cancel_crawl_job(job_id: str) -> Dict[str, Any]:
    """
    Cancel a running crawl job.
    
    Attempts to cancel a crawl job if it's currently running.
    """
    logger.info("Cancelling crawl job", job_id=job_id)
    
    try:
        crawl_service = CrawlService()
        success = await crawl_service.cancel_job(job_id)
        
        if not success:
            raise HTTPException(
                status_code=400,
                detail=f"Could not cancel crawl job {job_id}"
            )
        
        log_job_event(job_id, "cancelled")
        logger.info("Crawl job cancelled successfully", job_id=job_id)
        
        return {"message": f"Crawl job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel crawl job", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel crawl job: {str(e)}"
        )


@router.get("/crawl")
async def list_crawl_jobs(
    status: str = None,
    limit: int = 50,
    offset: int = 0
) -> Dict[str, Any]:
    """
    List crawl jobs with optional filtering.
    
    Returns a paginated list of crawl jobs, optionally filtered by status.
    """
    logger.debug(
        "Listing crawl jobs",
        status=status,
        limit=limit,
        offset=offset
    )
    
    try:
        crawl_service = CrawlService()
        jobs, total = await crawl_service.list_jobs(
            status=status,
            limit=limit,
            offset=offset
        )
        
        return {
            "jobs": jobs,
            "total": total,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error("Failed to list crawl jobs", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list crawl jobs: {str(e)}"
        )
