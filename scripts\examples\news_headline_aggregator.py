#!/usr/bin/env python3
"""
Economic News Aggregator for Asia & Latin America - SpigaMonde
Crawls economic news sources focused on Asian and Latin American markets.

Usage:
    python news_headline_aggregator.py

Features:
    - Economic news from Asia and Latin America
    - Business and financial market coverage
    - Multi-source crawling (Reuters, Bloomberg, Nikkei, etc.)
    - Headline extraction and economic metadata parsing
    - Regional economic trend analysis
    - HTML dashboard generation with live updates
    - Real-time monitoring and progress tracking
    - Export options (HTML, JSON, CSV)
"""

import sys
import os
import json
import re
from pathlib import Path
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
import threading
import time

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content, ContentAnalysis
from spigamonde.monitoring.dashboard import run_live_dashboard
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Enhanced HTML parsing
try:
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True
except ImportError:
    HAS_BEAUTIFULSOUP = False
    print("Installing beautifulsoup4 for enhanced headline extraction...")
    os.system("pip install beautifulsoup4")
    from bs4 import BeautifulSoup
    HAS_BEAUTIFULSOUP = True


class NewsHeadlineExtractor:
    """Specialized news headline and article extractor."""
    
    def __init__(self):
        # Economic news sources focused on Asia and Latin America
        self.news_sources = {
            'Reuters Business Asia': {
                'rss': 'https://feeds.reuters.com/reuters/businessNews',
                'web': 'https://www.reuters.com/business/',
                'category': 'economics'
            },
            'Bloomberg Asia': {
                'rss': 'https://feeds.bloomberg.com/markets/news.rss',
                'web': 'https://www.bloomberg.com/asia',
                'category': 'economics'
            },
            'Financial Times Asia': {
                'web': 'https://www.ft.com/world/asia-pacific',
                'category': 'economics'
            },
            'Nikkei Asia': {
                'rss': 'https://asia.nikkei.com/rss/feed/nar',
                'web': 'https://asia.nikkei.com/',
                'category': 'economics'
            },
            'South China Morning Post Business': {
                'rss': 'https://www.scmp.com/rss/91/feed',
                'web': 'https://www.scmp.com/business',
                'category': 'economics'
            },
            'Reuters Latin America': {
                'rss': 'https://feeds.reuters.com/reuters/worldNews',
                'web': 'https://www.reuters.com/world/americas/',
                'category': 'economics'
            },
            'Bloomberg Latin America': {
                'web': 'https://www.bloomberg.com/latam',
                'category': 'economics'
            },
            'Americas Quarterly': {
                'rss': 'https://www.americasquarterly.org/fullfeed/',
                'web': 'https://www.americasquarterly.org/',
                'category': 'economics'
            },
            'Latin Trade': {
                'web': 'https://latintrade.com/',
                'category': 'economics'
            },
            'Asia Times': {
                'rss': 'https://asiatimes.com/feed/',
                'web': 'https://asiatimes.com/',
                'category': 'economics'
            },
            'The Diplomat Economics': {
                'web': 'https://thediplomat.com/topics/economics/',
                'category': 'economics'
            },
            'Council on Foreign Relations': {
                'rss': 'https://www.cfr.org/feeds/blog.xml',
                'web': 'https://www.cfr.org/',
                'category': 'economics'
            }
        }
        
        # Headline extraction patterns
        self.headline_selectors = [
            'h1', 'h2', 'h3',  # Standard headings
            '.headline', '.title', '.story-title',  # Common classes
            '[data-testid="headline"]',  # Modern data attributes
            'article h1', 'article h2',  # Article headings
            '.entry-title', '.post-title',  # Blog/CMS titles
        ]
        
        # Article metadata selectors
        self.metadata_selectors = {
            'author': ['.author', '.byline', '[rel="author"]', '.writer'],
            'date': ['.date', '.timestamp', '.published', 'time'],
            'category': ['.category', '.section', '.topic'],
            'summary': ['.summary', '.excerpt', '.description', '.lead']
        }
        
        # RSS feed patterns
        self.rss_patterns = {
            'title': re.compile(r'<title[^>]*>([^<]+)</title>', re.IGNORECASE),
            'link': re.compile(r'<link[^>]*>([^<]+)</link>', re.IGNORECASE),
            'description': re.compile(r'<description[^>]*>([^<]+)</description>', re.IGNORECASE),
            'pubDate': re.compile(r'<pubDate[^>]*>([^<]+)</pubDate>', re.IGNORECASE),
        }
    
    def get_all_news_urls(self):
        """Get all news URLs to crawl."""
        urls = []
        for source_name, source_info in self.news_sources.items():
            if 'rss' in source_info:
                urls.append(source_info['rss'])
            if 'web' in source_info:
                urls.append(source_info['web'])
        return urls
    
    def extract_headlines_from_html(self, html_content, url):
        """Extract headlines and metadata from HTML content."""
        if not HAS_BEAUTIFULSOUP:
            return self._extract_headlines_regex(html_content, url)
        
        soup = BeautifulSoup(html_content, 'html.parser')
        headlines = []
        
        # Determine source info
        source_info = self._get_source_info(url)
        
        # Extract headlines using multiple selectors
        for selector in self.headline_selectors:
            elements = soup.select(selector)
            for element in elements:
                headline_text = element.get_text().strip()
                if self._is_valid_headline(headline_text):
                    # Extract associated metadata
                    metadata = self._extract_article_metadata(element, soup)
                    
                    headline = {
                        'title': headline_text,
                        'url': url,
                        'source': source_info['name'],
                        'category': source_info['category'],
                        'extracted_at': datetime.now().isoformat(),
                        'metadata': metadata
                    }
                    headlines.append(headline)
        
        return headlines
    
    def extract_headlines_from_rss(self, rss_content, url):
        """Extract headlines from RSS feed content."""
        headlines = []
        source_info = self._get_source_info(url)
        
        # Parse RSS items
        items = re.findall(r'<item[^>]*>(.*?)</item>', rss_content, re.DOTALL | re.IGNORECASE)
        
        for item in items:
            headline = {
                'source': source_info['name'],
                'category': source_info['category'],
                'extracted_at': datetime.now().isoformat(),
                'metadata': {}
            }
            
            # Extract RSS fields
            for field, pattern in self.rss_patterns.items():
                match = pattern.search(item)
                if match:
                    value = match.group(1).strip()
                    if field == 'title':
                        headline['title'] = self._clean_html(value)
                    elif field == 'link':
                        headline['url'] = value
                    else:
                        headline['metadata'][field] = self._clean_html(value)
            
            if headline.get('title') and self._is_valid_headline(headline['title']):
                headlines.append(headline)
        
        return headlines
    
    def _get_source_info(self, url):
        """Get source information from URL."""
        domain = urlparse(url).netloc.lower()
        
        for source_name, source_info in self.news_sources.items():
            # Check if URL matches any source
            for source_url in [source_info.get('rss', ''), source_info.get('web', '')]:
                if source_url and domain in source_url.lower():
                    return {
                        'name': source_name,
                        'category': source_info['category']
                    }
        
        # Default for unknown sources
        return {
            'name': domain.replace('www.', '').title(),
            'category': 'general'
        }
    
    def _extract_article_metadata(self, headline_element, soup):
        """Extract metadata associated with a headline."""
        metadata = {}
        
        # Look for metadata near the headline
        parent = headline_element.parent
        if parent:
            for meta_type, selectors in self.metadata_selectors.items():
                for selector in selectors:
                    element = parent.select_one(selector)
                    if element:
                        metadata[meta_type] = element.get_text().strip()
                        break
        
        return metadata
    
    def _is_valid_headline(self, text):
        """Check if text is a valid headline."""
        if not text or len(text) < 10:
            return False
        if len(text) > 200:  # Too long to be a headline
            return False
        if text.lower() in ['home', 'news', 'menu', 'search', 'login']:
            return False
        return True
    
    def _clean_html(self, text):
        """Remove HTML tags and clean text."""
        # Remove HTML tags
        text = re.sub(r'<[^>]+>', '', text)
        # Decode HTML entities
        text = text.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
        text = text.replace('&quot;', '"').replace('&#39;', "'")
        return text.strip()
    
    def _extract_headlines_regex(self, content, url):
        """Fallback headline extraction using regex."""
        headlines = []
        source_info = self._get_source_info(url)
        
        # Simple title extraction
        title_matches = re.findall(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
        for title in title_matches:
            if self._is_valid_headline(title):
                headlines.append({
                    'title': self._clean_html(title),
                    'url': url,
                    'source': source_info['name'],
                    'category': source_info['category'],
                    'extracted_at': datetime.now().isoformat(),
                    'metadata': {}
                })
        
        return headlines


class NewsHTMLGenerator:
    """Generate HTML dashboard for news headlines."""
    
    def __init__(self):
        self.template_dir = Path(__file__).parent / 'templates'
        self.output_dir = Path(__file__).parent / 'output'
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_news_dashboard(self, headlines, output_file='news_dashboard.html'):
        """Generate complete HTML news dashboard."""
        
        # Organize headlines by source and time
        organized_headlines = self._organize_headlines(headlines)
        
        # Generate HTML
        html_content = self._generate_html(organized_headlines)
        
        # Write to file
        output_path = self.output_dir / output_file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def _organize_headlines(self, headlines):
        """Organize headlines by source, category, and time."""
        organized = {
            'by_source': {},
            'by_category': {},
            'recent': [],
            'total_count': len(headlines),
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Sort by extraction time (most recent first)
        sorted_headlines = sorted(headlines, 
                                key=lambda x: x.get('extracted_at', ''), 
                                reverse=True)
        
        organized['recent'] = sorted_headlines[:20]  # Top 20 recent
        
        # Group by source
        for headline in headlines:
            source = headline.get('source', 'Unknown')
            if source not in organized['by_source']:
                organized['by_source'][source] = []
            organized['by_source'][source].append(headline)
        
        # Group by category
        for headline in headlines:
            category = headline.get('category', 'general')
            if category not in organized['by_category']:
                organized['by_category'][category] = []
            organized['by_category'][category].append(headline)
        
        return organized
    
    def _generate_html(self, organized_headlines):
        """Generate the complete HTML dashboard."""
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpigaMonde News Dashboard</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        .content-grid {{
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }}
        .headlines-section {{
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }}
        .sidebar {{
            display: flex;
            flex-direction: column;
            gap: 20px;
        }}
        .sidebar-section {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .headline {{
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }}
        .headline:last-child {{
            border-bottom: none;
        }}
        .headline-title {{
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }}
        .headline-meta {{
            font-size: 0.9em;
            color: #666;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }}
        .source-tag {{
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }}
        .category-tag {{
            background: #f0f0f0;
            color: #555;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }}
        .source-list {{
            list-style: none;
            padding: 0;
        }}
        .source-item {{
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }}
        .refresh-info {{
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }}
        @media (max-width: 768px) {{
            .content-grid {{
                grid-template-columns: 1fr;
            }}
            .stats {{
                grid-template-columns: repeat(2, 1fr);
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📰 SpigaMonde News Dashboard</h1>
        <p>Real-time news aggregation and analysis</p>
        <p>Last Updated: {organized_headlines['last_updated']}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">{organized_headlines['total_count']}</div>
            <div>Total Headlines</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len(organized_headlines['by_source'])}</div>
            <div>News Sources</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len(organized_headlines['by_category'])}</div>
            <div>Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{len(organized_headlines['recent'])}</div>
            <div>Recent Headlines</div>
        </div>
    </div>
    
    <div class="content-grid">
        <div class="headlines-section">
            <h2>🔥 Latest Headlines</h2>
            {self._generate_headlines_html(organized_headlines['recent'])}
        </div>
        
        <div class="sidebar">
            <div class="sidebar-section">
                <h3>📊 Sources</h3>
                <ul class="source-list">
                    {self._generate_sources_html(organized_headlines['by_source'])}
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>🏷️ Categories</h3>
                <ul class="source-list">
                    {self._generate_categories_html(organized_headlines['by_category'])}
                </ul>
            </div>
        </div>
    </div>
    
    <div class="refresh-info">
        <p>🔄 Dashboard auto-refreshes every 15 minutes</p>
        <p>Powered by SpigaMonde Web Crawler</p>
    </div>
    
    <script>
        // Auto-refresh every 15 minutes
        setTimeout(function() {{
            location.reload();
        }}, 15 * 60 * 1000);
    </script>
</body>
</html>
"""
        return html
    
    def _generate_headlines_html(self, headlines):
        """Generate HTML for headlines list."""
        html = ""
        for headline in headlines:
            title = headline.get('title', 'No title')
            source = headline.get('source', 'Unknown')
            category = headline.get('category', 'general')
            
            html += f"""
            <div class="headline">
                <div class="headline-title">{title}</div>
                <div class="headline-meta">
                    <span class="source-tag">{source}</span>
                    <span class="category-tag">{category}</span>
                </div>
            </div>
            """
        return html
    
    def _generate_sources_html(self, sources):
        """Generate HTML for sources list."""
        html = ""
        for source, headlines in sources.items():
            html += f"""
            <li class="source-item">
                <span>{source}</span>
                <span>{len(headlines)}</span>
            </li>
            """
        return html
    
    def _generate_categories_html(self, categories):
        """Generate HTML for categories list."""
        html = ""
        for category, headlines in categories.items():
            html += f"""
            <li class="source-item">
                <span>{category.title()}</span>
                <span>{len(headlines)}</span>
            </li>
            """
        return html


def main():
    """Execute news headline aggregation and dashboard generation."""
    console = Console()
    
    # Display banner
    console.print(Panel.fit(
        "[bold green]📈 Economic News: Asia & Latin America[/bold green]\n"
        "Business and financial market coverage from key regions",
        border_style="green"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Initialize extractor
    extractor = NewsHeadlineExtractor()
    news_urls = extractor.get_all_news_urls()
    
    console.print(f"[cyan]Crawling {len(news_urls)} news sources...[/cyan]")
    
    # Configure crawl for news sites
    crawl_config = {
        'USER_AGENT': 'SpigaMonde News Aggregator 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.0,  # Respectful crawling
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS': 3,  # Moderate concurrency for news
        'DEPTH_LIMIT': 1,  # Focus on main pages and RSS feeds
        'CLOSESPIDER_PAGECOUNT': 100,
        'LOG_LEVEL': 'INFO',
        'ALLOWED_FILE_TYPES': ['html', 'xml', 'rss'],
    }
    
    # Start monitoring
    console.print("[yellow]Starting monitoring dashboard...[/yellow]")
    dashboard_thread = threading.Thread(
        target=run_live_dashboard,
        args=(300,),  # 5 minutes
        daemon=True
    )
    dashboard_thread.start()
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=news_urls)
        process.start()
        
        console.print("[green]✓ News crawl completed![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Extract headlines and generate dashboard
    console.print("\n[bold cyan]Extracting headlines and generating dashboard...[/bold cyan]")
    generate_news_dashboard(console, extractor)
    
    return 0


def generate_news_dashboard(console, extractor):
    """Extract headlines and generate HTML dashboard."""
    
    all_headlines = []
    
    with session_scope() as session:
        # Get all crawled content
        crawled_content = session.query(Content).all()
        
        console.print(f"Processing {len(crawled_content)} crawled items...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Extracting headlines...", total=len(crawled_content))
            
            for content in crawled_content:
                if content.local_path and os.path.exists(content.local_path):
                    try:
                        with open(content.local_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content_text = f.read()
                        
                        # Determine if RSS or HTML
                        if 'rss' in content.url.lower() or '<rss' in content_text.lower():
                            headlines = extractor.extract_headlines_from_rss(content_text, content.url)
                        else:
                            headlines = extractor.extract_headlines_from_html(content_text, content.url)
                        
                        all_headlines.extend(headlines)
                        
                    except Exception as e:
                        console.print(f"[yellow]Warning: Could not process {content.url}: {e}[/yellow]")
                
                progress.advance(task)
    
    console.print(f"[green]✓ Extracted {len(all_headlines)} headlines[/green]")
    
    # Generate HTML dashboard
    if all_headlines:
        generator = NewsHTMLGenerator()
        output_path = generator.generate_news_dashboard(all_headlines)
        
        console.print(f"[green]✓ HTML dashboard generated: {output_path}[/green]")
        console.print(f"[cyan]Open in browser: file://{output_path.absolute()}[/cyan]")
        
        # Display summary
        sources = set(h.get('source', 'Unknown') for h in all_headlines)
        categories = set(h.get('category', 'general') for h in all_headlines)
        
        summary_table = Table(title="News Aggregation Summary", show_header=True, header_style="bold blue")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="green")
        
        summary_table.add_row("Total Headlines", str(len(all_headlines)))
        summary_table.add_row("News Sources", str(len(sources)))
        summary_table.add_row("Categories", str(len(categories)))
        summary_table.add_row("Dashboard File", str(output_path.name))
        
        console.print(summary_table)
        
        # Export options
        console.print(f"\n[dim]Export headlines to JSON:[/dim]")
        json_path = output_path.parent / 'headlines.json'
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(all_headlines, f, indent=2, ensure_ascii=False)
        console.print(f"[dim]JSON export: {json_path}[/dim]")
        
    else:
        console.print("[yellow]No headlines extracted. Check news sources and try again.[/yellow]")


if __name__ == "__main__":
    sys.exit(main())
