"""Integration tests for database operations."""

import pytest
import tempfile
import os
from datetime import datetime
from pathlib import Path

from spigamonde.database.connection import DatabaseManager, session_scope
from spigamonde.models.content import Content, ContentStatus, ContentType, URL, CrawlSession
from spigamonde.config.settings import Settings


@pytest.fixture
def temp_db():
    """Create a temporary database for testing."""
    # Use in-memory SQLite database for better test isolation
    test_db_url = "sqlite:///:memory:"

    # Create settings with in-memory database
    test_settings = Settings()
    test_settings.database.url = test_db_url

    # Create database manager
    db_manager = DatabaseManager()
    db_manager._initialized = False  # Reset initialization

    # Override global settings temporarily
    import spigamonde.config.settings
    original_settings = spigamonde.config.settings.settings
    spigamonde.config.settings.settings = test_settings

    try:
        # Initialize database
        db_manager.initialize()
        db_manager.create_tables()

        yield db_manager

    finally:
        # Cleanup
        db_manager.close()
        spigamonde.config.settings.settings = original_settings


class TestDatabaseIntegration:
    """Integration tests for database operations."""
    
    def test_database_initialization(self, temp_db):
        """Test database initializes correctly."""
        assert temp_db._initialized
        assert temp_db.engine is not None
        assert temp_db.SessionLocal is not None
    
    def test_url_crud_operations(self, temp_db):
        """Test URL model CRUD operations."""
        with temp_db.session_scope() as session:
            # Create URL
            url = URL(
                url="https://example.com/test",
                domain="example.com",
                path="/test",
                depth=1,
                is_seed=True
            )
            session.add(url)
            session.flush()
            
            url_id = url.id
            assert url_id is not None
        
        # Read URL
        with temp_db.session_scope() as session:
            retrieved_url = session.query(URL).filter(URL.id == url_id).first()
            assert retrieved_url is not None
            assert retrieved_url.url == "https://example.com/test"
            assert retrieved_url.domain == "example.com"
            assert retrieved_url.is_seed is True
            assert retrieved_url.is_crawled is False
        
        # Update URL
        with temp_db.session_scope() as session:
            url = session.query(URL).filter(URL.id == url_id).first()
            url.is_crawled = True
            url.status_code = 200
            url.last_crawled = datetime.now()
        
        # Verify update
        with temp_db.session_scope() as session:
            url = session.query(URL).filter(URL.id == url_id).first()
            assert url.is_crawled is True
            assert url.status_code == 200
            assert url.last_crawled is not None
    
    def test_content_crud_operations(self, temp_db):
        """Test Content model CRUD operations."""
        # First create a URL
        with temp_db.session_scope() as session:
            url = URL(
                url="https://example.com",
                domain="example.com",
                path="/",
                depth=0,
                is_seed=True
            )
            session.add(url)
            session.flush()
            url_id = url.id

        # Create Content
        with temp_db.session_scope() as session:
            content = Content(
                url="https://example.com/document.pdf",
                filename="document.pdf",
                file_extension="pdf",
                mime_type="application/pdf",
                content_type=ContentType.DOCUMENT,
                status=ContentStatus.PENDING,
                url_hash="test_hash_123",
                source_url_id=url_id
            )
            session.add(content)
            session.flush()
            content_id = content.id

        # Read Content
        with temp_db.session_scope() as session:
            retrieved_content = session.query(Content).filter(Content.id == content_id).first()
            assert retrieved_content is not None
            assert retrieved_content.filename == "document.pdf"
            assert retrieved_content.content_type == ContentType.DOCUMENT
            assert retrieved_content.status == ContentStatus.PENDING
            assert retrieved_content.is_downloaded is False

        # Update Content (simulate download)
        with temp_db.session_scope() as session:
            content = session.query(Content).filter(Content.id == content_id).first()
            content.status = ContentStatus.COMPLETED
            content.is_downloaded = True
            content.local_path = "/downloads/document.pdf"
            content.file_size = 1024
            content.content_hash = "abc123def456"

        # Verify update
        with temp_db.session_scope() as session:
            content = session.query(Content).filter(Content.id == content_id).first()
            assert content.status == ContentStatus.COMPLETED
            assert content.is_downloaded is True
            assert content.local_path == "/downloads/document.pdf"
            assert content.file_size == 1024
    
    def test_crawl_session_operations(self, temp_db):
        """Test CrawlSession model operations."""
        with temp_db.session_scope() as session:
            # Create crawl session
            crawl_session = CrawlSession(
                session_name="test_session",
                start_time=datetime.now(),
                seed_urls='["https://example.com"]',
                max_depth=3,
                max_pages=100
            )
            session.add(crawl_session)
            session.flush()
            session_id = crawl_session.id

        # Update session statistics
        with temp_db.session_scope() as session:
            crawl_session = session.query(CrawlSession).filter(CrawlSession.id == session_id).first()
            crawl_session.urls_discovered = 50
            crawl_session.urls_crawled = 25
            crawl_session.content_found = 10
            crawl_session.content_downloaded = 8
            crawl_session.total_size_bytes = 1024000

        # Verify statistics
        with temp_db.session_scope() as session:
            crawl_session = session.query(CrawlSession).filter(CrawlSession.id == session_id).first()
            assert crawl_session.urls_discovered == 50
            assert crawl_session.urls_crawled == 25
            assert crawl_session.content_found == 10
            assert crawl_session.content_downloaded == 8
            assert crawl_session.total_size_bytes == 1024000
    
    def test_relationships(self, temp_db):
        """Test model relationships work correctly."""
        with temp_db.session_scope() as session:
            # Create URL
            url = URL(
                url="https://relationships-test.com",
                domain="relationships-test.com",
                path="/",
                depth=0,
                is_seed=True
            )
            session.add(url)
            session.flush()
            
            # Create multiple content items for the same URL
            content1 = Content(
                url="https://relationships-test.com/doc1.pdf",
                filename="doc1.pdf",
                content_type=ContentType.DOCUMENT,
                url_hash="rel_hash1",
                source_url_id=url.id
            )
            content2 = Content(
                url="https://relationships-test.com/image1.jpg",
                filename="image1.jpg",
                content_type=ContentType.IMAGE,
                url_hash="rel_hash2",
                source_url_id=url.id
            )
            
            session.add_all([content1, content2])
            session.flush()
            
            # Test relationship from URL to Content
            assert len(url.content_items) == 2
            assert content1 in url.content_items
            assert content2 in url.content_items
            
            # Test relationship from Content to URL
            assert content1.source_url == url
            assert content2.source_url == url
    
    def test_unique_constraints(self, temp_db):
        """Test that unique constraints are enforced."""
        with temp_db.session_scope() as session:
            # Create first URL
            url1 = URL(
                url="https://example.com/test",
                domain="example.com",
                path="/test",
                depth=0
            )
            session.add(url1)
            session.commit()

        # Try to create duplicate URL
        with pytest.raises(Exception):  # Should raise integrity error
            with temp_db.session_scope() as session:
                url2 = URL(
                    url="https://example.com/test",  # Same URL
                    domain="example.com",
                    path="/test",
                    depth=1
                )
                session.add(url2)
                session.commit()
    
    def test_content_filtering_queries(self, temp_db):
        """Test common filtering queries work correctly."""
        # Create test data
        with temp_db.session_scope() as session:
            url = URL(
                url="https://filtering-test.com",
                domain="filtering-test.com",
                path="/",
                depth=0,
                is_seed=True
            )
            session.add(url)
            session.flush()
            
            # Create various content types
            contents = [
                Content(
                    url="https://filtering-test.com/doc.pdf",
                    filename="doc.pdf",
                    content_type=ContentType.DOCUMENT,
                    status=ContentStatus.COMPLETED,
                    is_downloaded=True,
                    url_hash="filter_hash1",
                    source_url_id=url.id
                ),
                Content(
                    url="https://filtering-test.com/image.jpg",
                    filename="image.jpg",
                    content_type=ContentType.IMAGE,
                    status=ContentStatus.PENDING,
                    is_downloaded=False,
                    url_hash="filter_hash2",
                    source_url_id=url.id
                ),
                Content(
                    url="https://filtering-test.com/video.mp4",
                    filename="video.mp4",
                    content_type=ContentType.VIDEO,
                    status=ContentStatus.FAILED,
                    is_downloaded=False,
                    url_hash="filter_hash3",
                    source_url_id=url.id
                )
            ]
            session.add_all(contents)
        
        # Test filtering by content type
        with temp_db.session_scope() as session:
            documents = session.query(Content).filter(Content.content_type == ContentType.DOCUMENT).all()
            assert len(documents) == 1
            assert documents[0].filename == "doc.pdf"

        # Test filtering by status
        with temp_db.session_scope() as session:
            completed = session.query(Content).filter(Content.status == ContentStatus.COMPLETED).all()
            assert len(completed) == 1
            assert completed[0].filename == "doc.pdf"

        # Test filtering by download status
        with temp_db.session_scope() as session:
            downloaded = session.query(Content).filter(Content.is_downloaded == True).all()
            assert len(downloaded) == 1
            assert downloaded[0].filename == "doc.pdf"

        # Test combined filtering
        with temp_db.session_scope() as session:
            pending_images = session.query(Content).filter(
                Content.content_type == ContentType.IMAGE,
                Content.status == ContentStatus.PENDING
            ).all()
            assert len(pending_images) == 1
            assert pending_images[0].filename == "image.jpg"


if __name__ == "__main__":
    pytest.main([__file__])
