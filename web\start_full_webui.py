#!/usr/bin/env python3
"""
SpigaMonde Complete Web UI Startup Script
Starts both backend and frontend servers for the web interface
Location: web/start_full_webui.py
"""

import os
import sys
import subprocess
import threading
import time
from pathlib import Path

def start_backend(web_dir):
    """Start the FastAPI backend server"""
    print("Starting backend server...")

    # Change to web directory for uvicorn to find backend.main
    # (UV will use the main project environment automatically)
    os.chdir(web_dir)
    
    cmd = [
        "uv", "run", "uvicorn", 
        "backend.main:app", 
        "--reload", 
        "--host", "127.0.0.1", 
        "--port", "8000"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Backend server failed: {e}")
    except KeyboardInterrupt:
        print("🛑 Backend server stopped")

def start_frontend(web_dir):
    """Start the frontend HTTP server"""
    print("Starting frontend server...")

    # Change to frontend directory
    # (UV will use the main project environment automatically)
    frontend_dir = web_dir / "frontend"
    os.chdir(frontend_dir)
    
    cmd = [
        "uv", "run", "python", "-m", "http.server", "3000"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Frontend server failed: {e}")
    except KeyboardInterrupt:
        print("🛑 Frontend server stopped")

def main():
    """Start both backend and frontend servers"""
    
    # Get directories
    web_dir = Path(__file__).parent.absolute()
    project_root = web_dir.parent
    
    print("Starting SpigaMonde Complete Web UI...")
    print(f"Project root: {project_root}")
    print(f"Web directory: {web_dir}")
    print()

    # Verify backend exists
    backend_script = web_dir / "backend" / "main.py"
    if not backend_script.exists():
        print(f"ERROR: Backend script not found at {backend_script}")
        sys.exit(1)

    # Verify frontend exists
    frontend_index = web_dir / "frontend" / "index.html"
    if not frontend_index.exists():
        print(f"ERROR: Frontend index not found at {frontend_index}")
        sys.exit(1)

    print("All components found")
    print()
    print("Starting servers...")
    print("   Backend: http://localhost:8000")
    print("   Frontend: http://localhost:3000")
    print("   API Docs: http://localhost:8000/docs")
    print()
    print("Press Ctrl+C to stop both servers")
    print("-" * 60)
    
    try:
        # Start backend in a separate thread
        backend_thread = threading.Thread(
            target=start_backend, 
            args=(web_dir,), 
            daemon=True
        )
        backend_thread.start()
        
        # Give backend time to start
        time.sleep(3)
        
        # Start frontend in main thread (so Ctrl+C works)
        start_frontend(web_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping SpigaMonde Web UI...")
        print("✅ Both servers stopped")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting Web UI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
