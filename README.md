# SpigaUI - Modern Web Interface for SpigaMonde

A clean, modern web interface for the SpigaMonde web crawler built with FastAPI, Celery, and React/Vue.

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Node.js 18+ (for frontend)
- Redis (for Celery backend)
- UV package manager

### Backend Setup

```bash
# Install UV if not already installed
pip install uv

# Create virtual environment and install dependencies
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uv pip install -e .

# Install development dependencies
uv pip install -e ".[dev]"

# Start Redis (required for Celery)
redis-server

# Start the backend server
uv run uvicorn spigaui.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Setup

```bash
cd frontend
npm install
npm run dev
```

## 🏗️ Architecture

### Backend (FastAPI + Celery)
- **API Server**: FastAPI with async support
- **Task Queue**: Celery for background crawl jobs
- **Message Broker**: Redis for task queuing and real-time updates
- **Real-time Updates**: Server-Sent Events (SSE) for progress tracking

### Frontend (React/Vue + TypeScript)
- **Framework**: React or Vue 3 with TypeScript
- **State Management**: Redux Toolkit or Pinia
- **Styling**: Tailwind CSS or similar
- **Build Tool**: Vite for fast development

### Integration
- **SpigaMonde Core**: Imported as library dependency
- **API-First**: Clean separation between UI and crawler logic
- **Type Safety**: Full TypeScript coverage with Pydantic models

## 📁 Project Structure

```
SpigaUI/
├── spigaui/              # Backend Python package
│   ├── api/              # FastAPI routes
│   ├── core/             # Business logic
│   ├── models/           # Pydantic models
│   ├── services/         # Service layer
│   └── workers/          # Celery tasks
├── frontend/             # Frontend application
│   ├── src/
│   │   ├── components/   # Reusable components
│   │   ├── pages/        # Page components
│   │   ├── services/     # API clients
│   │   └── types/        # TypeScript definitions
│   └── public/
├── tests/                # Test suite
├── docker/               # Docker configuration
└── docs/                 # Documentation
```

## 🔧 Development

### Code Quality
```bash
# Format code
uv run black spigaui tests
uv run isort spigaui tests

# Lint code
uv run flake8 spigaui tests
uv run mypy spigaui

# Run tests
uv run pytest
```

### Environment Variables
Create a `.env` file:
```env
# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# SpigaMonde Configuration
SPIGAMONDE_DATABASE_URL=sqlite:///spigamonde.db

# Development Settings
DEBUG=true
LOG_LEVEL=INFO
```

## 🚀 Deployment

### Docker Compose
```bash
docker-compose up -d
```

### Manual Deployment
1. Set up Redis server
2. Configure environment variables
3. Run database migrations
4. Start Celery workers
5. Start FastAPI server
6. Build and serve frontend

## 📚 API Documentation

Once the server is running, visit:
- **API Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
