# SpigaMonde

A powerful web spider for searching and cataloging various file types and content using Python's Scrapy library.

## Features

### Core Crawling (Phase 1) ✅
- **CLI-First Design**: Robust command-line interface for all operations
- **File Type Detection**: Automatic detection and classification of various file types
- **SQLite Database**: Stores metadata about discovered content and crawl sessions
- **Configurable Crawling**: Customizable depth, delays, file types, and size limits
- **Content Deduplication**: Prevents downloading duplicate files using content hashing
- **Rich CLI Output**: Beautiful terminal output with tables and progress indicators

### Enhanced Monitoring (Phase 2) ✅
- **Real-time Dashboard**: Live monitoring with Rich terminal UI
- **Performance Metrics**: Comprehensive timing and success rate tracking
- **Alerting System**: Configurable alerts with auto-resolution
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **CLI Monitoring**: Status, metrics, and alerts commands

### Content Analysis (Phase 3) ✅
- **Smart Classification**: 22-category content classification with confidence scoring
- **Quality Assessment**: 5-level quality scoring with detailed metrics
- **Metadata Extraction**: Title, author, language, keywords, and entity extraction
- **Performance Tracking**: Sub-5ms analysis times with full monitoring
- **Database Integration**: Rich analysis results storage and relationships

### Web Interface (Phase 4) 🚧
- **FastAPI Backend**: RESTful API with real-time WebSocket support
- **Responsive Frontend**: Modern web interface for monitoring and control
- **Real-time Updates**: Live crawl monitoring and statistics
- **API Integration**: Full access to SpigaMonde functionality via web interface

### Architecture
- **Extensible Design**: Modular architecture ready for AI and web interface features
- **Production Ready**: Comprehensive testing, monitoring, and error handling
- **Scalable Foundation**: Thread-safe operations with efficient resource management

## Configuration Modes

SpigaMonde supports easy switching between testing and production configurations:

### 🧪 Testing Mode
Use anonymized user-agent and conservative settings for testing:
```bash
python scripts/switch_config.py testing
```
- **User-Agent**: `TestCrawler/1.0` (anonymized)
- **Downloads**: `./test_downloads/` (separate directory)
- **Limits**: Conservative (max 20 pages, 10MB files)
- **Purpose**: Safe testing without identifying as SpigaMonde

### 🚀 Production Mode
Use official SpigaMonde identification and full settings:
```bash
python scripts/switch_config.py production
```
- **User-Agent**: `SpigaMonde/0.1.0` (official)
- **Downloads**: `./downloads/` (production directory)
- **Limits**: Full production settings (unlimited pages, 100MB files)
- **Purpose**: Official crawling with SpigaMonde identification

### 📊 Check Current Mode
```bash
python scripts/switch_config.py status
```

## Logging and Monitoring

SpigaMonde provides comprehensive logging across all components:

### 🕷️ Spider Logs
**Console Output** (default):
- All spider operations logged to console with timestamps
- Visible when running `spiga crawl <URL>` commands
- Rich formatting with color-coded log levels

**Manual Log Capture**:
```bash
# Capture all spider logs to file
spiga crawl <URL> 2>&1 | tee logs/crawl_$(date +%Y%m%d_%H%M%S).log

# Capture only errors
spiga crawl <URL> 2> logs/errors_$(date +%Y%m%d_%H%M%S).log
```

### 🌐 Web Interface Logs
**Backend API Logs**:
- **Console Output**: Real-time logs when running web backend
- **File Logs**: Persistent logs in `web/logs/` directory (see File Locations below)
- **Start Backend**: `cd web && uv run uvicorn backend.main:app --reload --port 8000`
- **Content**: API requests, database operations, SpigaMonde integration

**Frontend Logs**:
- **Browser Console**: All frontend actions and API calls
- **Activity Log**: Real-time log viewer in web interface
- **Network Tab**: API request/response details in browser dev tools

**Web Interface File Logging** ✅:
- **Main Log**: `web/logs/spigamonde.log` - All web interface activity
- **Error Log**: `web/logs/spigamonde_errors.log` - Error-level logs only
- **Performance Log**: `web/logs/spigamonde_performance.log` - Performance metrics
- **Format**: Structured JSON logs with timestamps and correlation IDs

### 📁 Log File Locations
```
logs/                           # CLI and manual log captures
├── crawl_YYYYMMDD_HHMMSS.log  # Spider crawl logs (manual capture)
├── errors_YYYYMMDD_HHMMSS.log # Error-only logs (manual capture)
└── spigamonde_manual.log      # Continuous manual logging

web/logs/                       # Web interface persistent logs ✅
├── spigamonde.log             # All web interface activity (JSON format)
├── spigamonde_errors.log      # Error-level logs only
└── spigamonde_performance.log # Performance metrics and timing

web/frontend/                   # Web interface browser logs
├── Browser Console            # Frontend JavaScript logs
└── Network Tab               # API request logs

Terminal Output                 # Real-time logs
├── Spider operations          # spiga commands
└── Web backend               # FastAPI server logs
```

**Note**: Web interface scripts and API operations log to `web/logs/` since the web backend runs from the `web/` directory. CLI operations can be manually captured to the root `logs/` directory.

### 🔍 Log Analysis
```bash
# Search spider logs for errors
spiga crawl <URL> 2>&1 | grep -E "(ERROR|CRITICAL)"

# Monitor web backend in real-time
cd web && uv run uvicorn backend.main:app --reload | grep -E "(INFO|ERROR)"

# View recent database activity
spiga stats  # Shows recent crawl statistics
```

**📚 Detailed Logging Guide**: See `docs/logging-guide.md` for comprehensive logging documentation and troubleshooting.

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd SpigaMonde
```

2. Install dependencies:
```bash
pip install -e .
```

3. Initialize the database:
```bash
spiga init
```

## Quick Start

1. **Start a basic crawl**:
```bash
spiga crawl https://example.com
```

2. **Crawl with custom settings**:
```bash
spiga crawl https://example.com --depth 2 --max-pages 100 --file-types pdf,doc,jpg
```

3. **List discovered content**:
```bash
spiga list-content --limit 20
```

4. **View crawling statistics**:
```bash
spiga stats
```

## CLI Commands

### `spiga init`
Initialize or reset the database.

Options:
- `--reset`: Drop all tables and recreate them

### `spiga crawl <urls>`
Start crawling the specified URLs.

Options:
- `--depth, -d`: Maximum crawl depth (default: 3)
- `--max-pages, -p`: Maximum pages to crawl
- `--delay, -t`: Download delay in seconds (default: 1.0)
- `--output-dir, -o`: Output directory for downloads
- `--file-types, -f`: Comma-separated list of allowed file types
- `--concurrent, -c`: Number of concurrent requests (default: 16)

Example:
```bash
spiga crawl https://example.com https://test.com --depth 2 --file-types pdf,doc,jpg --output-dir ./downloads
```

### `spiga list-content`
List discovered content.

Options:
- `--limit, -l`: Number of results to show (default: 50)
- `--content-type, -t`: Filter by content type (document, image, video, etc.)
- `--status, -s`: Filter by status (pending, completed, failed, etc.)
- `--downloaded`: Show only downloaded content

### `spiga list-sessions`
List crawl sessions.

Options:
- `--limit, -l`: Number of sessions to show (default: 20)

### `spiga stats`
Show crawling statistics including total URLs, content counts, and breakdowns by type and status.

### `spiga show-content <id>`
Show detailed information about specific content by ID.

### `spiga config`
Show current configuration settings.

## Web Interface Usage

SpigaMonde includes a modern web interface for monitoring and controlling crawl operations:

### 🚀 Starting the Web Interface

**Quick Start:**
```bash
# CRITICAL: Always use 'uv run' to avoid virtual environment issues

# Terminal 1: Start Backend
cd web
uv run uvicorn backend.main:app --reload --host 127.0.0.1 --port 8000

# Terminal 2: Start Frontend
cd web/frontend
uv run python -m http.server 3000

# Open: http://localhost:3000
```

**⚠️ Important**: The web interface must run in the same virtual environment as SpigaMonde. Using regular `python` or `uvicorn` commands will cause script execution failures.

**📚 Detailed Instructions**: See `web/README.md` for complete setup guide, troubleshooting, and development instructions.

### 🌐 Web Interface Features

**Dashboard**:
- Real-time SpigaMonde statistics
- Database connection status
- Content and analysis counts

**API Testing**:
- Test backend connectivity
- Fetch live SpigaMonde data
- Real-time activity logging

**Monitoring**:
- Live activity log with timestamps
- API request/response monitoring
- Error handling and display

### 🔧 Web Interface Development

**Backend Development**:
```bash
cd web
uv sync                    # Install dependencies
uv run python backend/main.py  # Alternative start method
```

**Frontend Development**:
- Pure HTML/CSS/JavaScript (no build process)
- Real-time API communication
- Responsive design for mobile/desktop

**API Endpoints**:
- `GET /api/test` - Connection test with database stats
- `GET /api/spiga/stats` - Live SpigaMonde statistics
- `GET /api/health` - Health check endpoint
- `GET /docs` - Interactive API documentation

## Configuration

SpigaMonde uses environment variables and `.env` files for configuration. Copy `.env.example` to `.env` and modify as needed.

### Key Configuration Options

- `DATABASE_URL`: Database connection string (default: sqlite:///spigamonde.db)
- `SPIDER_MAX_DEPTH`: Maximum crawl depth (default: 3)
- `SPIDER_DOWNLOAD_DELAY`: Delay between requests in seconds (default: 1.0)
- `SPIDER_CONCURRENT_REQUESTS`: Number of concurrent requests (default: 16)
- `SPIDER_MAX_FILE_SIZE`: Maximum file size to download in bytes (default: 100MB)
- `SPIDER_ALLOWED_FILE_TYPES`: Comma-separated list of allowed file extensions
- `STORAGE_BASE_PATH`: Base directory for downloaded files (default: ./downloads)

## File Organization

Downloaded files are automatically organized in the storage directory:

```
downloads/
├── 2024/
│   └── 08/
│       └── 24/
│           ├── document/
│           │   ├── report.pdf
│           │   └── manual.doc
│           ├── image/
│           │   ├── photo.jpg
│           │   └── diagram.png
│           └── video/
│               └── tutorial.mp4
```

Organization can be controlled via:
- `STORAGE_ORGANIZE_BY_DATE`: Organize by date (year/month/day)
- `STORAGE_ORGANIZE_BY_TYPE`: Organize by content type
- `STORAGE_ORGANIZE_BY_DOMAIN`: Organize by source domain

## Supported File Types

SpigaMonde can detect and download various file types:

- **Documents**: PDF, DOC, DOCX, TXT, RTF, ODT
- **Spreadsheets**: XLS, XLSX, CSV, ODS
- **Presentations**: PPT, PPTX, ODP
- **Images**: JPG, JPEG, PNG, GIF, BMP, SVG, WebP
- **Audio**: MP3, WAV, FLAC, OGG, M4A
- **Video**: MP4, AVI, MKV, MOV, WMV, FLV, WebM
- **Archives**: ZIP, RAR, 7Z, TAR, GZ, BZ2
- **Web/Data**: HTML, HTM, XML, JSON, YAML, YML

## Development

### Running Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black spigamonde/
isort spigamonde/
```

### Type Checking

```bash
mypy spigamonde/
```

## Architecture

SpigaMonde follows a modular architecture:

- **CLI Layer**: Click-based command-line interface
- **Spider Layer**: Scrapy-based web crawling engine
- **Database Layer**: SQLAlchemy models with SQLite backend
- **Utils Layer**: File detection, content processing, and utilities
- **Config Layer**: Pydantic-based configuration management

## Quick Reference

### 🚀 Essential Commands
```bash
# Configuration
python scripts/switch_config.py testing     # Switch to testing mode
python scripts/switch_config.py production  # Switch to production mode
python scripts/switch_config.py status      # Check current mode

# Basic Crawling
spiga crawl https://example.com              # Basic crawl
spiga crawl <URL> --depth 2 --max-pages 50  # Limited crawl
spiga stats                                  # Show statistics
spiga list-content --limit 20                # List recent content

# Web Interface (IMPORTANT: Use 'uv run' to avoid environment issues)
cd web && uv run uvicorn backend.main:app --reload --port 8000  # Start backend
cd web/frontend && uv run python -m http.server 3000           # Start frontend
# Open: http://localhost:3000

# Logging
spiga crawl <URL> 2>&1 | tee logs/crawl.log  # Capture spider logs
# Web logs: Browser console + backend terminal
```

### 📁 Key Directories
```
SpigaMonde/
├── downloads/          # Production downloads
├── test_downloads/     # Testing downloads
├── logs/              # Manual log captures
├── web/               # Web interface
│   ├── backend/       # FastAPI backend
│   └── frontend/      # HTML/CSS/JS frontend
├── docs/              # Documentation
└── scripts/           # Utility scripts
```

### 🔗 Important URLs
- **Web Interface**: http://localhost:3000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health

## Future Enhancements

### 🚧 In Progress
- **Advanced Web Interface**: Widget system, real-time monitoring, crawl management
- **WebSocket Integration**: Live updates and real-time communication

### 🎯 Planned
- **AI/LLM Integration**: Enhanced content analysis and classification
- **Advanced Dashboard**: Charts, analytics, and reporting
- **User Management**: Authentication and multi-user support
- **PostgreSQL Support**: Scalable database backend for larger datasets
- **Elasticsearch Integration**: Advanced search and indexing
- **Docker Containerization**: Easy deployment and scaling
- **Distributed Crawling**: Multi-node crawling support
- **API Rate Limiting**: Production-ready API security

## License

[Add your license here]

## Contributing

[Add contribution guidelines here]