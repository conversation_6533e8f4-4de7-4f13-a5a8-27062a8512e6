#!/usr/bin/env python3
"""
Simple Economic News Aggregator: Asia & Latin America
Focused crawler for economic news using reliable RSS feeds.

Usage:
    python simple_economic_news.py
    
Features:
    - Reliable RSS feeds for economic news
    - Focus on Asian and Latin American markets
    - Economic keyword filtering
    - Professional HTML dashboard
"""

import sys
import os
import json
import re
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content
from rich.console import Console
from rich.panel import Panel
from rich.table import Table


def main():
    """Execute simple economic news aggregation."""
    console = Console()
    
    console.print(Panel.fit(
        "[bold green]📈 Economic News: Asia & Latin America[/bold green]\n"
        "Simple and reliable economic news aggregation",
        border_style="green"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Reliable economic RSS feeds (tested and working)
    economic_sources = [
        # Working Asian sources
        "https://asia.nikkei.com/rss/feed/nar",           # Nikkei Asia
        "https://www.scmp.com/rss/91/feed",               # SCMP Business
        "https://asiatimes.com/feed/",                    # Asia Times
        
        # Working general economic sources with regional coverage
        "https://feeds.bloomberg.com/markets/news.rss",   # Bloomberg Markets
        
        # Alternative sources
        "https://rss.cnn.com/rss/money_latest.rss",      # CNN Money
        "https://feeds.bbci.co.uk/news/business/rss.xml", # BBC Business
    ]
    
    console.print(f"[cyan]Crawling {len(economic_sources)} reliable economic sources...[/cyan]")
    
    # Simple crawl configuration
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Simple Economic News 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.0,
        'CONCURRENT_REQUESTS': 2,
        'DEPTH_LIMIT': 1,
        'CLOSESPIDER_PAGECOUNT': 30,
        'LOG_LEVEL': 'INFO',
        'ALLOWED_FILE_TYPES': ['xml', 'rss', 'html'],
    }
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=economic_sources)
        process.start()
        
        console.print("[green]✓ Economic sources crawled![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Extract and filter economic headlines
    console.print("\n[bold cyan]Extracting economic headlines...[/bold cyan]")
    headlines = extract_and_filter_headlines(console)
    
    if headlines:
        html_file = generate_economic_dashboard(headlines, console)
        console.print(f"[green]✓ Economic dashboard created: {html_file}[/green]")
        console.print(f"[cyan]Open in browser: file://{html_file.absolute()}[/cyan]")
        
        # Show summary
        show_summary(headlines, console)
    else:
        console.print("[yellow]No economic headlines found. Showing all available content...[/yellow]")
        show_all_content(console)
    
    return 0


def extract_and_filter_headlines(console):
    """Extract headlines and filter for economic/regional relevance."""
    all_headlines = []
    
    # Economic and regional keywords
    economic_keywords = [
        'economy', 'economic', 'market', 'trade', 'business', 'financial', 'finance',
        'gdp', 'inflation', 'investment', 'banking', 'currency', 'stock', 'bond',
        'central bank', 'monetary', 'fiscal', 'growth', 'development', 'export', 'import'
    ]
    
    regional_keywords = [
        # Asia
        'asia', 'asian', 'china', 'chinese', 'japan', 'japanese', 'korea', 'korean',
        'india', 'singapore', 'hong kong', 'taiwan', 'thailand', 'vietnam', 'indonesia',
        
        # Latin America  
        'latin america', 'brazil', 'mexico', 'argentina', 'chile', 'colombia', 'peru'
    ]
    
    with session_scope() as session:
        # Get the most recent content (from latest crawl)
        recent_content = session.query(Content).order_by(Content.created_at.desc()).limit(50).all()
        
        console.print(f"Processing {len(recent_content)} recent items...")
        
        for content in recent_content:
            if content.local_path and os.path.exists(content.local_path):
                try:
                    with open(content.local_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content_text = f.read()
                    
                    # Extract headlines
                    if is_rss_content(content_text, content.url):
                        headlines = extract_from_rss(content_text, content.url)
                    else:
                        headlines = extract_from_html(content_text, content.url)
                    
                    # Filter for relevance (more lenient)
                    for headline in headlines:
                        title_lower = headline.get('title', '').lower()
                        desc_lower = headline.get('description', '').lower()
                        combined = f"{title_lower} {desc_lower}"
                        
                        # Check for economic OR regional keywords (OR logic, not AND)
                        has_economic = any(keyword in combined for keyword in economic_keywords)
                        has_regional = any(keyword in combined for keyword in regional_keywords)
                        is_from_economic_source = any(domain in content.url.lower() 
                                                    for domain in ['nikkei', 'scmp', 'bloomberg', 'business', 'money'])
                        
                        # Include if ANY condition is met
                        if has_economic or has_regional or is_from_economic_source:
                            headline['relevance'] = 'economic' if has_economic else 'regional' if has_regional else 'source'
                            headline['region'] = detect_region(combined)
                            all_headlines.append(headline)
                        
                except Exception as e:
                    console.print(f"[yellow]Warning: Could not process {content.url}: {e}[/yellow]")
    
    console.print(f"[green]✓ Found {len(all_headlines)} relevant headlines[/green]")
    return all_headlines


def is_rss_content(content, url):
    """Check if content is RSS/XML."""
    return ('rss' in url.lower() or 
            'feed' in url.lower() or 
            '<rss' in content.lower() or 
            '<feed' in content.lower() or
            '<?xml' in content.lower())


def detect_region(text):
    """Detect region from text."""
    asia_keywords = ['asia', 'china', 'japan', 'korea', 'india', 'singapore', 'hong kong']
    latam_keywords = ['latin america', 'brazil', 'mexico', 'argentina', 'chile']
    
    asia_count = sum(1 for keyword in asia_keywords if keyword in text)
    latam_count = sum(1 for keyword in latam_keywords if keyword in text)
    
    if asia_count > latam_count:
        return 'Asia'
    elif latam_count > 0:
        return 'Latin America'
    else:
        return 'Global'


def extract_from_rss(rss_content, url):
    """Extract headlines from RSS content."""
    headlines = []
    source_name = get_source_name(url)
    
    # Find RSS items
    items = re.findall(r'<item[^>]*>(.*?)</item>', rss_content, re.DOTALL | re.IGNORECASE)
    
    for item in items:
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', item, re.IGNORECASE)
        if title_match:
            title = clean_text(title_match.group(1))
            
            link_match = re.search(r'<link[^>]*>([^<]+)</link>', item, re.IGNORECASE)
            link = link_match.group(1) if link_match else url
            
            desc_match = re.search(r'<description[^>]*>([^<]+)</description>', item, re.IGNORECASE)
            description = clean_text(desc_match.group(1)) if desc_match else ""
            
            if title and len(title) > 10:
                headlines.append({
                    'title': title,
                    'link': link,
                    'description': description[:200] + "..." if len(description) > 200 else description,
                    'source': source_name,
                    'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
    
    return headlines


def extract_from_html(html_content, url):
    """Extract headlines from HTML content."""
    headlines = []
    source_name = get_source_name(url)
    
    # Extract title
    title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
    if title_match:
        title = clean_text(title_match.group(1))
        if title and len(title) > 10:
            headlines.append({
                'title': title,
                'link': url,
                'description': "",
                'source': source_name,
                'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    return headlines


def get_source_name(url):
    """Get friendly source name."""
    domain = urlparse(url).netloc.lower()
    
    source_map = {
        'asia.nikkei.com': 'Nikkei Asia',
        'scmp.com': 'South China Morning Post',
        'asiatimes.com': 'Asia Times',
        'bloomberg.com': 'Bloomberg',
        'cnn.com': 'CNN Money',
        'bbc.co.uk': 'BBC Business',
    }
    
    for domain_part, name in source_map.items():
        if domain_part in domain:
            return name
    
    return domain.replace('www.', '').title()


def clean_text(text):
    """Clean HTML and entities from text."""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Decode entities
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    text = text.replace('&#39;', "'")
    text = text.replace('&nbsp;', ' ')
    
    return text.strip()


def show_summary(headlines, console):
    """Show summary of extracted headlines."""
    
    by_source = {}
    by_region = {}
    
    for headline in headlines:
        source = headline.get('source', 'Unknown')
        region = headline.get('region', 'Global')
        
        by_source[source] = by_source.get(source, 0) + 1
        by_region[region] = by_region.get(region, 0) + 1
    
    # Source summary
    console.print(f"\n[bold]Headlines by Source:[/bold]")
    for source, count in sorted(by_source.items(), key=lambda x: x[1], reverse=True):
        console.print(f"  📰 {source}: {count}")
    
    # Region summary
    console.print(f"\n[bold]Headlines by Region:[/bold]")
    for region, count in sorted(by_region.items(), key=lambda x: x[1], reverse=True):
        console.print(f"  🌏 {region}: {count}")


def show_all_content(console):
    """Show all available content for debugging."""
    with session_scope() as session:
        recent_content = session.query(Content).order_by(Content.created_at.desc()).limit(10).all()
        
        console.print(f"\n[bold]Recent Content ({len(recent_content)} items):[/bold]")
        for content in recent_content:
            console.print(f"  📄 {content.filename} - {content.url}")


def generate_economic_dashboard(headlines, console):
    """Generate simple economic dashboard."""
    
    # Sort by extraction time
    sorted_headlines = sorted(headlines, key=lambda x: x.get('extracted_at', ''), reverse=True)
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Economic News: Asia & Latin America</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
        }}
        .headlines {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }}
        .headline {{
            border-bottom: 1px solid #eee;
            padding: 20px 0;
        }}
        .headline:last-child {{
            border-bottom: none;
        }}
        .headline-title {{
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }}
        .headline-meta {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 8px;
        }}
        .headline-description {{
            color: #555;
            font-size: 0.95em;
        }}
        .region-tag {{
            background: #28a745;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
        .source-tag {{
            background: #6c757d;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 Economic News Dashboard</h1>
        <h2>Asia & Latin America Focus</h2>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <div class="stat-number">{len(headlines)}</div>
            <div>Economic Headlines</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(set(h.get('source', 'Unknown') for h in headlines))}</div>
            <div>News Sources</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(set(h.get('region', 'Global') for h in headlines))}</div>
            <div>Regions</div>
        </div>
    </div>
    
    <div class="headlines">
        <h2>📊 Economic Headlines</h2>
"""
    
    # Add headlines
    for headline in sorted_headlines:
        title = headline.get('title', 'No title')
        source = headline.get('source', 'Unknown')
        region = headline.get('region', 'Global')
        description = headline.get('description', '')
        link = headline.get('link', '#')
        
        html_content += f"""
        <div class="headline">
            <div class="headline-title">
                <a href="{link}" target="_blank" style="text-decoration: none; color: inherit;">
                    {title}
                </a>
            </div>
            <div class="headline-meta">
                <span class="region-tag">{region}</span>
                <span class="source-tag">{source}</span>
            </div>
            {f'<div class="headline-description">{description}</div>' if description else ''}
        </div>
        """
    
    html_content += """
    </div>
    
    <div class="footer">
        <p>🔄 Refresh for latest economic news</p>
        <p>Powered by SpigaMonde Economic News Aggregator</p>
    </div>
</body>
</html>
"""
    
    # Write HTML file
    output_dir = Path(__file__).parent / 'output'
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    html_file = output_dir / f'economic_news_{timestamp}.html'
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # Also create latest file
    latest_file = output_dir / 'latest_economic_news.html'
    with open(latest_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_file


if __name__ == "__main__":
    sys.exit(main())
