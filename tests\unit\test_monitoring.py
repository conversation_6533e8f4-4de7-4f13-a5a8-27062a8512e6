"""Tests for monitoring and logging functionality."""

import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest

from spigamonde.monitoring.logger import StructuredLogger, log_context, log_performance
from spigamonde.monitoring.metrics import <PERSON>ricsCollector, PerformanceTimer
from spigamonde.monitoring.alerts import <PERSON><PERSON><PERSON><PERSON><PERSON>, Alert<PERSON>ule, AlertSeverity


class TestStructuredLogger:
    """Test structured logging functionality."""
    
    def test_logger_initialization(self):
        """Test logger initializes correctly."""
        logger = StructuredLogger()
        assert logger is not None
        assert logger.settings is not None
    
    def test_log_context(self):
        """Test logging context manager."""
        with log_context(correlation_id_val="test-123", session_id_val="session-456") as correlation_id:
            assert correlation_id == "test-123"
    
    def test_log_performance_decorator(self):
        """Test performance logging decorator."""
        @log_performance("test_operation")
        def test_function():
            time.sleep(0.01)  # Small delay
            return "success"
        
        result = test_function()
        assert result == "success"


class TestMetricsCollector:
    """Test metrics collection functionality."""
    
    def setup_method(self):
        """Setup test metrics collector."""
        self.metrics = MetricsCollector()
    
    def test_record_metric(self):
        """Test recording a metric."""
        self.metrics.record_metric("test_metric", 42.0, {"label": "value"})
        
        metrics = self.metrics.get_metrics("test_metric")
        assert "test_metric" in metrics
        assert len(metrics["test_metric"]) == 1
        assert metrics["test_metric"][0].value == 42.0
    
    def test_increment_counter(self):
        """Test incrementing a counter."""
        self.metrics.increment_counter("test_counter", 5)
        self.metrics.increment_counter("test_counter", 3)
        
        counters = self.metrics.get_counters()
        assert "test_counter" in counters
        assert counters["test_counter"] == 8
    
    def test_set_gauge(self):
        """Test setting a gauge value."""
        self.metrics.set_gauge("test_gauge", 123.45)
        
        gauges = self.metrics.get_gauges()
        assert "test_gauge" in gauges
        assert gauges["test_gauge"] == 123.45
    
    def test_record_performance(self):
        """Test recording performance metrics."""
        self.metrics.record_performance("test_op", 1.5, success=True)
        self.metrics.record_performance("test_op", 2.0, success=False)
        
        stats = self.metrics.get_performance_stats("test_op")
        assert "test_op" in stats
        
        op_stats = stats["test_op"]
        assert op_stats.count == 2
        assert op_stats.success_count == 1
        assert op_stats.error_count == 1
        assert op_stats.avg_duration == 1.75
    
    def test_performance_timer(self):
        """Test performance timer context manager."""
        with PerformanceTimer("timer_test") as timer:
            time.sleep(0.01)
        
        assert timer.duration is not None
        assert timer.duration > 0
        assert timer.success is True
        
        # Check that metrics were recorded
        stats = self.metrics.get_performance_stats("timer_test")
        assert "timer_test" in stats
    
    def test_get_summary(self):
        """Test getting metrics summary."""
        self.metrics.record_performance("op1", 1.0, True)
        self.metrics.increment_counter("counter1", 5)
        self.metrics.set_gauge("gauge1", 10.5)
        
        summary = self.metrics.get_summary()
        
        assert "performance_stats" in summary
        assert "counters" in summary
        assert "gauges" in summary
        assert "metric_counts" in summary
        
        assert "op1" in summary["performance_stats"]
        assert "counter1" in summary["counters"]
        assert "gauge1" in summary["gauges"]


class TestAlertManager:
    """Test alert management functionality."""
    
    def setup_method(self):
        """Setup test alert manager."""
        self.alert_manager = AlertManager()
    
    def test_add_rule(self):
        """Test adding an alert rule."""
        rule = AlertRule(
            name="test_rule",
            condition=lambda: True,
            severity=AlertSeverity.WARNING,
            message_template="Test alert message"
        )
        
        self.alert_manager.add_rule(rule)
        assert "test_rule" in self.alert_manager._rules
    
    def test_remove_rule(self):
        """Test removing an alert rule."""
        rule = AlertRule(
            name="test_rule",
            condition=lambda: True,
            severity=AlertSeverity.WARNING,
            message_template="Test alert message"
        )
        
        self.alert_manager.add_rule(rule)
        self.alert_manager.remove_rule("test_rule")
        assert "test_rule" not in self.alert_manager._rules
    
    def test_trigger_alert(self):
        """Test triggering an alert."""
        triggered = False
        
        def test_condition():
            return triggered
        
        rule = AlertRule(
            name="test_alert",
            condition=test_condition,
            severity=AlertSeverity.ERROR,
            message_template="Test alert triggered"
        )
        
        self.alert_manager.add_rule(rule)
        
        # Initially no alerts
        active_alerts = self.alert_manager.get_active_alerts()
        assert len(active_alerts) == 0
        
        # Trigger condition
        triggered = True
        self.alert_manager._check_all_rules()
        
        # Should have active alert
        active_alerts = self.alert_manager.get_active_alerts()
        assert len(active_alerts) == 1
        assert active_alerts[0].name == "test_alert"
    
    def test_resolve_alert(self):
        """Test resolving an alert."""
        triggered = True
        
        def test_condition():
            return triggered
        
        def resolve_condition():
            return not triggered
        
        rule = AlertRule(
            name="test_alert",
            condition=test_condition,
            severity=AlertSeverity.ERROR,
            message_template="Test alert triggered",
            auto_resolve=True,
            resolve_condition=resolve_condition,
            cooldown_minutes=0  # No cooldown for testing
        )
        
        self.alert_manager.add_rule(rule)
        
        # Trigger alert
        self.alert_manager._check_all_rules()
        assert len(self.alert_manager.get_active_alerts()) == 1
        
        # Resolve condition
        triggered = False
        self.alert_manager._check_all_rules()
        assert len(self.alert_manager.get_active_alerts()) == 0
    
    def test_acknowledge_alert(self):
        """Test acknowledging an alert."""
        rule = AlertRule(
            name="test_alert",
            condition=lambda: True,
            severity=AlertSeverity.WARNING,
            message_template="Test alert"
        )
        
        self.alert_manager.add_rule(rule)
        self.alert_manager._check_all_rules()
        
        # Acknowledge the alert
        self.alert_manager.acknowledge_alert("test_alert")
        
        active_alerts = self.alert_manager.get_active_alerts()
        assert len(active_alerts) == 1
        assert active_alerts[0].status.value == "acknowledged"
    
    def test_alert_summary(self):
        """Test getting alert summary."""
        rule = AlertRule(
            name="test_alert",
            condition=lambda: True,
            severity=AlertSeverity.CRITICAL,
            message_template="Test alert"
        )
        
        self.alert_manager.add_rule(rule)
        self.alert_manager._check_all_rules()
        
        summary = self.alert_manager.get_alert_summary()
        
        assert "active_alerts" in summary
        assert "active_by_severity" in summary
        assert "total_rules" in summary
        assert summary["active_alerts"] == 1
        assert summary["total_rules"] == 1
        assert "critical" in summary["active_by_severity"]


class TestMonitoringIntegration:
    """Test integration between monitoring components."""
    
    def test_metrics_and_alerts_integration(self):
        """Test that metrics can trigger alerts."""
        metrics = MetricsCollector()
        alert_manager = AlertManager()
        
        # Create an alert rule based on metrics
        def high_error_rate():
            stats = metrics.get_performance_stats()
            for operation, perf_stats in stats.items():
                if perf_stats and perf_stats.count >= 2:
                    error_rate = perf_stats.error_count / perf_stats.count
                    if error_rate > 0.5:  # 50% error rate
                        return True
            return False
        
        rule = AlertRule(
            name="high_error_rate",
            condition=high_error_rate,
            severity=AlertSeverity.ERROR,
            message_template="High error rate detected"
        )
        
        alert_manager.add_rule(rule)
        
        # Record some metrics with high error rate
        metrics.record_performance("test_operation", 1.0, success=False)
        metrics.record_performance("test_operation", 1.5, success=False)
        
        # Check alerts
        alert_manager._check_all_rules()
        
        active_alerts = alert_manager.get_active_alerts()
        assert len(active_alerts) == 1
        assert active_alerts[0].name == "high_error_rate"
    
    @patch('spigamonde.monitoring.logger.logger')
    def test_structured_logging_with_context(self, mock_logger):
        """Test structured logging with context."""
        structured_logger = StructuredLogger()
        
        with log_context(correlation_id_val="test-123", session_id_val="session-456"):
            structured_logger.info("Test message", extra_field="extra_value")
        
        # Verify logger was called
        assert mock_logger.bind.called
        assert mock_logger.bind.return_value.info.called
