#!/usr/bin/env python3
"""
Economic News Aggregator: Asia & Latin America
Specialized crawler for economic and business news from Asian and Latin American markets.

Usage:
    python economic_news_asia_latam.py
    
Features:
    - Economic news from major Asian and Latin American sources
    - Business and financial market coverage
    - Regional economic trend analysis
    - Professional HTML dashboard with economic focus
    - Export options for further analysis
"""

import sys
import os
import json
import re
from pathlib import Path
from datetime import datetime
from urllib.parse import urlparse

# Add SpigaMonde to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from scrapy.crawler import CrawlerProcess
from spigamonde.spiders.content_spider import ContentSpider
from spigamonde.config.settings import get_settings
from spigamonde.database.connection import init_database, session_scope
from spigamonde.models.content import Content
from rich.console import Console
from rich.panel import Panel
from rich.table import Table


def main():
    """Execute economic news aggregation for Asia and Latin America."""
    console = Console()
    
    console.print(Panel.fit(
        "[bold green]📈 Economic News: Asia & Latin America[/bold green]\n"
        "Business and financial market coverage from key regions",
        border_style="green"
    ))
    
    # Initialize database
    try:
        init_database()
        console.print("[green]✓[/green] Database initialized")
    except Exception as e:
        console.print(f"[red]✗ Database error: {e}[/red]")
        return 1
    
    # Economic news sources focused on Asia and Latin America
    economic_sources = [
        # Asian Economic Sources
        "https://feeds.reuters.com/reuters/businessNews",  # Reuters Business
        "https://asia.nikkei.com/rss/feed/nar",           # Nikkei Asia
        "https://www.scmp.com/rss/91/feed",               # SCMP Business
        "https://asiatimes.com/feed/",                    # Asia Times
        
        # Latin American Economic Sources
        "https://feeds.reuters.com/reuters/worldNews",     # Reuters World (includes LatAm)
        "https://www.americasquarterly.org/fullfeed/",     # Americas Quarterly
        "https://www.cfr.org/feeds/blog.xml",             # Council on Foreign Relations
        
        # General Economic Sources with Regional Coverage
        "https://feeds.bloomberg.com/markets/news.rss",   # Bloomberg Markets
        "https://feeds.feedburner.com/economist-economics", # The Economist Economics
    ]
    
    console.print(f"[cyan]Crawling {len(economic_sources)} economic news sources...[/cyan]")
    console.print("[dim]Focus: Asian and Latin American markets[/dim]")
    
    # Economic news crawl configuration
    crawl_config = {
        'USER_AGENT': 'SpigaMonde Economic News Aggregator 1.0',
        'ROBOTSTXT_OBEY': True,
        'DOWNLOAD_DELAY': 1.5,  # Respectful crawling for financial sites
        'CONCURRENT_REQUESTS': 2,
        'DEPTH_LIMIT': 1,  # Focus on RSS feeds and main pages
        'CLOSESPIDER_PAGECOUNT': 50,
        'LOG_LEVEL': 'INFO',
        'ALLOWED_FILE_TYPES': ['xml', 'rss', 'html'],
    }
    
    # Execute crawl
    try:
        process = CrawlerProcess(crawl_config)
        process.crawl(ContentSpider, start_urls=economic_sources)
        process.start()
        
        console.print("[green]✓ Economic news sources crawled![/green]")
        
    except Exception as e:
        console.print(f"[red]✗ Crawl error: {e}[/red]")
        return 1
    
    # Extract economic headlines and generate dashboard
    console.print("\n[bold cyan]Extracting economic headlines...[/bold cyan]")
    headlines = extract_economic_headlines(console)
    
    if headlines:
        html_file = generate_economic_dashboard(headlines, console)
        console.print(f"[green]✓ Economic dashboard created: {html_file}[/green]")
        console.print(f"[cyan]Open in browser: file://{html_file.absolute()}[/cyan]")
        
        # Show summary
        show_economic_summary(headlines, console)
    else:
        console.print("[yellow]No economic headlines extracted.[/yellow]")
    
    return 0


def extract_economic_headlines(console):
    """Extract economic headlines with regional focus."""
    headlines = []
    
    # Economic keywords for filtering
    economic_keywords = [
        'economy', 'economic', 'market', 'trade', 'business', 'financial', 'finance',
        'gdp', 'inflation', 'investment', 'banking', 'currency', 'export', 'import',
        'stock', 'bond', 'commodity', 'oil', 'gold', 'dollar', 'yuan', 'peso',
        'central bank', 'monetary', 'fiscal', 'recession', 'growth', 'development'
    ]
    
    # Regional keywords for Asia and Latin America
    regional_keywords = [
        # Asia
        'asia', 'asian', 'china', 'chinese', 'japan', 'japanese', 'korea', 'korean',
        'india', 'indian', 'singapore', 'hong kong', 'taiwan', 'thailand', 'vietnam',
        'indonesia', 'malaysia', 'philippines', 'myanmar', 'cambodia', 'laos',
        
        # Latin America
        'latin america', 'south america', 'brazil', 'brazilian', 'mexico', 'mexican',
        'argentina', 'chile', 'colombia', 'peru', 'venezuela', 'ecuador', 'bolivia',
        'uruguay', 'paraguay', 'guatemala', 'costa rica', 'panama', 'nicaragua',
        'honduras', 'el salvador', 'caribbean'
    ]
    
    with session_scope() as session:
        crawled_content = session.query(Content).all()
        
        console.print(f"Processing {len(crawled_content)} crawled items...")
        
        for content in crawled_content:
            if content.local_path and os.path.exists(content.local_path):
                try:
                    with open(content.local_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content_text = f.read()
                    
                    # Extract headlines
                    if is_rss_content(content_text, content.url):
                        extracted = extract_from_rss(content_text, content.url)
                    else:
                        extracted = extract_from_html(content_text, content.url)
                    
                    # Filter for economic and regional relevance
                    for headline in extracted:
                        title_lower = headline.get('title', '').lower()
                        desc_lower = headline.get('description', '').lower()
                        combined_text = f"{title_lower} {desc_lower}"
                        
                        # Check for economic keywords
                        has_economic = any(keyword in combined_text for keyword in economic_keywords)
                        
                        # Check for regional keywords
                        has_regional = any(keyword in combined_text for keyword in regional_keywords)
                        
                        # Include if it has economic content OR is from a regional source
                        if has_economic or has_regional or is_economic_source(content.url):
                            headline['relevance_score'] = calculate_relevance_score(combined_text, economic_keywords, regional_keywords)
                            headline['region'] = detect_region(combined_text)
                            headlines.append(headline)
                        
                except Exception as e:
                    console.print(f"[yellow]Warning: Could not process {content.url}: {e}[/yellow]")
    
    # Sort by relevance score
    headlines.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
    
    console.print(f"[green]✓ Extracted {len(headlines)} relevant economic headlines[/green]")
    return headlines


def is_rss_content(content, url):
    """Check if content is RSS/XML."""
    return ('rss' in url.lower() or 
            'feed' in url.lower() or 
            '<rss' in content.lower() or 
            '<feed' in content.lower())


def is_economic_source(url):
    """Check if URL is from a known economic source."""
    economic_domains = [
        'reuters.com', 'bloomberg.com', 'nikkei.com', 'scmp.com',
        'asiatimes.com', 'americasquarterly.org', 'cfr.org',
        'economist.com', 'ft.com'
    ]
    return any(domain in url.lower() for domain in economic_domains)


def calculate_relevance_score(text, economic_keywords, regional_keywords):
    """Calculate relevance score based on keyword matches."""
    score = 0
    
    # Economic keyword matches (higher weight)
    for keyword in economic_keywords:
        if keyword in text:
            score += 2
    
    # Regional keyword matches
    for keyword in regional_keywords:
        if keyword in text:
            score += 1
    
    return score


def detect_region(text):
    """Detect which region the content is about."""
    asia_keywords = ['asia', 'china', 'japan', 'korea', 'india', 'singapore', 'hong kong', 'taiwan']
    latam_keywords = ['latin america', 'brazil', 'mexico', 'argentina', 'chile', 'colombia']
    
    asia_score = sum(1 for keyword in asia_keywords if keyword in text)
    latam_score = sum(1 for keyword in latam_keywords if keyword in text)
    
    if asia_score > latam_score:
        return 'Asia'
    elif latam_score > asia_score:
        return 'Latin America'
    else:
        return 'Global'


def extract_from_rss(rss_content, url):
    """Extract headlines from RSS content."""
    headlines = []
    source_name = get_source_name(url)
    
    # Find all RSS items
    items = re.findall(r'<item[^>]*>(.*?)</item>', rss_content, re.DOTALL | re.IGNORECASE)
    
    for item in items:
        # Extract title
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', item, re.IGNORECASE)
        if title_match:
            title = clean_html_text(title_match.group(1))
            
            # Extract other fields
            link_match = re.search(r'<link[^>]*>([^<]+)</link>', item, re.IGNORECASE)
            link = link_match.group(1) if link_match else url
            
            desc_match = re.search(r'<description[^>]*>([^<]+)</description>', item, re.IGNORECASE)
            description = clean_html_text(desc_match.group(1)) if desc_match else ""
            
            date_match = re.search(r'<pubDate[^>]*>([^<]+)</pubDate>', item, re.IGNORECASE)
            pub_date = date_match.group(1) if date_match else ""
            
            if title and len(title) > 10:
                headlines.append({
                    'title': title,
                    'link': link,
                    'description': description[:300] + "..." if len(description) > 300 else description,
                    'source': source_name,
                    'date': pub_date,
                    'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
    
    return headlines


def extract_from_html(html_content, url):
    """Extract headlines from HTML content."""
    headlines = []
    source_name = get_source_name(url)
    
    # Extract title
    title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
    if title_match:
        title = clean_html_text(title_match.group(1))
        if title and len(title) > 10:
            headlines.append({
                'title': title,
                'link': url,
                'description': "",
                'source': source_name,
                'date': "",
                'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
    
    return headlines


def get_source_name(url):
    """Extract source name from URL."""
    domain = urlparse(url).netloc.lower()
    
    source_map = {
        'feeds.reuters.com': 'Reuters',
        'asia.nikkei.com': 'Nikkei Asia',
        'scmp.com': 'South China Morning Post',
        'asiatimes.com': 'Asia Times',
        'americasquarterly.org': 'Americas Quarterly',
        'cfr.org': 'Council on Foreign Relations',
        'bloomberg.com': 'Bloomberg',
        'economist.com': 'The Economist',
    }
    
    for domain_part, name in source_map.items():
        if domain_part in domain:
            return name
    
    return domain.replace('www.', '').replace('.com', '').title()


def clean_html_text(text):
    """Clean HTML tags and entities from text."""
    if not text:
        return ""
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', '', text)
    
    # Decode common HTML entities
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    text = text.replace('&#39;', "'")
    text = text.replace('&nbsp;', ' ')
    
    return text.strip()


def show_economic_summary(headlines, console):
    """Display summary of economic headlines."""
    
    # Analyze by region
    by_region = {}
    by_source = {}
    
    for headline in headlines:
        region = headline.get('region', 'Global')
        source = headline.get('source', 'Unknown')
        
        by_region[region] = by_region.get(region, 0) + 1
        by_source[source] = by_source.get(source, 0) + 1
    
    # Display regional breakdown
    region_table = Table(title="Economic News by Region", show_header=True, header_style="bold green")
    region_table.add_column("Region", style="cyan")
    region_table.add_column("Headlines", style="yellow")
    region_table.add_column("Percentage", style="blue")
    
    total = len(headlines)
    for region, count in sorted(by_region.items(), key=lambda x: x[1], reverse=True):
        percentage = f"{count/total*100:.1f}%"
        region_table.add_row(region, str(count), percentage)
    
    console.print(region_table)
    
    # Display source breakdown
    console.print(f"\n[bold]Top Economic News Sources:[/bold]")
    for source, count in sorted(by_source.items(), key=lambda x: x[1], reverse=True)[:5]:
        console.print(f"  📰 {source}: {count} headlines")


def generate_economic_dashboard(headlines, console):
    """Generate HTML dashboard focused on economic news."""
    
    # Sort headlines by relevance score
    sorted_headlines = sorted(headlines, key=lambda x: x.get('relevance_score', 0), reverse=True)
    
    # Group by region
    by_region = {}
    for headline in headlines:
        region = headline.get('region', 'Global')
        if region not in by_region:
            by_region[region] = []
        by_region[region].append(headline)
    
    # Generate HTML with economic focus
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Economic News: Asia & Latin America</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }}
        .stat {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #28a745;
        }}
        .content-grid {{
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }}
        .headlines {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
        }}
        .headline {{
            border-bottom: 1px solid #eee;
            padding: 20px 0;
        }}
        .headline:last-child {{
            border-bottom: none;
        }}
        .headline-title {{
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }}
        .headline-meta {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 8px;
        }}
        .headline-description {{
            color: #555;
            font-size: 0.95em;
        }}
        .region-tag {{
            background: #28a745;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
        .source-tag {{
            background: #6c757d;
            color: white;
            padding: 3px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin-right: 10px;
        }}
        .relevance-score {{
            background: #ffc107;
            color: #212529;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: bold;
        }}
        .sidebar {{
            display: flex;
            flex-direction: column;
            gap: 20px;
        }}
        .sidebar-section {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .region-list {{
            list-style: none;
            padding: 0;
        }}
        .region-item {{
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 Economic News Dashboard</h1>
        <h2>Asia & Latin America</h2>
        <p>Business and Financial Market Coverage</p>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <div class="stat-number">{len(headlines)}</div>
            <div>Economic Headlines</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(by_region)}</div>
            <div>Regions Covered</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len(set(h.get('source', 'Unknown') for h in headlines))}</div>
            <div>News Sources</div>
        </div>
        <div class="stat">
            <div class="stat-number">{len([h for h in headlines if h.get('relevance_score', 0) > 3])}</div>
            <div>High Relevance</div>
        </div>
    </div>
    
    <div class="content-grid">
        <div class="headlines">
            <h2>🔥 Top Economic Headlines</h2>
"""
    
    # Add headlines
    for headline in sorted_headlines[:30]:  # Show top 30
        title = headline.get('title', 'No title')
        source = headline.get('source', 'Unknown')
        region = headline.get('region', 'Global')
        description = headline.get('description', '')
        link = headline.get('link', '#')
        relevance = headline.get('relevance_score', 0)
        
        html_content += f"""
        <div class="headline">
            <div class="headline-title">
                <a href="{link}" target="_blank" style="text-decoration: none; color: inherit;">
                    {title}
                </a>
            </div>
            <div class="headline-meta">
                <span class="region-tag">{region}</span>
                <span class="source-tag">{source}</span>
                <span class="relevance-score">Score: {relevance}</span>
            </div>
            {f'<div class="headline-description">{description}</div>' if description else ''}
        </div>
        """
    
    # Add sidebar
    html_content += """
        </div>
        
        <div class="sidebar">
            <div class="sidebar-section">
                <h3>🌏 Regional Coverage</h3>
                <ul class="region-list">
"""
    
    for region, region_headlines in sorted(by_region.items(), key=lambda x: len(x[1]), reverse=True):
        html_content += f"""
                <li class="region-item">
                    <span>{region}</span>
                    <span>{len(region_headlines)}</span>
                </li>
        """
    
    html_content += """
                </ul>
            </div>
            
            <div class="sidebar-section">
                <h3>📊 Market Focus</h3>
                <p>This dashboard aggregates economic news specifically relevant to:</p>
                <ul>
                    <li>Asian markets and economies</li>
                    <li>Latin American business news</li>
                    <li>Regional trade and investment</li>
                    <li>Currency and commodity markets</li>
                    <li>Economic policy and development</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>🔄 Refresh for latest economic news</p>
        <p>Powered by SpigaMonde Economic News Aggregator</p>
    </div>
</body>
</html>
"""
    
    # Write HTML file
    output_dir = Path(__file__).parent / 'output'
    output_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    html_file = output_dir / f'economic_news_{timestamp}.html'
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # Also create a latest economic news file
    latest_file = output_dir / 'latest_economic_news.html'
    with open(latest_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return html_file


if __name__ == "__main__":
    sys.exit(main())
