"""Content and URL models for storing scraped data."""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>, DateTime, Enum as SQLEnum, Float, Integer, String, Text,
    UniqueConstraint, Index, ForeignKey
)
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class ContentStatus(str, Enum):
    """Status of content processing."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ContentType(str, Enum):
    """Type of content found."""
    DOCUMENT = "document"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    ARCHIVE = "archive"
    WEB_PAGE = "web_page"
    DATA_FILE = "data_file"
    OTHER = "other"


class URL(BaseModel):
    """Model for storing URLs to be crawled or that have been crawled."""
    
    __tablename__ = "urls"
    
    # URL information
    url: Mapped[str] = mapped_column(String(2048), nullable=False, unique=True)
    domain: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    path: Mapped[str] = mapped_column(String(1024), nullable=True)
    
    # Crawling metadata
    depth: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    status_code: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    last_crawled: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    crawl_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Status and flags
    is_crawled: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_seed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_blocked: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Error tracking
    last_error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Relationships
    content_items = relationship("Content", back_populates="source_url", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index("idx_url_domain_crawled", "domain", "is_crawled"),
        Index("idx_url_status_depth", "is_crawled", "depth"),
    )
    
    def __repr__(self) -> str:
        return f"<URL(id={self.id}, url='{self.url[:50]}...', crawled={self.is_crawled})>"


class Content(BaseModel):
    """Model for storing discovered content and files."""
    
    __tablename__ = "content"
    
    # Basic content information
    title: Mapped[Optional[str]] = mapped_column(String(512), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    url: Mapped[str] = mapped_column(String(2048), nullable=False)
    
    # File information
    filename: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    file_extension: Mapped[Optional[str]] = mapped_column(String(10), nullable=True, index=True)
    file_size: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    mime_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Content classification
    content_type: Mapped[ContentType] = mapped_column(
        SQLEnum(ContentType), 
        default=ContentType.OTHER, 
        nullable=False,
        index=True
    )
    
    # Processing status
    status: Mapped[ContentStatus] = mapped_column(
        SQLEnum(ContentStatus), 
        default=ContentStatus.PENDING, 
        nullable=False,
        index=True
    )
    
    # Content hashes for deduplication
    content_hash: Mapped[Optional[str]] = mapped_column(String(64), nullable=True, index=True)
    url_hash: Mapped[str] = mapped_column(String(64), nullable=False, index=True)
    
    # Storage information
    local_path: Mapped[Optional[str]] = mapped_column(String(1024), nullable=True)
    is_downloaded: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Metadata
    language: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    encoding: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Quality and relevance scoring (for future AI integration)
    quality_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    relevance_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Error tracking
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    retry_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Foreign keys
    source_url_id: Mapped[int] = mapped_column(Integer, ForeignKey("urls.id"), nullable=False, index=True)
    
    # Relationships
    source_url = relationship("URL", back_populates="content_items")
    analysis: Mapped[Optional["ContentAnalysis"]] = relationship("ContentAnalysis", back_populates="content", uselist=False)
    
    # Constraints and indexes
    __table_args__ = (
        UniqueConstraint("url_hash", name="uq_content_url_hash"),
        Index("idx_content_type_status", "content_type", "status"),
        Index("idx_content_downloaded", "is_downloaded"),
        Index("idx_content_hash", "content_hash"),
        Index("idx_content_size", "file_size"),
    )
    
    def __repr__(self) -> str:
        return f"<Content(id={self.id}, filename='{self.filename}', type={self.content_type.value})>"


class CrawlSession(BaseModel):
    """Model for tracking crawl sessions and statistics."""
    
    __tablename__ = "crawl_sessions"
    
    # Session information
    session_name: Mapped[str] = mapped_column(String(255), nullable=False)
    start_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    end_time: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Session configuration
    seed_urls: Mapped[str] = mapped_column(Text, nullable=False)  # JSON array of URLs
    max_depth: Mapped[int] = mapped_column(Integer, nullable=False)
    max_pages: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Statistics
    urls_discovered: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    urls_crawled: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    content_found: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    content_downloaded: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    total_size_bytes: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_completed: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    
    # Error tracking
    error_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    last_error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    def __repr__(self) -> str:
        return f"<CrawlSession(id={self.id}, name='{self.session_name}', active={self.is_active})>"


class ContentCategory(str, Enum):
    """Enhanced content categories for classification."""
    ACADEMIC_PAPER = "academic_paper"
    TECHNICAL_DOCUMENTATION = "technical_documentation"
    LEGAL_DOCUMENT = "legal_document"
    FINANCIAL_REPORT = "financial_report"
    NEWS_ARTICLE = "news_article"
    BLOG_POST = "blog_post"
    EDUCATIONAL_VIDEO = "educational_video"
    ENTERTAINMENT_VIDEO = "entertainment_video"
    PODCAST = "podcast"
    MUSIC = "music"
    ARTWORK = "artwork"
    PHOTOGRAPHY = "photography"
    DATASET = "dataset"
    API_DOCUMENTATION = "api_documentation"
    CODE_REPOSITORY = "code_repository"
    CONFIGURATION_FILE = "configuration_file"
    LANDING_PAGE = "landing_page"
    PRODUCT_PAGE = "product_page"
    CONTACT_INFO = "contact_info"
    ABOUT_PAGE = "about_page"
    UNKNOWN = "unknown"
    LOW_QUALITY = "low_quality"


class QualityScore(int, Enum):
    """Content quality scoring levels."""
    EXCELLENT = 5
    GOOD = 4
    AVERAGE = 3
    POOR = 2
    VERY_POOR = 1


class ContentAnalysis(BaseModel):
    """Content analysis results and metadata."""
    __tablename__ = "content_analysis"

    # Foreign key to content
    content_id: Mapped[int] = mapped_column(Integer, ForeignKey("content.id"), nullable=False, unique=True)

    # Classification results
    category: Mapped[ContentCategory] = mapped_column(SQLEnum(ContentCategory), nullable=False, default=ContentCategory.UNKNOWN)
    confidence: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    quality_score: Mapped[QualityScore] = mapped_column(SQLEnum(QualityScore), nullable=False, default=QualityScore.AVERAGE)

    # Content metadata
    title: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    author: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    language: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)

    # Content statistics
    word_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    character_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    line_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    paragraph_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)

    # Quality metrics
    readability_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    complexity_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)

    # Hashes for deduplication
    content_hash: Mapped[Optional[str]] = mapped_column(String(64), nullable=True, index=True)
    similarity_hash: Mapped[Optional[str]] = mapped_column(String(32), nullable=True, index=True)

    # Extracted entities (JSON stored as text)
    emails: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array
    urls: Mapped[Optional[str]] = mapped_column(Text, nullable=True)    # JSON array
    phone_numbers: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array
    dates: Mapped[Optional[str]] = mapped_column(Text, nullable=True)   # JSON array
    keywords: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array
    topics: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array

    # Analysis metadata
    analysis_time: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    analysis_success: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Relationships
    content: Mapped["Content"] = relationship("Content", back_populates="analysis")

    def __repr__(self) -> str:
        return f"<ContentAnalysis(id={self.id}, category={self.category.value}, quality={self.quality_score.value})>"
