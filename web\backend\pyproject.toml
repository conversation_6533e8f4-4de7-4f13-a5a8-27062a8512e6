[project]
name = "spigamonde-web-backend"
version = "0.1.0"
description = "SpigaMonde Web Interface Backend"
requires-python = ">=3.9"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "python-dateutil>=2.8.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "httpx>=0.25.0",  # For testing FastAPI
]
